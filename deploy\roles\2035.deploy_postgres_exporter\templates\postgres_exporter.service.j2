{{ ansible_managed | comment }}

[Unit]
Description=postgres_exporter
After=network-online.target

[Service]
Type=simple
User=postgres_exporter
Group=postgres_exporter
Environment="DATA_SOURCE_NAME=*******************************************************************"
ExecReload=/bin/kill -HUP $MAINPID
ExecStart=/usr/local/bin/postgres_exporter --web.listen-address=":9188"
LimitNOFILE=65000
Restart=always

[Install]
WantedBy=multi-user.target
