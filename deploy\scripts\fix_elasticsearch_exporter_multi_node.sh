#!/bin/bash

# 修复Elasticsearch Exporter多节点配置问题
# 问题：多个--es.uri参数导致启动失败

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 检查当前问题
check_current_issue() {
    log_header "检查当前问题"
    
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        log_info "检查 $host..."
        
        # 检查服务状态
        local status=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-active elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
        echo "  服务状态: $status"
        
        if [ "$status" != "active" ]; then
            # 检查服务文件内容
            log_info "  检查服务文件配置..."
            local service_content=$(ansible -i hosts.ini "$host" -m shell -a "cat /etc/systemd/system/elasticsearch_exporter.service | grep ExecStart" --become 2>/dev/null | grep -v "SUCCESS" || echo "")
            
            if echo "$service_content" | grep -q "es.uri.*es.uri"; then
                log_error "  ✗ 发现重复的--es.uri参数"
                echo "    $service_content"
            else
                log_info "  ✓ 服务文件配置正常"
            fi
            
            # 检查最近的错误日志
            log_info "  最近的错误日志:"
            ansible -i hosts.ini "$host" -m shell -a "journalctl -u elasticsearch_exporter --since '10 minutes ago' --no-pager -n 5" --become 2>/dev/null | grep -v "SUCCESS" | head -10
        else
            log_info "  ✓ 服务运行正常"
        fi
        
        echo ""
    done
}

# 修复配置
fix_configuration() {
    log_header "修复多节点配置"
    
    # 重新部署配置角色
    log_info "重新部署配置角色..."
    ansible-playbook -i hosts.ini 2029.elasticsearch_exporter.playbook.yml --tags config
    
    if [ $? -eq 0 ]; then
        log_info "✓ 配置部署成功"
    else
        log_error "✗ 配置部署失败"
        return 1
    fi
}

# 重启服务
restart_services() {
    log_header "重启Elasticsearch Exporter服务"
    
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        log_info "重启 $host 上的服务..."
        
        # 停止服务
        ansible -i hosts.ini "$host" -m systemd -a "name=elasticsearch_exporter state=stopped" --become
        
        # 重载systemd配置
        ansible -i hosts.ini "$host" -m systemd -a "daemon_reload=yes" --become
        
        # 启动服务
        ansible -i hosts.ini "$host" -m systemd -a "name=elasticsearch_exporter state=started enabled=yes" --become
        
        # 等待服务启动
        sleep 5
        
        # 检查服务状态
        local status=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-active elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
        
        if [ "$status" = "active" ]; then
            log_info "  ✓ $host 服务启动成功"
        else
            log_error "  ✗ $host 服务启动失败"
            
            # 显示详细错误信息
            log_info "  服务状态详情:"
            ansible -i hosts.ini "$host" -m shell -a "systemctl status elasticsearch_exporter --no-pager -l" --become 2>/dev/null | grep -v "SUCCESS" | head -15
            
            log_info "  最近日志:"
            ansible -i hosts.ini "$host" -m shell -a "journalctl -u elasticsearch_exporter --no-pager -n 10" --become 2>/dev/null | grep -v "SUCCESS"
        fi
        
        echo ""
    done
}

# 验证修复结果
verify_fix() {
    log_header "验证修复结果"
    
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    local all_ok=true
    
    for host in "${hosts[@]}"; do
        log_info "验证 $host..."
        
        # 检查服务状态
        local status=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-active elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
        
        if [ "$status" = "active" ]; then
            log_info "  ✓ 服务运行正常"
            
            # 检查进程
            local process_count=$(ansible -i hosts.ini "$host" -m shell -a "pgrep -f elasticsearch_exporter | wc -l" --become 2>/dev/null | grep -v "SUCCESS" || echo "0")
            echo "    进程数: $process_count"
            
            # 检查端口监听
            local port_check=$(ansible -i hosts.ini "$host" -m shell -a "netstat -tlnp | grep :9114" --become 2>/dev/null | grep -v "SUCCESS" | wc -l || echo "0")
            
            if [ "$port_check" -gt 0 ]; then
                log_info "  ✓ 端口9114监听正常"
            else
                log_warn "  ⚠ 端口9114未监听"
                all_ok=false
            fi
            
            # 检查指标端点
            sleep 5  # 等待服务完全启动
            local metrics_check=$(ansible -i hosts.ini "$host" -m uri -a "url=http://localhost:9114/metrics timeout=10" 2>/dev/null | grep -c "status.*200" || echo "0")
            
            if [ "$metrics_check" -gt 0 ]; then
                log_info "  ✓ 指标端点正常"
                
                # 检查ES连接状态
                local es_up=$(ansible -i hosts.ini "$host" -m shell -a "curl -s http://localhost:9114/metrics | grep 'elasticsearch_up ' | head -1" 2>/dev/null | grep -v "SUCCESS" || echo "")
                if [ -n "$es_up" ]; then
                    echo "    ES连接: $es_up"
                fi
            else
                log_warn "  ⚠ 指标端点异常"
                all_ok=false
                
                # 显示详细错误信息
                ansible -i hosts.ini "$host" -m shell -a "curl -v http://localhost:9114/metrics" --become 2>/dev/null | grep -v "SUCCESS" | head -10
            fi
        else
            log_error "  ✗ 服务未运行: $status"
            all_ok=false
        fi
        
        echo ""
    done
    
    if [ "$all_ok" = true ]; then
        log_info "✓ 所有服务验证通过"
    else
        log_warn "⚠ 部分服务存在问题"
    fi
}

# 显示服务配置
show_service_config() {
    log_header "当前服务配置"
    
    log_info "查看修复后的服务文件..."
    
    # 显示一个主机的服务文件内容
    ansible -i hosts.ini "SZ-ES-001" -m shell -a "cat /etc/systemd/system/elasticsearch_exporter.service" --become 2>/dev/null | grep -v "SUCCESS" | head -20
    
    echo ""
    log_info "ExecStart行详情:"
    ansible -i hosts.ini "SZ-ES-001" -m shell -a "grep 'ExecStart' /etc/systemd/system/elasticsearch_exporter.service" --become 2>/dev/null | grep -v "SUCCESS"
}

# 测试ES集群连接
test_es_cluster() {
    log_header "测试Elasticsearch集群连接"
    
    local es_nodes=("*************:9200" "*************:9200" "*************:9200")
    
    for node in "${es_nodes[@]}"; do
        log_info "测试 $node..."
        
        if curl -s --connect-timeout 5 "http://$node" > /dev/null; then
            log_info "  ✓ 连接正常"
            
            # 获取集群健康状态
            local health=$(curl -s "http://$node/_cluster/health" | jq -r '.status' 2>/dev/null || echo "unknown")
            echo "    集群状态: $health"
        else
            log_warn "  ⚠ 连接失败"
        fi
    done
}

# 主函数
main() {
    echo "========================================"
    echo "修复Elasticsearch Exporter多节点配置"
    echo "修复时间: $(date)"
    echo "========================================"
    echo ""
    
    # 检查前置条件
    if [ ! -f "hosts.ini" ]; then
        log_error "hosts.ini 文件不存在"
        exit 1
    fi
    
    if ! command -v ansible-playbook &> /dev/null; then
        log_error "ansible-playbook 未安装"
        exit 1
    fi
    
    # 执行修复步骤
    check_current_issue
    test_es_cluster
    fix_configuration
    restart_services
    verify_fix
    show_service_config
    
    log_info "修复完成！"
    echo ""
    echo "验证命令:"
    echo "  # 检查服务状态"
    echo "  ansible -i hosts.ini 'SZ-ES-001' -m shell -a 'systemctl status elasticsearch_exporter' --become"
    echo ""
    echo "  # 检查指标"
    echo "  curl http://SZ-ES-001:9114/metrics | grep elasticsearch_up"
    echo ""
    echo "  # 检查ES连接"
    echo "  curl http://SZ-ES-001:9114/metrics | grep elasticsearch_cluster_health_status"
    echo ""
    echo "如果问题仍然存在，请检查:"
    echo "1. Elasticsearch集群是否正常运行"
    echo "2. 网络连接是否正常"
    echo "3. elasticsearch_exporter版本是否兼容"
}

# 执行主函数
main "$@"
