#!/bin/bash

# API性能监控配置脚本
# 用法: ./setup_api_monitoring.sh [log_format] [log_path]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "API性能监控配置脚本"
    echo ""
    echo "用法: $0 [log_format] [log_path]"
    echo ""
    echo "支持的日志格式:"
    echo "  standard  - 标准格式: '接口: /api/path 耗时: 1200ms'"
    echo "  json      - JSON格式: '{\"api_path\":\"/api/path\",\"response_time\":1200}'"
    echo "  nginx     - Nginx访问日志格式"
    echo "  custom    - 自定义格式 (最后一列为耗时)"
    echo ""
    echo "示例:"
    echo "  $0 standard /var/log/application/*.log"
    echo "  $0 json /var/log/api/*.json"
    echo "  $0 nginx /var/log/nginx/access.log"
    echo ""
}

# 检测日志格式
detect_log_format() {
    local log_file="$1"
    
    if [ ! -f "$log_file" ]; then
        log_warn "日志文件不存在: $log_file"
        return 1
    fi
    
    log_info "分析日志格式: $log_file"
    
    # 读取前几行进行格式检测
    local sample_lines=$(head -n 10 "$log_file")
    
    # 检测JSON格式
    if echo "$sample_lines" | grep -q '{".*response_time.*"}'; then
        echo "json"
        return 0
    fi
    
    # 检测Nginx格式
    if echo "$sample_lines" | grep -qE '^\S+ - - \[.*\] "\w+ .* HTTP/.*" \d+ \d+ ".*" ".*" \d+(\.\d+)?'; then
        echo "nginx"
        return 0
    fi
    
    # 检测标准格式
    if echo "$sample_lines" | grep -qE '接口[:：].*耗时[:：].*[0-9]+(\.[0-9]+)?\s*(ms|毫秒|s|秒)'; then
        echo "standard"
        return 0
    fi
    
    # 默认为自定义格式
    echo "custom"
    return 0
}

# 生成Promtail配置
generate_promtail_config() {
    local format="$1"
    local log_path="$2"
    local config_file="/tmp/api_monitoring_config.yml"
    
    log_info "生成Promtail配置: $format 格式"
    
    case "$format" in
        "standard")
            cat > "$config_file" << EOF
# API性能监控 - 标准格式
- job_name: api_performance_monitoring
  static_configs:
  - targets:
    - localhost
    labels:
      job: api-performance
      __path__: $log_path
  pipeline_stages:
  - regex:
      expression: '.*接口[:：]\s*(?P<api_path>[^\s]+).*耗时[:：]\s*(?P<response_time>\d+(?:\.\d+)?)\s*(?P<time_unit>ms|毫秒|s|秒).*'
  - template:
      source: response_time_ms
      template: |
        {{- if eq .time_unit "s" -}}
          {{ mul .response_time 1000 }}
        {{- else if eq .time_unit "秒" -}}
          {{ mul .response_time 1000 }}
        {{- else -}}
          {{ .response_time }}
        {{- end -}}
  - labels:
      api_path: api_path
  - metrics:
      api_response_time_ms:
        type: Histogram
        description: API response time in milliseconds
        source: response_time_ms
        config:
          buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000]
  - match:
      selector: '{job="api-performance"}'
      stages:
      - template:
          source: is_slow
          template: |
            {{- if gt (float64 .response_time_ms) 1000.0 -}}
              1
            {{- else -}}
              0
            {{- end -}}
      - metrics:
          api_slow_requests_total:
            type: Counter
            description: Total slow API requests (>1s)
            source: is_slow
            config:
              value: "1"
              action: inc
EOF
            ;;
        "json")
            cat > "$config_file" << EOF
# API性能监控 - JSON格式
- job_name: api_performance_monitoring
  static_configs:
  - targets:
    - localhost
    labels:
      job: api-performance-json
      __path__: $log_path
  pipeline_stages:
  - json:
      expressions:
        api_path: api_path
        response_time: response_time
        method: method
        status_code: status_code
  - labels:
      api_path: api_path
      method: method
  - metrics:
      api_response_time_ms:
        type: Histogram
        description: API response time from JSON
        source: response_time
        config:
          buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000]
  - match:
      selector: '{job="api-performance-json"}'
      stages:
      - template:
          source: is_slow
          template: |
            {{- if gt (float64 .response_time) 1000.0 -}}
              1
            {{- else -}}
              0
            {{- end -}}
      - metrics:
          api_slow_requests_total:
            type: Counter
            description: Total slow API requests
            source: is_slow
            config:
              value: "1"
              action: inc
EOF
            ;;
        "nginx")
            cat > "$config_file" << EOF
# API性能监控 - Nginx格式
- job_name: api_performance_monitoring
  static_configs:
  - targets:
    - localhost
    labels:
      job: api-performance-nginx
      __path__: $log_path
  pipeline_stages:
  - regex:
      expression: '^(?P<remote_addr>[^\s]+) - - \[(?P<timestamp>[^\]]+)\] "(?P<method>\w+) (?P<api_path>[^\s]+) HTTP/[^"]*" (?P<status_code>\d+) (?P<body_bytes_sent>\d+) "[^"]*" "[^"]*" (?P<response_time>\d+(?:\.\d+)?)'
  - labels:
      api_path: api_path
      method: method
      status_code: status_code
  - template:
      source: response_time_ms
      template: "{{ mul .response_time 1000 }}"
  - metrics:
      api_response_time_ms:
        type: Histogram
        description: Nginx API response time
        source: response_time_ms
        config:
          buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000]
  - match:
      selector: '{job="api-performance-nginx"} |~ "\"\\w+ /api/"'
      stages:
      - template:
          source: is_slow
          template: |
            {{- if gt (float64 .response_time_ms) 1000.0 -}}
              1
            {{- else -}}
              0
            {{- end -}}
      - metrics:
          api_slow_requests_total:
            type: Counter
            description: Nginx slow API requests
            source: is_slow
            config:
              value: "1"
              action: inc
EOF
            ;;
        "custom")
            cat > "$config_file" << EOF
# API性能监控 - 自定义格式 (最后一列为耗时)
- job_name: api_performance_monitoring
  static_configs:
  - targets:
    - localhost
    labels:
      job: api-performance-custom
      __path__: $log_path
  pipeline_stages:
  - regex:
      expression: '^.*\s+(?P<api_path>/[^\s]*)\s+.*\s+(?P<response_time>\d+(?:\.\d+)?)(?:ms)?\s*$'
  - labels:
      api_path: api_path
  - metrics:
      api_response_time_ms:
        type: Histogram
        description: Custom format API response time
        source: response_time
        config:
          buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000]
  - match:
      selector: '{job="api-performance-custom"}'
      stages:
      - template:
          source: is_slow
          template: |
            {{- if gt (float64 .response_time) 1000.0 -}}
              1
            {{- else -}}
              0
            {{- end -}}
      - metrics:
          api_slow_requests_total:
            type: Counter
            description: Custom format slow requests
            source: is_slow
            config:
              value: "1"
              action: inc
EOF
            ;;
        *)
            log_error "不支持的日志格式: $format"
            return 1
            ;;
    esac
    
    echo "$config_file"
}

# 部署配置
deploy_config() {
    local config_file="$1"
    
    log_info "部署API监控配置..."
    
    # 更新Promtail配置
    ansible-playbook -i hosts.ini 2003.promtail.playbook.yml \
        --extra-vars "api_monitoring_config=$config_file" \
        --tags config
    
    # 重载Prometheus配置
    ansible-playbook -i hosts.ini 2005.prometheus.playbook.yml \
        --tags config
    
    log_info "配置部署完成"
}

# 验证配置
verify_config() {
    log_info "验证API监控配置..."
    
    # 检查Promtail指标
    local promtail_hosts=("SZ-ES-001:19080" "SZ-MONT-002:19080" "HK-MG-002:19080")
    
    for host in "${promtail_hosts[@]}"; do
        if curl -s "http://$host/metrics" | grep -q "api_response_time_ms"; then
            log_info "✓ $host - API监控指标正常"
        else
            log_warn "⚠ $host - 未检测到API监控指标"
        fi
    done
    
    # 检查Prometheus规则
    log_info "检查Prometheus告警规则..."
    # 这里可以添加更多验证逻辑
}

# 主函数
main() {
    local log_format="${1:-}"
    local log_path="${2:-}"
    
    if [ -z "$log_format" ] || [ -z "$log_path" ]; then
        show_usage
        exit 1
    fi
    
    log_header "API性能监控配置"
    echo "日志格式: $log_format"
    echo "日志路径: $log_path"
    echo ""
    
    # 如果是auto，则自动检测格式
    if [ "$log_format" = "auto" ]; then
        log_format=$(detect_log_format "$log_path")
        log_info "检测到日志格式: $log_format"
    fi
    
    # 生成配置
    local config_file=$(generate_promtail_config "$log_format" "$log_path")
    
    if [ $? -eq 0 ]; then
        log_info "配置文件生成成功: $config_file"
        
        # 显示配置预览
        log_info "配置预览:"
        echo "----------------------------------------"
        head -n 20 "$config_file"
        echo "----------------------------------------"
        
        # 询问是否部署
        read -p "是否部署此配置? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            deploy_config "$config_file"
            verify_config
            
            log_info "API性能监控配置完成！"
            echo ""
            echo "监控指标:"
            echo "  - api_response_time_ms: API响应时间直方图"
            echo "  - api_slow_requests_total: 慢请求计数器"
            echo ""
            echo "告警规则:"
            echo "  - SlowAPIRequests: 检测到慢请求"
            echo "  - HighSlowAPIRequestRate: 慢请求频率过高"
            echo "  - HighAPIResponseTimeP95: P95响应时间过高"
        else
            log_info "配置已生成但未部署: $config_file"
        fi
    else
        log_error "配置生成失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
