---
- name: create blackbox_exporter system group
  group:
    name: blackbox_exporter
    system: true
    state: present
    gid: 3240

- name: create blackbox_exporter system user
  user:
    name: blackbox_exporter
    system: true
    shell: "/usr/sbin/nologin"
    group: blackbox_exporter
    createhome: false
    home: "{{ BLACKBOX_EXPORTER_DB_DIR }}"
    uid: 3240

- name: create blackbox_exporter data directory
  file:
    path: "{{ BLACKBOX_EXPORTER_DB_DIR }}"
    state: directory
    recurse: true
    owner: blackbox_exporter
    group: blackbox_exporter
    mode: 0755

- name: create blackbox_exporter configuration directories
  file:
    path: "{{ item }}"
    state: directory
    owner: root
    group: blackbox_exporter
    mode: 0770
  with_items:
  - "{{ BLACKBOX_EXPORTER_CONFIG_DIR }}"

- name: blackbox_exporter url
  debug:
    msg: "{{BLACKBOX_EXPORTER_DOWNLOAD_URL}}"

- name: download blackbox_exporter binary to local folder
  become: false
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "{{BLACKBOX_EXPORTER_DOWNLOAD_URL}}"
    dest: "/tmp/blackbox_exporter-{{BLACKBOX_EXPORTER_VERSION}}.tar.gz"
    checksum: "{{BLACKBOX_EXPORTER_CHECKSUM}}"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: unpack blackbox_exporter binaries
  unarchive:
    src: "/tmp/blackbox_exporter-{{BLACKBOX_EXPORTER_VERSION}}.tar.gz"
    dest: "/tmp"
    remote_src: true
  check_mode: false

- name: copy official blackbox_exporter binaries
  copy:
    src: "/tmp/blackbox_exporter-{{BLACKBOX_EXPORTER_VERSION}}.linux-amd64/{{item}}"
    dest: "/usr/local/bin/{{item}}"
    mode: '755'
    owner: root
    group: root
    remote_src: true
  with_items:
  - blackbox_exporter
  notify:
  - restart blackbox_exporter

- name: create systemd service unit
  template:
    src: blackbox_exporter.service.j2
    dest: /etc/systemd/system/blackbox_exporter.service
    owner: root
    group: root
    mode: 0644
  notify:
  - restart blackbox_exporter

- name: enabled blackbox_exporter systemd
  systemd:
    name: blackbox_exporter
    daemon-reload: true
    enabled: true
