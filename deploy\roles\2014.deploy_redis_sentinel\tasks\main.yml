---
#- name: install debian dependencies
#  apt:
#    pkg:
#      - gcc
#      - make
#      - libc6-dev
#      - "{{ 'libc6-dev-i386' if redis_make_32bit|bool else 'gcc' }}"
#    update_cache: yes
#    state: present
#  when: ansible_os_family == "Debian"

- name: apt install redis
  apt:
    name:
    - redis
    - redis-sentinel
    state: present
    update_cache: true

- name: Ensure redis-server started
  systemd:
    daemon_reload: yes
    enabled: yes
    name: redis-server
    state: started

- name: apt install redis-sentinel
  apt:
    name:
    - redis-sentinel
    state: present
    update_cache: true
  when: IF_SENTINEL is defined and IF_SENTINEL

- name: Ensure redis-sentinel started
  systemd:
    daemon_reload: yes
    enabled: yes
    name: redis-sentinel
    state: started
  when: IF_SENTINEL is defined and IF_SENTINEL