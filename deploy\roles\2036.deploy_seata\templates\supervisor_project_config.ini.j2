[program:seata]
directory = /etc/seata/seata-server-{{seata_version}}/bin
command = sh seata-server.sh -p 8091 -h 127.0.0.1 -m db
autorestart = true
startretries = 100
user = {{ start_user }}
killasgroup = true
stopasgroup = true
startsecs = 10
environment = LANG="en_US.UTF-8"

stdout_logfile_maxbytes = 100MB
stdout_logfile = /var/log/supervisor/%(program_name)s.log
stdout_logfile_backups = 1
stderr_logfile_maxbytes = 100MB
stderr_logfile = /var/log/supervisor/%(program_name)s.error.log
stderr_logfile_backups = 1
redirect_stderr = true
