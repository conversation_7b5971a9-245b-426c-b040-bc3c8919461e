---
- name: <PERSON><PERSON><PERSON>_EXPORTER | Ensure redis_exporter group
  group:
    name: "{{ redis_exporter_group }}"
    system: yes
    state: present

- name: REDIS_EXPORTER | Ensure redis_exporter user
  user:
    name: "{{ redis_exporter_user }}"
    group: "{{ redis_exporter_group }}"
    system: yes
    shell: /usr/sbin/nologin
    createhome: no

- name: REDIS_EXPORTER | Ensure skeleton bin paths
  file:
    dest: "{{ item }}"
    owner: "{{ redis_exporter_user }}"
    group: "{{ redis_exporter_group }}"
    state: directory
  with_items:
    - "{{ redis_exporter_bin_path }}"

- name: REDIS_EXPORTER | Ensure skeleton log paths
  file:
    dest: "{{ item }}"
    owner: "{{ redis_exporter_user }}"
    group: "{{ redis_exporter_group }}"
    state: directory
  with_items:
    - "{{ redis_exporter_log_path }}"
  when: redis_exporter_log_path is defined


- name: REDIS_EXPORTER | Check redis_exporter version
  command: redis_exporter -version
  register: redis_exporter_check
  changed_when: false
  ignore_errors: true

- name: REDIS_EXPORTER | Download package
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "{{ redis_exporter_url }}"
    dest: "{{ redis_exporter_package_path }}"
  when: redis_exporter_force_reinstall or redis_exporter_check is failed or redis_exporter_version not in redis_exporter_check.stderr
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: REDIS_EXPORTER | Extract downloaded package
  unarchive:
    src: "{{ redis_exporter_package_path }}"
    dest: "{{ redis_exporter_download_path }}"
    remote_src: True
  when: redis_exporter_force_reinstall or redis_exporter_check is failed or redis_exporter_version  not in redis_exporter_check.stderr

- name: REDIS_EXPORTER | Copy binary
  copy:
    src: "{{ redis_exporter_src_bin }}"
    dest: "{{ redis_exporter_bin_path }}/redis_exporter"
    owner: "{{ redis_exporter_user }}"
    group: "{{ redis_exporter_group }}"
    remote_src: True
    mode: 0755
  when: redis_exporter_force_reinstall or redis_exporter_check is failed or redis_exporter_version not in redis_exporter_check.stderr
  notify: restart redis_exporter

- name: REDIS_EXPORTER | Link binary
  file:
    src: "{{ redis_exporter_bin_path }}/redis_exporter"
    dest: "/usr/bin/redis_exporter"
    state: link
  when: redis_exporter_force_reinstall or redis_exporter_check is failed or redis_exporter_version  not in redis_exporter_check.stderr

- name: REDIS_EXPORTER | Copy Daemon script
  template:
    src: redis_exporter.service.j2
    dest: /etc/systemd/system/redis_exporter.service
    mode: 0644
  notify: restart redis_exporter
