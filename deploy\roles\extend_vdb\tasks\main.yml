---
- block:
  - name: check Disklabel
    shell:
      cmd: fdisk -lu |grep 'Disklabel type'|awk -F':' '{print($2)}'|tr -d '\n'|awk '{print($2)}'
    register: check_disklabel
  - name: debug Disklabel stdout
    debug:
      var: check_disklabel.stdout
  when: (ansible_distribution|lower)=='ubuntu'
- block:
  - name: check Disk label
    shell:
      cmd: fdisk -lu |grep 'Disk label type'|awk -F':' '{print($2)}'|tr -d '\n'|awk '{print($2)}'
    register: check_disk_label
  - name: debug Disk label stdout
    debug:
      var: check_disk_label.stdout
  when: (ansible_distribution|lower)=='centos'
- name: check filesystem type
  shell:
    cmd: df -Th |grep /data |awk '{print $2}'
  register: check_fstype
- name: debug filesystem type stdout
  debug:
    var: check_fstype.stdout
###ubuntu
- block:
  - name: extend mbr partitions
    shell:
      cmd: apt-get update && type growpart || apt-get install -y cloud-guest-utils && LC_ALL=en_US.UTF-8 growpart /dev/vdb 1
    when: check_disklabel.stdout=='dos'
  - name: extend GPT partitions
    shell:
      cmd: apt-get update && type growpart || apt-get install -y cloud-guest-utils && type sgdisk || apt-get install -y gdisk && LC_ALL=en_US.UTF-8 growpart /dev/vdb 1 
    when: check_disklabel.stdout=='gpt'     
  when: (ansible_distribution|lower)=='ubuntu'
###centos
- block:
  - name: extend mbr partitions
    shell:
      cmd: type growpart || yum install -y cloud-utils-growpart && LC_ALL=en_US.UTF-8 growpart /dev/vdb 1
    when: check_disk_label.stdout=='dos'
  - name: extend GPT partitions
    shell:
      cmd: type growpart || yum install -y cloud-utils-growpart && type sgdisk || yum install -y gdisk && LC_ALL=en_US.UTF-8 growpart /dev/vdb 1 
    when: check_disk_label.stdout=='gpt'     
  when: (ansible_distribution|lower)=='centos'
###ext file system
- name: extend ext file system
  shell:
    cmd: resize2fs /dev/vdb1
  when: check_fstype.stdout.startswith('ext')
###XFS file system
- name: extend XFS file system
  shell:
    cmd: type xfs_growfs || yum install -y xfsprogs && xfs_growfs /data
  when: check_fstype.stdout=='xfs'
- name: check filesystem extend result
  shell:
    cmd: df -Th
  register: check_fs_extend
- name: debug filesystem extend result
  debug:
    var: check_fs_extend.stdout_lines
