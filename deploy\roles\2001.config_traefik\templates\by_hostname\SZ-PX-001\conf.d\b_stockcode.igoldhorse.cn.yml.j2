{{ansible_managed|comment}}
http:
  middlewares:
    szPx001StockcodeIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    stockcode.igoldhorse.cn:
      service: szPx001Stockcode
      rule: Host(`stockcode.igoldhorse.cn`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
  services:
    szPx001Stockcode:
      loadBalancer:
        healthcheck:
          path: /actuator/info
        servers:
        - url: http://SZ-TASK-001:8028
        - url: http://SZ-DERIV-001:8028
