{{ansible_managed|comment}}
http:
  middlewares:
    openapiAuth:
      forwardAuth:
        address: http://HK-PROXY-001:8210/open-api-auth/auth_api/traffic_authentication
        trustForwardHeader: true
#    hkProxy001IpWhiteList:
#      ipWhiteList:
#        sourceRange:
#        # TODO: add office ip
#        - "***************"
#        - "*************"
#        - "**************"
#        - "***************"
#        - "*************"
#        - "**************"
  routers:
    openapi-auth-base:
      service: hkProxy001Auth
      rule: Host(`openapi-auth.igoldhorse.cn`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
        domains:
        - main: openapi-auth.igoldhorse.cn
    openapi-auth.igoldhorse.cn:
      service: hkProxy001AuthWs
      rule: Host(`openapi-auth.igoldhorse.cn`) && Path(`/ws`)
      middlewares:
      - gzip
      - openapiAuth
      tls:
        certResolver: httpResolver
        domains:
        - main: openapi-auth.igoldhorse.cn
    openapi-push.igoldhorse.cn:
      service: hkPx001OpAccBackendMqttWs
      rule: Host(`openapi-push.igoldhorse.cn`,`openapi-push.igoldhorse.com`) && Path(`/ws`)
      middlewares:
      - gzip
      - openapiAuth
      tls:
        certResolver: httpResolver
        domains:
        - main: openapi-push.igoldhorse.cn
        - main: openapi-push.igoldhorse.com
  services:
    hkProxy001AuthWs:
      loadBalancer:
        servers:
        - url: http://SZ-PUSH-001:15675
    hkPx001OpAccBackendMqttWs:
      loadBalancer:
        servers:
        - url: http://HK-TD-002:15675
    hkProxy001Auth:
      loadBalancer:
        servers:
        - url: http://HK-PROXY-001:8211
        - url: http://HK-MG-002:8211

