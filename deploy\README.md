# 服务器部署工具集

这是一个基于 Ansible 的服务器部署工具集，用于标准化和自动化服务器环境的配置和应用部署。

## 功能特性

- 服务器初始化配置
- 用户权限管理
- 各类中间件部署
- 监控系统部署
- 应用服务部署

## 目录结构

```
deploy/
├── roles/                # 角色定义目录
│   ├── 2001.deploy_traefik/    # Traefik 部署
│   ├── 2004.deploy_grafana/    # Grafana 部署
│   ├── 2005.deploy_prometheus/ # Prometheus 部署
│   └── ...
├── vars/                # 变量配置目录
├── *.playbook.yml      # 各类部署 playbook
└── hosts.ini           # 主机清单
```

## 前置条件

- Ansible 2.9+
- 目标机器可通过 SSH 访问
- Python 3.x

## 快速开始

1. 设置 sudo 密码（可选）
```bash
export ANSIBLE_BECOME_PASS=<SUDOPASSWORD>
```

2. 配置 SSH
```bash
eval $(ssh-agent)
ssh-add -D
ssh-add <ID_ED25519_FILE>
```

3. 执行部署
```bash
ansible-playbook playbook.yml \
    -i hosts.ini \
    -e @../vault.vars.yml \
    -e DEPLOY_PROJECT_HOSTS=<DEPLOYHOST/GROUP> \
    -u <SERVER_USERNAME> \
    -b
```

## 主要部署项目

### 基础设施
- Traefik (2001)
- Grafana (2004)
- Prometheus (2005)
- AlertManager (2006)

### 中间件
- Redis Cluster (2021)
- Redis Sentinel (2014)
- MySQL (2012)
- RabbitMQ (2015)
- Nacos (2016)
- MinIO (2011)
- RocketMQ (2039)
- ClickHouse (2034)

### 监控组件
- Redis Exporter (2027)
- MySQL Exporter (2028)
- RocketMQ Exporter (2040)
- Jaeger Agent (2012)

### 工具
- Yearning
- Loki
- Webhook DingTalk

## 参数说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `-i` | 指定 inventory 文件 | `hosts.ini` |
| `-e` | 设置变量 | `@vault.vars.yml` |
| `-u` | 远程用户名 | `admin` |
| `-b` | 使用 sudo 权限 | - |

## 常见问题

1. SSH 连接失败
   - 检查 SSH 密钥是否正确添加
   - 确认目标机器 SSH 端口是否开放

2. 权限问题
   - 确认 ANSIBLE_BECOME_PASS 是否正确设置
   - 检查用户是否有 sudo 权限

3. 代理设置
   - 部分组件需要通过代理下载，确认 HTTP_PROXY/HTTPS_PROXY 设置是否正确

## 维护说明

- 所有配置文件模板位于各角色的 `templates` 目录下
- 默认变量定义在各角色的 `defaults/main.yml` 中
- 主机组定义在 `hosts.ini` 文件中

## 贡献指南

1. Fork 本仓库
2. 创建功能分支
3. 提交变更
4. 创建 Pull Request

## 注意事项

- 生产环境部署前请先在测试环境验证
- 确保备份关键数据
- 注意检查防火墙规则
- 遵循最小权限原则