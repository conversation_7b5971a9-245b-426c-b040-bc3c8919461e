{{ansible_managed|comment}}
http:
  routers:
    # Dmz
    gqlDmz_base.trade.tanghui.press:
      rule: Host(`base.trade.tanghui.press`) && Method(`POST`) && Path(`/dmz/api/v1/gql/`)
      service: gqlDmzTh
      middlewares:
      - gzip
      - requestAuthError
      - requestAuthDmz
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    gqlDmz_base.trade.tanghui.press_cors:
      rule: Host(`base.trade.tanghui.press`) && Method(`OPTIONS`) && Path(`/dmz/api/v1/gql/`)
      service: gqlDmzTh
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    gqlDmz_base.trade.tanghui.press_ping:
      rule: Host(`base.trade.tanghui.press`) && Method(`GET`) && Path(`/dmz/-/ping/`)
      service: gqlDmzTh
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    # Secret
    gqlSecret_base.trade.tanghui.press:
      rule: Host(`base.trade.tanghui.press`) && Method(`POST`) && Path(`/secret/api/v1/gql/`)
      service: gqlSecretTh
      middlewares:
      - gzip
      - requestAuthError
      - requestAuthSecret
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    gqlSecret_base.trade.tanghui.press_cors:
      rule: Host(`base.trade.tanghui.press`) && Method(`OPTIONS`) && Path(`/secret/api/v1/gql/`)
      service: gqlSecretTh
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    gqlSecret_base.trade.tanghui.press_ping:
      rule: Host(`base.trade.tanghui.press`) && Method(`GET`) && Path(`/secret/-/ping/`)
      service: gqlSecretTh
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    mqttSecret_sz.quot.tanghui.press:
      rule: Host(`sz.quot.tanghui.press`) && Path(`/ws`)
      service: mqttSecret
      middlewares:
      - requestAuthError
      - requestAuthSecret
      tls:
        certResolver: httpResolver
