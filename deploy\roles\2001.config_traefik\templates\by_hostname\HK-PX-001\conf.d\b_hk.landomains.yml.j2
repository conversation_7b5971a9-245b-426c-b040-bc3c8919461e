{{ansible_managed|comment}}
http:
  routers:
    #ayersgts.lan.ghhk:
    #  service: ghayersgts
    #  rule: Host(`ayersgts.lan.ghhk`)
    #  middlewares:
    #  - gzip
    ayersvrs.lan.ghhk:
      service: ghayersvrs
      rule: Host(`ayersvrs.lan.ghhk`)
      middlewares:
      - gzip
    ayerstrd.lan.ghhk:
      service: ghayerstrd
      rule: Host(`ayerstrd.lan.ghhk`)
      middlewares:
      - gzip
    userstd.lan.ghhk:
      service: ghuserstd
      rule: Host(`userstd.lan.ghhk`)
      middlewares:
      - gzip
  services:
    #ghayersgts:
    #  loadBalancer:
    #    healthcheck:
    #      path: /-/ping/
    #    servers:
    #    - url: http://************:44648
    #    - url: http://************:44648
    ghayersvrs:
      loadBalancer:
        healthcheck:
          path: /-/ping/
        servers:
        - url: http://HK-TD-003:31412
        - url: http://HK-MG-001:31412
    ghayerstrd:
      loadBalancer:
        healthcheck:
          path: /-/ping/
        servers:
        - url: http://HK-TD-003:10002
        - url: http://HK-TD-001:10002
    ghuserstd:
      loadBalancer:
        healthcheck:
          path: /-/ping/
        servers:
        - url: http://HK-TD-003:35366
        - url: http://HK-MG-001:35366
