groups:
- name: promtail
  rules:
  - alert: AyersGtsServiceHasCriticalLogs
    expr: promtail_custom_gh_ayersgts_critical > 0
    for: 0m
    labels:
      severity: critical
      gh_severity: critical
    annotations:
      summary: AyersGtsService has critical level log (instance {{ $labels.instance }})
      description: "Check Service Log For More Detail\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
  - alert: AyersGtsServiceHasErrorLogs
    expr: promtail_custom_gh_ayersgts_error > 0
    for: 0m
    labels:
      severity: critical
      gh_severity: critical
    annotations:
      summary: AyersGtsService has error level log (instance {{ $labels.instance }})
      description: "Check Service Log For More Detail\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
  - alert: Prod_PyServiceHasTracebackLogs
    expr: promtail_custom_gh_Traceback > 0
    for: 0m
    labels:
      py_severity: critical
    annotations:
      summary: Prod_PyService has Traceback log (instance {{ $labels.instance }})
      description: "Check Service Log For More Detail\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
  - alert: Prod_PyServiceHasConnectErrorLogs
    expr: promtail_custom_gh_connect_error > 0
    for: 0m
    labels:
      py_severity: critical
    annotations:
      summary: Prod_PyService has gh_connect_error log (instance {{ $labels.instance }})
      description: "Check Service Log For More Detail\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
  - alert: Prod_PyServiceHaServerNotConnectLogs
    expr: promtail_custom_gh_server_not_connect > 0
    for: 0m
    labels:
      py_severity: critical
    annotations:
      summary: Prod_PyService has server not connect log (instance {{ $labels.instance }})
      description: "Check Service Log For More Detail\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
  - alert: Prod_PyServiceHasOperatorNotLoginLogs
    expr: promtail_custom_gh_operator_not_login > 0
    for: 0m
    labels:
      py_severity: critical
    annotations:
      summary: Prod_PyService has operator not login log (instance {{ $labels.instance }})
      description: "Check Service Log For More Detail\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
#  - alert: Prod_PyServiceHasVipPlaceOrderLogs
#    expr: promtail_custom_gh_vip_place_order > 0
#    for: 0m
#    labels:
#      py_severity: critical
#    annotations:
#      summary: Prod_PyService has vip_place_order log (instance {{ $labels.instance }})
#      description: "Check Service Log For More Detail\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
#  - alert: Prod_PyServiceHasVipCancelOrderLogs
#    expr: promtail_custom_gh_vip_cancel_order > 0
#    for: 0m
#    labels:
#      py_severity: critical
#    annotations:
#      summary: Prod_PyService has vip_cancel_order log (instance {{ $labels.instance }})
#      description: "Check Service Log For More Detail\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
 