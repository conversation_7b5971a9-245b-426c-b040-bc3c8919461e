[Unit]
Description=Elasticsearch Exporter
Documentation=https://github.com/prometheus-community/elasticsearch_exporter
Wants=network-online.target
After=network-online.target

[Service]
Type=simple
User={{ elasticsearch_exporter_user }}
Group={{ elasticsearch_exporter_group }}
ExecStart={{ elasticsearch_exporter_bin_path }}/elasticsearch_exporter --web.listen-address={{ elasticsearch_exporter_ip }}:{{ elasticsearch_exporter_port }} --web.telemetry-path=/{{ elasticsearch_exporter_web_telemetry_path }} --es.uri={{ elasticsearch_exporter_es_uri }} --es.all={{ elasticsearch_exporter_es_all | lower }} --es.indices={{ elasticsearch_exporter_es_indices | lower }} --es.indices_settings={{ elasticsearch_exporter_es_indices_settings | lower }} --es.shards={{ elasticsearch_exporter_es_shards | lower }} --es.snapshots={{ elasticsearch_exporter_es_snapshots | lower }} --es.timeout={{ elasticsearch_exporter_es_timeout }} --log.level={{ elasticsearch_exporter_log_level }} --log.format={{ elasticsearch_exporter_log_format }}
SyslogIdentifier=elasticsearch_exporter
Restart=always
RestartSec=1
StartLimitInterval=0

{% if elasticsearch_exporter_private_tmp %}
PrivateTmp=yes
{% endif %}

ProtectHome=yes
NoNewPrivileges=yes
ProtectSystem=strict
ProtectControlGroups=true
ProtectKernelModules=true
ProtectKernelTunables=yes

[Install]
WantedBy=multi-user.target
