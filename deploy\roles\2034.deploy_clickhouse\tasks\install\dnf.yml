---
#Main installation actions
#https://clickhouse.yandex/docs/en/getting_started/index.html#installation

- name: Install by YUM | Ensure clickhouse repo GPG key imported
  rpm_key:
    state: present
    key: "{{ clickhouse_repo_key }}"
  tags: [install]
  become: true

- name: Install by YUM | Ensure clickhouse repo installed
  yum_repository:
    name: clickhouse
    file: clickhouse
    description: "Clickhouse repo"
    baseurl: "{{ clickhouse_repo }}"
    enabled: yes
    gpgcheck: 1
    gpgkey: "{{ clickhouse_repo_key }}"
  tags: [install]
  become: true

- name: Install by YUM | Ensure clickhouse package installed (latest)
  dnf:
    name: "{{ clickhouse_package }} "
    state: "{{ clickhouse_version }}"
  become: true
  tags: [install]
  when: clickhouse_version == 'latest'

- name: Install by YUM | Ensure clickhouse package installed (version {{ clickhouse_version }})
  dnf:
    name: "{{ clickhouse_package | map('regex_replace', '$', '-' + clickhouse_version) | list }}"
    state: present
    disable_gpg_check: "{{ true if clickhouse_version == '19.4.0' else omit }}" 
  become: true
  tags: [install]
  when: clickhouse_version != 'latest'
