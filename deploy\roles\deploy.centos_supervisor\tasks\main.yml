- name: Install centos supervisor
  dnf:
    name: supervisor
    enablerepo: testing
    state: present
  when: (ansible_distribution|lower)=='centos'

- name: Ensure centos supervisor service started
  systemd:
    name: supervisord
    daemon_reload: true
    enabled: true
  when: (ansible_distribution|lower)=='centos'

- name: Ensure logs directories
  file:
    path: "/data/logs"
    state: directory
    owner: wukong
    group: wukong
    recurse: yes

- name: Ensure apps directories
  file:
    path: "/data/apps"
    state: directory
    owner: wukong
    group: wukong
    recurse: yes


