# /etc/frp/fprc.ini

{{ansible_managed|comment}}

[common]
server_addr = {{hostvars['SZ-OPACC-001']['ansible_host']}}
server_port = {{FRPS_PORT}}
token = {{FRP_AUTH}}
protocol = kcp
enable_prometheus = true

dashboard_addr = 0.0.0.0
dashboard_port = {{FRPS_PORT+1}}
dashboard_user = {{FRPS_DASHBOARD_USER}}
dashboard_pwd = {{FRPS_DASHBOARD_PASSWORD}}

admin_addr = 0.0.0.0
admin_port = {{FRPS_PORT+2}}
admin_user = {{FRPS_ADMIN_USER}}
admin_pwd = {{FRPS_ADMIN_PASSWORD}}

[HK-TD-002:redis:63790]
local_ip = 127.0.0.1
local_port = 63790
remote_port = 63790
type = tcp
use_encryption = true
use_compression = true

[HK-TD-002:rsync:52093]
local_ip = 127.0.0.1
local_port = 52093
remote_port = 52093
type = tcp
use_encryption = true
use_compression = true

[HK-TD-002:mysql:33260]
local_ip = 127.0.0.1
local_port = 33260
remote_port = 33260
type = tcp
use_encryption = true
use_compression = true

[HK-TD-002:biz_platform_app:8105]
local_ip = 127.0.0.1
local_port = 8105
remote_port = 18105
type = tcp
use_encryption = true
use_compression = true

[HK-MG-001:user:35366]
local_ip = ************
local_port = 35366
remote_port = 35366
type = tcp
use_encryption = true
use_compression = true

[HK-MG-002:lpoa:42924]
local_ip = ************
local_port = 42924
remote_port = 29244
type = tcp
use_encryption = true
use_compression = true

[HK-OPENAPI-001:quot_struct:8060]
local_ip = *************
local_port = 8060
remote_port = 8060
type = tcp
use_encryption = true
use_compression = true

[HK-OPENAPI-001:biz_struct:8108]
local_ip = *************
local_port = 8108
remote_port = 8108
type = tcp
use_encryption = true
use_compression = true
