---
- name: create rocketmq system group
  group:
    name: rocketmq
    system: true
    state: present
    gid: 3661

- name: create rocketmq system user
  user:
    name: rocketmq
    system: true
    shell: "/usr/sbin/nologin"
    group: rocketmq
    createhome: false
    uid: 3661

- name: create rocketmq db directory
  file:
    path: "/data/rocketmq"
    state: directory
    recurse: true
    owner: rocketmq
    group: rocketmq
    mode: 0755

- name: download rocketmq binary
  become: true
  get_url:
    url: "https://filescfcdn.fwdev.top/common/rocketmq-all-{{rocketmq_version}}-ALPHA-bin-release.zip"
    dest: "/opt/rocketmq-{{rocketmq_version}}.zip"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: Unpack rocketmq binary
  become: true
  unarchive:
    src: "/opt/rocketmq-{{rocketmq_version}}.zip"
    dest: "/opt"
    remote_src: True
  check_mode: false

- name: Ensure apps directories
  file:
    path: "/opt/rocketmq"
    state: directory
    owner: rocketmq
    group: rocketmq
    recurse: yes

- name: mv default diretory
  shell:
    cmd: cp -r /opt/rocketmq-all-5.0.0-ALPHA-bin-release/* /opt/rocketmq;rm /opt/rocketmq-all-5.0.0-ALPHA-bin-release -rf

- name: Ensure apps directories
  file: 
    path: "/opt/rocketmq"
    state: directory
    owner: rocketmq
    group: rocketmq
    recurse: yes

- name: Ensure logs directories
  file:
    path: "{{ item }}"
    state: directory
    owner: rocketmq
    group: rocketmq
    recurse: yes
  with_items:
  - "/data/logs/rocketmq"
  - "/data/rocketmq/logs/store"
  - "/data/rocketmq/store/commitlog"
  - "/data/logs/rocketmq-dashboard"
  - "/home/<USER>/logs/consolelogs"

- name: create config
  template:
    src: "{{ item }}.j2"
    dest: /opt/rocketmq/conf/{{ item }}
    owner: rocketmq
    group: rocketmq
    mode: 0644
  with_items:
  - broker.conf
  - logback_broker.xml
  - logback_namesrv.xml
  - logback_tools.xml
  notify:
  - restart rocketmq

- name: create sh
  template:
    src: "{{ item }}.j2"
    dest: /opt/rocketmq/bin/{{ item }}
    owner: rocketmq
    group: rocketmq
    mode: 0755
  with_items:
  - runbroker.sh
  - runserver.sh
  notify:
  - restart rocketmq

- name: download rocketmq-dashboard binary
  become: true
  get_url:
    url: "https://filescfcdn.fwdev.top/common/rocketmq-dashboard-1.0.1-SNAPSHOT.jar"
    dest: "/opt/rocketmq"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: copy  supervisor config file
  template:
    src: supervisor_project_config.ini.j2
    dest: "/etc/supervisor/conf.d/rocketmq.conf"
  notify:
  - "restart rocketmq"

#- name: start dashboard
#  shell:
#    cmd: nohup java -jar rocketmq-dashboard-1.0.1-SNAPSHOT.jar >> /data/logs/rocketmq-dashboard/rocketmq-dashboard.out 2>&1 &
#    chdir: /opt/rocketmq