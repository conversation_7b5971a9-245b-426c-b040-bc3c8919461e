{{ansible_managed|comment}}
http:
  routers:
    ayersStatementDaily_files.statement.tanghui.press:
      rule: Host(`files.statement.tanghui.press`) && Method(`GET`) && Path(`/ayers_reports/daily/`)
      service: ayersStatement
      middlewares:
      - gzip
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: files.statement.tanghui.press
    ayersStatementMonthly_files.statement.tanghui.press:
      rule: Host(`files.statement.tanghui.press`) && Method(`GET`) && Path(`/ayers_reports/monthly/`)
      service: ayersStatement
      middlewares:
      - gzip
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: files.statement.tanghui.press
  services:
    ayersStatement:
      loadBalancer:
        servers:
        - url: HK-MG-002:34239
