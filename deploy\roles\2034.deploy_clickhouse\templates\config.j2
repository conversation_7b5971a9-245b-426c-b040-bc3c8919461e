<!-- This file was generated automatically.
     Do not edit it: it is likely to be discarded and generated again before it's read next time.
     Files used to generate this file:
       /etc/clickhouse-server/config2.xml
       /etc/clickhouse-server/metrika2.xml      -->

<yandex>
    <!-- 日志 -->
    <logger>
        <level>trace</level>
        <log>/data/clickhouse/{{ inventory_hostname }}/log/server.log</log>
        <errorlog>/data/clickhouse/{{ inventory_hostname }}/log/error.log</errorlog>
        <size>2048M</size>
        <count>10</count>
    </logger>

    <!-- 端口 -->
    <http_port>8123</http_port>
    <tcp_port>9000</tcp_port>
    <!-- Compatibility with MySQL protocol.
         ClickHouse will pretend to be MySQL for applications connecting to this port.
    -->
    <mysql_port>9004</mysql_port>

    <!-- Compatibility with PostgreSQL protocol.
         ClickHouse will pretend to be PostgreSQL for applications connecting to this port.
    -->
    <postgresql_port>9005</postgresql_port>
    <interserver_http_port>9009</interserver_http_port>
    <!-- For 'Connection: keep-alive' in HTTP 1.1 -->
    <keep_alive_timeout>3</keep_alive_timeout>
    <!-- 本机域名 -->
    <interserver_http_host>{{ inventory_hostname }}</interserver_http_host>

    <!-- 监听IP -->
    <listen_host>::</listen_host>
    <!-- 最大连接数 -->
    <max_connections>4096</max_connections>

    <!-- 没搞懂的参数 -->
    <keep_alive_timeout>3</keep_alive_timeout>

    <!-- Default root page on http[s] server. For example load UI from https://tabix.io/ when opening http://localhost:8123 -->
    <http_server_default_response>
        <![CDATA[
        <html ng-app="SMI2">
            <head>
                <base href="http://ui.tabix.io/">
            </head>
            <body>
                <div ui-view="" class="content-ui">
                </div>
                <script src="http://loader.tabix.io/master.js">
                </script>
            </body>
        </html>
       ]]>
    </http_server_default_response>

    <!-- 最大并发查询数 -->
    <max_concurrent_queries>500</max_concurrent_queries>
    <max_server_memory_usage>0</max_server_memory_usage>
    <max_thread_pool_size>10000</max_thread_pool_size>

    <!-- On memory constrained environments you may have to set this to value larger than 1.
      -->
    <max_server_memory_usage_to_ram_ratio>0.9</max_server_memory_usage_to_ram_ratio>

    <!-- Simple server-wide memory profiler. Collect a stack trace at every peak allocation step (in bytes).
        Data will be stored in system.trace_log table with query_id = empty string.
        Zero means disabled.
     -->
    <total_memory_profiler_step>4194304</total_memory_profiler_step>

    <!-- 单位是B -->
    <uncompressed_cache_size>8589934592</uncompressed_cache_size>
    <mark_cache_size>10737418240</mark_cache_size>
    <mmap_cache_size>1000</mmap_cache_size>

    <!-- Cache size in bytes for compiled expressions.-->
    <compiled_expression_cache_size>134217728</compiled_expression_cache_size>

    <!-- 存储路径 -->
    <path>/data/clickhouse/{{ inventory_hostname }}/</path>
    <tmp2_path>/data/clickhouse/{{ inventory_hostname }}/tmp/</tmp2_path>

    <!-- user配置 -->
    <!-- Directory with user provided files that are accessible by 'file' table function. -->
    <user_files_path>/data/clickhouse/{{ inventory_hostname }}/user_files/</user_files_path>
    <user_directories>
        <users_xml>
            <!-- Path to configuration file with predefined users. -->
            <path>users.xml</path>
        </users_xml>
        <local_directory>
            <!-- Path to folder where users created by SQL commands are stored. -->
            <path>/data/clickhouse/{{ inventory_hostname }}/access/</path>
        </local_directory>
    </user_directories>
    <default_profile>default</default_profile>
    <max_session_timeout>3600</max_session_timeout>
    <default_session_timeout>60</default_session_timeout>

    <query_log>
        <database>system</database>
        <table>query_log</table>
        <partition_by>toYYYYMM(event_date)</partition_by>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </query_log>

    <!-- Default profile of settings. -->
    <default_profile>default</default_profile>

    <default_database>default</default_database>
    <remote_servers>
        <!-- 3分片0备份 -->
        <cluster_3shards_0replicas>
            <!-- 数据分片1  -->
            <shard>
                <weight>1</weight>
                <!-- Optional. Whether to write data to just one of the replicas. Default: false (write data to all replicas).
                1. internal_replication为false时候，（前提是往分布表写入数据）会自动往同一shard下所有备份表写入相同数据，不需要任何其他外力，单独设置这个参数即可；
				但是会出现各备份之间数据不同步的情况，因为此种情况下往分布式表里面写数据，后台算法会先按照weight将数据分成shard数量堆，然后将对应堆的数据分别写入该shard下面的所有备份表中，
				有可能存在同样的数据写入A备份成功但是写入B备份失败的情况，这里是没有校验的；
                2. internal_replication为true时，一定要配合zookeeper和ReplicatedMergeTree引擎表使用，如果不配合这些，经本人测试查询数据时会出现严重错误，请切记
                -->
                <internal_replication>true</internal_replication>
                <replica>
                    <host>SZ-CLHOUSE-001</host>
                    <port>9000</port>
                    <user>default</user>
                    <password>123</password>
                </replica>
            </shard>
            <!-- 数据分片2  -->
            <shard>
                <weight>1</weight>
                <internal_replication>true</internal_replication>
                <replica>
                    <host>SZ-CLHOUSE-002</host>
                    <port>9000</port>
                    <user>default</user>
                    <password>123</password>
                </replica>
            </shard>
            <!-- 数据分片3  -->
            <shard>
                <weight>1</weight>
                <internal_replication>true</internal_replication>
                <replica>
                    <host>SZ-CLHOUSE-003</host>
                    <port>9000</port>
                    <user>default</user>
                    <password>123</password>
                </replica>
            </shard>
        </cluster_3shards_0replicas>
    </remote_servers>
    <zookeeper optional="true">
        <node index="l">
            <host>*************</host>
            <port>2181</port>
        </node>
        <node index="2">
            <host>*************</host>
            <port>2181</port>
        </node>
        <node index="3">
            <host>*************</host>
            <port>2181</port>
        </node>
    </zookeeper>
    <!-- <macros optional="true">
            <shard>03</shard>
            <replica>SZ-CLHOUSE-001_01</replica>
        </macros>-->

    <!-- 没搞懂的参数 -->
    <builtin_dictionaries_reload_interval>3600</builtin_dictionaries_reload_interval>

    <!-- 控制大表的删除 -->
    <max_table_size_to_drop>0</max_table_size_to_drop>

    <!-- Allow to execute distributed DDL queries (CREATE, DROP, ALTER, RENAME) on cluster.
         Works only if ZooKeeper is enabled. Comment it if such functionality isn't required. -->
    <distributed_ddl>
        <!-- Path in ZooKeeper to queue with DDL queries -->
        <path>/clickhouse/task_queue/ddl</path>
    </distributed_ddl>

    <!-- Directory in <clickhouse-path> containing schema files for various input formats.
         The directory will be created if it doesn't exist.
      -->
    <format_schema_path>/data/clickhouse/{{ inventory_hostname }}/format_schemas/</format_schema_path>
    <include_from>/etc/clickhouse-server/metrika.xml</include_from>
</yandex>