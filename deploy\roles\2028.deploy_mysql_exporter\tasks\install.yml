---
- name: Create the MySQL Exporter group
  group:
    name: "{{ mysqld_exporter_group }}"
    state: present
    system: true

- name: Create the MySQL Exporter user
  user:
    name: "{{ mysqld_exporter_user }}"
    groups: "{{ mysqld_exporter_group }}"
    append: true
    shell: /usr/sbin/nologin
    system: true
    createhome: false
    home: /

- block:
    - name: Download mysqld_exporter binary to local folder
      become: false
      get_url:
        use_proxy: '{{USE_PROXY|default(true)}}'
        url: "https://filescfcdn.fwdev.top/common/mysqld_exporter-{{ mysqld_exporter_version }}.linux-{{ go_arch_map[ansible_architecture] | default(ansible_architecture) }}.tar.gz"
        dest: "/tmp/mysqld_exporter-{{ mysqld_exporter_version }}.linux-{{ go_arch_map[ansible_architecture] | default(ansible_architecture) }}.tar.gz"
        checksum: "sha256:626584c5d1c0cf09982302763e69db24fabe5dc736e7b694a3f8fdfee3d8d9a2"
      register: _download_binary
      until: _download_binary is succeeded
      retries: 5
      delay: 2
      check_mode: false

    - name: Unpack mysqld_exporter binary
      become: false
      unarchive:
        src: "/tmp/mysqld_exporter-{{ mysqld_exporter_version }}.linux-{{ go_arch_map[ansible_architecture] | default(ansible_architecture) }}.tar.gz"
        dest: "/tmp"
        creates: "/tmp/mysqld_exporter-{{ mysqld_exporter_version }}.linux-{{ go_arch_map[ansible_architecture] | default(ansible_architecture) }}/mysqld_exporter"
        remote_src: True
      check_mode: false

    - name: Propagate mysqld_exporter binaries
      copy:
        src: "/tmp/mysqld_exporter-{{ mysqld_exporter_version }}.linux-{{ go_arch_map[ansible_architecture] | default(ansible_architecture) }}/mysqld_exporter"
        dest: "/usr/local/bin/mysqld_exporter"
        mode: 0755
        owner: root
        group: root
        remote_src: True
      notify:
        - restart mysqld exporter
      when: not ansible_check_mode
  when: mysqld_exporter_binary_local_dir | length == 0

- name: propagate locally distributed mysqld_exporter binary
  copy:
    src: "{{ mysqld_exporter_binary_local_dir }}/mysqld_exporter"
    dest: "/usr/local/bin/mysqld_exporter"
    mode: 0755
    owner: root
    group: root
    remote_src: True
  when: mysqld_exporter_binary_local_dir | length > 0
  notify: restart mysqld exporter

- name: Copy the mysqld_exporter systemd service file
  template:
    src: mysqld_exporter.service.j2
    dest: /etc/systemd/system/mysqld_exporter.service
    owner: root
    group: root
    mode: 0644
  no_log: "{{ not lookup('env', 'ANSIBLE_DEBUG') | bool }}"
  notify:
    - restart mysqld exporter
