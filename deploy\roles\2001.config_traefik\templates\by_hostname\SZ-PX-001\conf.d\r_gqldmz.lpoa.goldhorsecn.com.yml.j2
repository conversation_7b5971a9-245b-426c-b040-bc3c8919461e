{{ansible_managed|comment}}
http:
  routers:
    gqldmz-lpoa-root-goldhorsecn:
      rule: Host(`gqldmz.lpoa.goldhorsecn.com`) && Method(`GET`, `POST`) && Path(`/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      - requestAuthError
      - requestAuthDmz
      tls:
        certResolver: httpResolver
    gqldmz-lpoa-root-goldhorsecn-cors:
      rule: Host(`gqldmz.lpoa.goldhorsecn.com`) && Method(`OPTIONS`) && Path(`/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      tls:
        certResolver: httpResolver
    gqldmz-lpoa-auth-goldhorsecn:
      rule: Host(`gqldmz.lpoa.goldhorsecn.com`) && Method(`POST`) && Path(`/api/v1/gql/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      - requestAuthError
      - requestAuthDmz
      tls:
        certResolver: httpResolver
    gqldmz-lpoa-goldhorsecn-cors:
      rule: Host(`gqldmz.lpoa.goldhorsecn.com`) && Method(`OPTIONS`) && Path(`/api/v1/gql/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      - requestAuthError
      tls:
        certResolver: httpResolver
    gqldmz-lpoa-intranet-goldhorsecn:
      rule: Host(`gqldmz.lpoa.goldhorsecn.com`) && PathPrefix(`/-/ping/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      tls:
        certResolver: httpResolver