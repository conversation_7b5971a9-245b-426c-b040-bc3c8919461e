{{ansible_managed|comment}}
http:
  middlewares:
    szBackendOmsMediaIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    oms-media.goldhorsecn.com:
      service: szBackendOmsMedia
      rule: Host(`oms-media.goldhorsecn.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
        domains:
        - main: oms-media.goldhorsecn.com
  services:
    szBackendOmsMedia:
      loadBalancer:
        servers:
        - url: http://SZ-NEWS-001:32709
