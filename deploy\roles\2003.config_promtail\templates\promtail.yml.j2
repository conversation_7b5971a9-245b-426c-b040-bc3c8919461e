# file: /etc/promtail/config.yml
{{ ansible_managed | comment }}

server:
  http_listen_port: {{PROMTAIL_HTTP_PORT}}
  grpc_listen_port: 0
positions:
  filename: "{{PROMTAIL_CONFIG_DIR}}/positions.yaml"
clients:
- url: {{LOKI_API_URL}}
  external_labels:
    host: {{ansible_hostname}}
scrape_configs:
# - job_name: local_file_sd
#   file_sd_configs:
#   - refresh_interval: 10s
#     files:
#     - "{{PROMTAIL_CONFIG_DIR}}/conf.d/*.yml"
- job_name: tmplog
  static_configs:
  - targets:
    - localhost
    labels:
      job: tmplogs
      __path__: /tmp/*log
- job_name: supervisor
  static_configs:
  - targets:
    - localhost
    labels:
      job: supervisorlogs
      __path__: /var/log/supervisor/*log
  pipeline_stages:
  - regex:
      source: filename
      expression: /var/log/supervisor/(?P<service>.*?).log
  - labels:
      service: service
  - match:
      selector: '{service=~"gh.*",service!="gh_tradelimit-rbmq-ayers"} |~"CRITICAL"'
      stages:
      - regex:
          expression: '.*(?P<level>CRITICAL).*'
      - metrics:
          gh_ayersgts_critical:
            type: Counter
            description: gh_ayersgts has critical error
            source: level
            config:
              value: CRITICAL
              action: inc
  - match:
      selector: '{service=~"gh.*",service!="gh_tradelimit-rbmq-ayers"} |~"Traceback"'
      stages:
      - regex:
          expression: '.*(?P<keyvalue>Traceback).*'
      - metrics:
          gh_Traceback:
            type: Counter
            description: gh has Traceback error
            source: keyvalue
            config:
              value: Traceback
              action: inc
  - match:
      selector: '{service=~"gh.*"} |~"connect error"'
      stages:
      - regex:
          expression: '.*(?P<keyvalue>connect error).*'
      - metrics:
          gh_connect_error:
            type: Counter
            description: gh has connect error
            source: keyvalue
            config:
              value: connect error
              action: inc
  - match:
      selector: '{service=~"gh.*"} |~"server not connect"'
      stages:
      - regex:
          expression: '.*(?P<keyvalue>server not connect).*'
      - metrics:
          gh_server_not_connect:
            type: Counter
            description: gh has server not connect error
            source: keyvalue
            config:
              value: server not connect
              action: inc
  - match:
      selector: '{service=~"gh.*"} |~"operator not login"'
      stages:
      - regex:
          expression: '.*(?P<keyvalue>operator not login).*'
      - metrics:
          gh_operator_not_login:
            type: Counter
            description: gh has operator not login error
            source: keyvalue
            config:
              value: operator not login
              action: inc
  - match:
      selector: '{service=~"gh_ayersgts.*"} |~"ERROR"'
      stages:
      - regex:
          expression: '.*(?P<level>ERROR).*'
      - metrics:
          gh_ayersgts_error:
            type: Counter
            description: gh_ayersgts has errors
            source: level
            config:
              value: ERROR
              action: inc
  # 接口耗时监控 - 提取耗时信息
  - match:
      selector: '{job="datalogs-info"}'
      stages:
      - regex:
          expression: '.*耗时[:：]\s*(?P<response_time>\d+(?:\.\d+)?)\s*[ms毫秒s秒].*'
      - regex:
          expression: '.*接口[:：]\s*(?P<api_path>[^\s]+).*'
      - labels:
          api_path: api_path
      - metrics:
          api_response_time_ms:
            type: Histogram
            description: API response time in milliseconds
            source: response_time
            config:
              buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000]
  # ResponseResultAdvice日志监控 - 提取API路径和响应时间
  - match:
      selector: '{job="supervisorlogs"} |~ "ResponseResultAdvice.*request url.*use:"'
      stages:
      # 提取request url中的API路径和响应时间
      - regex:
          expression: '.*request url:\[<(?P<api_path>[^>]+)>\].*use: \[<(?P<response_time>\d+)>\]ms.*'
      - labels:
          api_path: api_path
      # 记录所有API响应时间
      - metrics:
          bam_api_response_time_ms:
            type: Histogram
            description: BAM API response time in milliseconds
            source: response_time
            config:
              buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000, 30000]
      # 记录慢请求计数器 (>1000ms)
      - template:
          source: is_slow
          template: |
            {{- if gt (float64 .response_time) 1000.0 -}}
              1
            {{- else -}}
              0
            {{- end -}}
      - metrics:
          bam_api_slow_requests_total:
            type: Counter
            description: Total number of slow BAM API requests (>1s)
            source: is_slow
            config:
              value: "1"
              action: inc

  # 慢接口告警 - 耗时大于1秒的接口
  - match:
      selector: '{job="datalogs-info"} |~ "耗时[:：]\\s*[1-9]\\d{3,}\\s*[ms毫秒]|耗时[:：]\\s*[1-9]\\d*\\.\\d+\\s*[s秒]"'
      stages:
      - regex:
          expression: '.*耗时[:：]\s*(?P<slow_response_time>\d+(?:\.\d+)?)\s*(?P<time_unit>[ms毫秒s秒]+).*'
      - regex:
          expression: '.*接口[:：]\s*(?P<slow_api_path>[^\s]+).*'
      - labels:
          slow_api_path: slow_api_path
          time_unit: time_unit
      - metrics:
          api_slow_requests_total:
            type: Counter
            description: Total number of slow API requests (>1s)
            source: slow_response_time
            config:
              action: inc
#  - match:
#      selector: '{service=~"gh_ayers_trade-rest"} |~"vip_place_order"'
#      stages:
#      - regex:
#          expression: '.*(?P<keyvalue>vip_place_order).*'
#      - metrics:
#          gh_vip_place_order:
#            type: Counter
#            description: vip_place_order happened
#            source: keyvalue
#            config:
#              value: vip_place_order
#              action: inc
#  - match:
#      selector: '{service=~"gh_ayers_trade-rest"} |~"vip_cancel_order"'
#      stages:
#      - regex:
#          expression: '.*(?P<keyvalue>vip_cancel_order).*'
#      - metrics:
#          gh_vip_cancel_order:
#            type: Counter
#            description: vip_cancel_order happened
#            source: keyvalue
#            config:
#              value: vip_cancel_order
#              action: inc

- job_name: system
  static_configs:
  - targets:
    - localhost
    labels:
      job: varlogs
      __path__: /var/log/*log
- job_name: datalogs
  static_configs:
  - targets:
    - localhost
    labels:
      job: datalogs-info
      __path__: /data/logs/*/*.log
  - targets:
    - localhost
    labels:
      job: datalogs-error
      __path__: /data/logs/*/error/*.log
  - targets:
    - localhost
    labels:
      job: datalogs-warn
      __path__: /data/logs/*/warn/*.log
  - targets:
    - localhost
    labels:
      job: traefik-log
      __path__: /var/log/traefik/access.log
  pipeline_stages:
  - regex:
      source: filename
      expression: /data/logs/(?P<service>.*?)/.*
  - regex:
      expression: '(?P<date>\d{4,4}-\d{2,2}-\d{2,2}) (?P<time>\d{2,2}:\d{2,2}:\d{2,2}.\d{3,3})'
  - regex:
      expression: '(?P<timestamp>\d{4,4}-\d{2,2}-\d{2,2} \d{2,2}:\d{2,2}:\d{2,2}.\d{3,3}) (?P<protocol>[[\s\S]*?]) (?P<level>\w*) (?P<message>((?:.|\n)*))'
  - labels:
      service: service
      level:
      date:
  - timestamp:
      source: timestamp
      location: "Asia/Shanghai"
      format: "2006-01-02 15:04:05.000"