---
- name: download rocketmq-exporter binary
  become: true
  get_url:
    url: "https://filescfcdn.fwdev.top/common/rocketmq-exporter-0.0.2-SNAPSHOT.jar"
    dest: "/opt/rocketmq"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: copy  exporter supervisor config file
  template:
    src: supervisor_exporter_config.ini.j2
    dest: "/etc/supervisor/conf.d/rocketmqexporter.conf"
  notify:
  - "restart rocketmqexporter"
