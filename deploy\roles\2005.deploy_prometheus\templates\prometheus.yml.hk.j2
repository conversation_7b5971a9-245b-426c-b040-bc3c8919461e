# {{ ansible_managed | comment }}

global:
  scrape_interval: 20s
  evaluation_interval: 20s
alerting:
  alertmanagers:
  - file_sd_configs:
    - files:
      - alertmanager.d/*.yml
rule_files:
- rule.d/*.yml
scrape_configs:
- job_name: prometheus
  static_configs:
  - targets:
    - 127.0.0.1:9090
- job_name: file_sd
  file_sd_configs:
  - files:
    - file_sd.d/*.yml
  relabel_configs:
  - source_labels: [__address__]
    target_label: __param_target
  - source_labels: [__param_target]
    target_label: instance
- job_name: jvm
  metrics_path: /actuator/prometheus
  file_sd_configs:
  - files:
    - jvm_file_sd.d/jvm.yml
  relabel_configs:
  - source_labels:
    - __address__
    - instance
    separator: "-"
    target_label: instance
- job_name: jvm_s
  file_sd_configs:
  - files:
    - jvm_file_sd.d/jvm_s.yml
  relabel_configs:
  - source_labels:
    - instance
    separator: "/"
    target_label: __metrics_path__
    regex: (.+)
    replacement: $1/actuator/prometheus
  - source_labels:
    - __address__
    - instance
    separator: "-"
    target_label: instance
#- job_name: 'ping_status'
#  metrics_path: /probe
#  params:
#    module: [icmp]
#  file_sd_configs:
#  - files:
#    - blackbox_sd.d/blackbox_icmp.yml
#  relabel_configs:
#  - source_labels: [__address__]
#    target_label: __param_target
#  - source_labels: [__param_target]
#    target_label: instance
#  - target_label: __address__
#    replacement: 127.0.0.1:9115
- job_name: 'port_status'
  metrics_path: /probe
  params:
    module: [tcp_connect]
  file_sd_configs:
  - files:
    - blackbox_sd.d/blackbox_port.yml
    - blackbox_sd.d/blackbox_port_frp.yml
  relabel_configs:
  - source_labels:
    - __address__
    - instance
    separator: "-"
    target_label: instance
  - source_labels: [__address__]
    target_label: __param_target
  #- source_labels: [__param_target]
  #  target_label: instance
  - target_label: __address__
    replacement: 127.0.0.1:9115
- job_name: 'blackbox-http'
  metrics_path: /probe
  params:
    modelue: [http_2xx]
  file_sd_configs:
  - files:
    - blackbox_sd.d/blackbox_http.yml
    relabel_configs:
    - source_labels: [__address__]
      target_label: __param_target
    - source_labels: [__param_target]
      target_label: instance
    - target_label: __address__
      replacement: 127.0.0.1:9115
- job_name: 'redis_exporter_targets'
  static_configs:
    - targets:
      - redis://HK-TD-002:63790
  metrics_path: /scrape
  relabel_configs:
    - source_labels: [__address__]
      target_label: __param_target
    - source_labels: [__param_target]
      target_label: instance
    - target_label: __address__
      replacement: 127.0.0.1:9121
- job_name: 'redis_exporter'
  static_configs:
  - targets:
    - 127.0.0.1:9121
- job_name: 'redis_exporter_targets2'
  static_configs:
    - targets:
      - redis://HK-MG-001:6379
  metrics_path: /scrape
  relabel_configs:
    - source_labels: [__address__]
      target_label: __param_target
    - source_labels: [__param_target]
      target_label: instance
    - target_label: __address__
      replacement: HK-MG-001:9121
- job_name: 'redis_exporter2'
  static_configs:
  - targets:
    - HK-MG-001:9121
#- job_name: 'redis_exporter_targets3'
#  static_configs:
#    - targets:
#      - redis://HK-PX-001:6379
#  metrics_path: /scrape
#  relabel_configs:
#    - source_labels: [__address__]
#      target_label: __param_target
#    - source_labels: [__param_target]
#      target_label: instance
#    - target_label: __address__
#      replacement: HK-PX-001:9121
#- job_name: 'redis_exporter3'
#  static_configs:
#  - targets:
#    - HK-PX-001:9121
#- job_name: 'dns-uptime'
#  metrics_path: /probe
#  params:
#    module: [dns_tcp]
#  static_configs:
#    - labels:
#        server: 'dnspod_free'
#      targets:
#      - cat.dnspod.net
#      - canoeing.dnspod.net
#    - labels:
#        server: 'dnspod_ent'
#      targets:
#      - ns3.dnsv4.com
#      - ns4.dnsv4.com
#  relabel_configs:
#    - source_labels: [__address__]
#      target_label: __param_target
#    - source_labels: [__param_target]
#      target_label: instance
#    - target_label: __address__
#      replacement: 127.0.0.1:9115