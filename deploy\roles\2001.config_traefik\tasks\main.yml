- name: start
  debug:
    msg: config traefik

- name: copy traefik static config
  template:
    src: "{{item}}"
    dest: "/etc/traefik/{{item|basename|regex_replace('(.*).j2', '\\1')}}"
    owner: traefik
  with_items:
  - static/traefik.yml.j2
  - static/traefik.env.j2
  notify:
  - restart traefik

- name: copy common traefik config templates
  tags:
  - traefik
  - common_config
  template:
    src: "{{item}}"
    dest: "/etc/traefik/{{item | regex_replace('.*../templates/common/(.*).j2', '\\1') }}"
    owner: traefik
  with_fileglob:
  - ../templates/common/conf.d/*.j2
  ignore_errors: true

- name: copy traefik config file_sd by hostname
  tags:
  - traefik
  - by_hostname_config
  template:
    src: "{{item}}"
    dest: "/etc/traefik/{{item | regex_replace('.*/(conf.d)/(.*?).j2', '\\1/\\2') }}"
    owner: traefik
  with_fileglob:
  - ../templates/by_hostname/{{ansible_hostname}}/conf.d/*.j2
  ignore_errors: true

