- name: create sync directory
  file:
    path: '{{item}}'
    state: directory
    mode: '755'
    owner: wukong
    group: wukong
  with_items:
  - '{{ayers_report_path}}'
  - '{{risk_marks_path}}'

- name: update rsync.secret
  lineinfile:
    path: /etc/rsyncd.secrets
    regex: '^{{ayers_report_user}}:.*'
    create: true
    line: '{{ayers_report_user}}:{{ayers_report_pass}}'
    mode: '600'

- name: update rsync.secret
  lineinfile:
    path: /etc/rsyncd.secrets
    regex: '^{{risk_marks_user}}:.*'
    create: true
    line: '{{risk_marks_user}}:{{risk_marks_pass}}'
    mode: '600'

- name: update rsync.secret
  lineinfile:
    path: /etc/rsyncd.secrets
    regex: '^{{risk_marks_user_uat_read}}:.*'
    create: true
    line: '{{risk_marks_user_uat_read}}:{{risk_marks_pass_uat_read}}'
    mode: '600'

- name: create rsync module
  template:
    src: "{{item}}"
    dest: "/etc/rsyncd.d/{{item|basename|splitext|first}}"
    mode: '640'
  with_fileglob:
  - 'templates/{{ansible_hostname}}/*.conf.j2'
