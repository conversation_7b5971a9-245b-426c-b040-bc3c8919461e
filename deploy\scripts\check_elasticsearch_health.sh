#!/bin/bash

# Elasticsearch 集群健康检查脚本
# 用法: ./check_elasticsearch_health.sh

set -e

# ES 节点列表
ES_NODES=("172.29.80.110:9200" "172.29.80.115:9200" "172.29.80.116:9200")

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 检查单个节点健康状态
check_node_health() {
    local node=$1
    local url="http://$node"
    
    echo "检查节点: $node"
    
    # 检查节点是否可达
    if ! curl -s --connect-timeout 5 "$url" > /dev/null; then
        log_error "  ✗ 节点不可达"
        return 1
    fi
    
    # 获取节点信息
    local node_info=$(curl -s "$url")
    local node_name=$(echo "$node_info" | jq -r '.name // "unknown"')
    local version=$(echo "$node_info" | jq -r '.version.number // "unknown"')
    
    log_info "  ✓ 节点可达"
    log_info "  节点名称: $node_name"
    log_info "  ES版本: $version"
    
    return 0
}

# 检查集群健康状态
check_cluster_health() {
    local node=$1
    local url="http://$node/_cluster/health"
    
    log_header "集群健康状态检查"
    
    local health_data=$(curl -s "$url")
    
    if [ $? -ne 0 ]; then
        log_error "无法获取集群健康状态"
        return 1
    fi
    
    local status=$(echo "$health_data" | jq -r '.status')
    local nodes=$(echo "$health_data" | jq -r '.number_of_nodes')
    local data_nodes=$(echo "$health_data" | jq -r '.number_of_data_nodes')
    local active_shards=$(echo "$health_data" | jq -r '.active_shards')
    local unassigned_shards=$(echo "$health_data" | jq -r '.unassigned_shards')
    local pending_tasks=$(echo "$health_data" | jq -r '.number_of_pending_tasks')
    
    echo "集群状态: $status"
    echo "节点数量: $nodes"
    echo "数据节点: $data_nodes"
    echo "活跃分片: $active_shards"
    echo "未分配分片: $unassigned_shards"
    echo "待处理任务: $pending_tasks"
    
    case $status in
        "green")
            log_info "✓ 集群状态良好"
            ;;
        "yellow")
            log_warn "⚠ 集群状态警告"
            ;;
        "red")
            log_error "✗ 集群状态严重"
            ;;
    esac
    
    if [ "$unassigned_shards" != "0" ]; then
        log_warn "存在 $unassigned_shards 个未分配分片"
    fi
    
    if [ "$pending_tasks" != "0" ]; then
        log_warn "存在 $pending_tasks 个待处理任务"
    fi
}

# 检查索引状态
check_indices_status() {
    local node=$1
    local url="http://$node/_cat/indices?v&h=index,status,health,docs.count,store.size"
    
    log_header "索引状态检查"
    
    local indices_data=$(curl -s "$url")
    
    if [ $? -ne 0 ]; then
        log_error "无法获取索引状态"
        return 1
    fi
    
    echo "$indices_data"
    
    # 检查是否有红色或黄色索引
    local red_indices=$(echo "$indices_data" | grep -c "red" || true)
    local yellow_indices=$(echo "$indices_data" | grep -c "yellow" || true)
    
    if [ "$red_indices" -gt 0 ]; then
        log_error "发现 $red_indices 个红色索引"
    fi
    
    if [ "$yellow_indices" -gt 0 ]; then
        log_warn "发现 $yellow_indices 个黄色索引"
    fi
    
    if [ "$red_indices" -eq 0 ] && [ "$yellow_indices" -eq 0 ]; then
        log_info "所有索引状态正常"
    fi
}

# 检查节点资源使用情况
check_node_resources() {
    local node=$1
    local url="http://$node/_nodes/stats"
    
    log_header "节点资源使用情况"
    
    local stats_data=$(curl -s "$url")
    
    if [ $? -ne 0 ]; then
        log_error "无法获取节点统计信息"
        return 1
    fi
    
    # 解析JVM堆内存使用情况
    local heap_used=$(echo "$stats_data" | jq -r '.nodes | to_entries[0].value.jvm.mem.heap_used_percent')
    local disk_usage=$(echo "$stats_data" | jq -r '.nodes | to_entries[0].value.fs.total.available_in_bytes')
    
    echo "JVM堆内存使用率: ${heap_used}%"
    
    if [ "$heap_used" -gt 80 ]; then
        log_warn "JVM堆内存使用率较高: ${heap_used}%"
    elif [ "$heap_used" -gt 90 ]; then
        log_error "JVM堆内存使用率过高: ${heap_used}%"
    else
        log_info "JVM堆内存使用率正常: ${heap_used}%"
    fi
}

# 检查监控端点
check_monitoring_endpoints() {
    log_header "监控端点检查"
    
    # 检查 elasticsearch_exporter
    local exporter_hosts=("SZ-ES-001:9114" "SZ-MONT-002:9114" "HK-MG-002:9114")
    
    for host in "${exporter_hosts[@]}"; do
        echo "检查 elasticsearch_exporter: $host"
        if curl -s --connect-timeout 5 "http://$host/metrics" > /dev/null; then
            log_info "  ✓ Exporter 正常"
        else
            log_error "  ✗ Exporter 不可达"
        fi
    done
}

# 主函数
main() {
    echo "========================================"
    echo "Elasticsearch 集群健康检查"
    echo "检查时间: $(date)"
    echo "========================================"
    echo ""
    
    # 检查 jq 是否安装
    if ! command -v jq &> /dev/null; then
        log_error "jq 未安装，请先安装 jq"
        exit 1
    fi
    
    # 检查各个节点
    log_header "节点连通性检查"
    local healthy_nodes=0
    for node in "${ES_NODES[@]}"; do
        if check_node_health "$node"; then
            ((healthy_nodes++))
        fi
        echo ""
    done
    
    log_info "健康节点数: $healthy_nodes/${#ES_NODES[@]}"
    echo ""
    
    # 如果有健康节点，进行进一步检查
    if [ $healthy_nodes -gt 0 ]; then
        # 使用第一个健康节点进行集群检查
        for node in "${ES_NODES[@]}"; do
            if curl -s --connect-timeout 5 "http://$node" > /dev/null; then
                check_cluster_health "$node"
                echo ""
                check_indices_status "$node"
                echo ""
                check_node_resources "$node"
                echo ""
                break
            fi
        done
    else
        log_error "所有节点都不可达！"
        exit 1
    fi
    
    # 检查监控端点
    check_monitoring_endpoints
    
    echo ""
    log_info "健康检查完成"
}

# 执行主函数
main "$@"
