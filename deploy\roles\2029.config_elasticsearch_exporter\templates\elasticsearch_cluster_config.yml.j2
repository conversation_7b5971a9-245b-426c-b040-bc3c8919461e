# Elasticsearch Cluster Configuration
cluster:
  name: {{ elasticsearch_cluster_name | default('production-cluster') }}
  nodes:
{% for node in elasticsearch_cluster_nodes %}
    - {{ node }}
{% endfor %}

monitoring:
  interval: {{ elasticsearch_monitoring_interval | default('30s') }}
  timeout: {{ elasticsearch_exporter_es_timeout | default('5s') }}

# Metrics collection settings
metrics:
  cluster_health: true
  cluster_stats: true
  indices_stats: true
  node_stats: true
  shards: true
  snapshots: true
