{{ansible_managed|comment}}
http:
  middlewares:
    redirectIgoldhorseCnToCom:
      redirectRegex:
        permanent: true
        regex: "^https://XX.igoldhorse.cn/(.*)"
        replacement: "https://XX.igoldhorse.com/${1}"
    test-compress-int:
      compress:
        minResponseBodyBytes: 1800000
    hkProxy001IpWhiteListInt:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "***************"
        - "*************"
        - "**************"
        # tongyu
        - "*************"
        - "**************"
        - "***************"
        - "*************"
        - "**************"
        - "*************"
        - "*************"
        - "*************"
        - "************"
        - "*************"
        - "**************"
        - "************"
        # tiger
        - "*************"
        - "***************"
        #Easytrade
        - "*************"
        #Lcm
        - "**************"
  routers:
    openapi-int.easyview.com.hk:
      service: hkProxy001Preproxy
      rule: Host(`openapi-int.easyview.com.hk`)
      middlewares:
      - hkProxy001IpWhiteListInt
      - test-compress-int
      tls:
        certResolver: httpResolver
        options: modern
        domains:
        - main: openapi-int.easyview.com.hk
  services:
    hkProxy001Preproxy:
      loadBalancer:
        servers:
        - url: http://HK-PROXY-001:8210
        - url: http://HK-MG-002:8210
tls:
  certificates:
  - certFile: /etc/traefik/ssl/easyview.com.hk_bundle.crt
    keyFile: /etc/traefik/ssl/easyview.com.hk.key