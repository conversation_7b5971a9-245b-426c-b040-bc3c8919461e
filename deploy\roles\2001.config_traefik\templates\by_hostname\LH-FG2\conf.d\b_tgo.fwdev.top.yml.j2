{{ansible_managed|comment}}
tcp:
  routers:
    tgo:
      service: tgo
      rule: HostSNI(`tgo.fwdev.top`, `tgo.tanghui.work`)
      tls:
        passthrough: true
    vl:
      service: vl
      rule: HostSNI(`vl.fwdev.top`, `vl.tanghui.work`)
      tls:
        passthrough: true
  services:
    tgo:
      loadBalancer:
        servers:
        - address: 127.0.0.1:33333
    vl:
      loadBalancer:
        servers:
        - address: 127.0.0.1:55555
