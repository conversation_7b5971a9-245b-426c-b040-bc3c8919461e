#!/bin/bash

# 检查Elasticsearch Exporter二进制文件和权限

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 检查二进制文件
check_binary() {
    local host="$1"
    
    log_info "检查 $host 上的二进制文件..."
    
    # 检查二进制文件是否存在
    local binary_exists=$(ansible -i hosts.ini "$host" -m shell -a "test -f /etc/elasticsearch_exporter/bin/elasticsearch_exporter && echo 'exists' || echo 'not found'" --become 2>/dev/null | grep -v "SUCCESS" || echo "not found")
    
    if [ "$binary_exists" = "exists" ]; then
        log_info "  ✓ 二进制文件存在"
        
        # 检查文件权限
        local permissions=$(ansible -i hosts.ini "$host" -m shell -a "ls -la /etc/elasticsearch_exporter/bin/elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "")
        echo "    权限: $permissions"
        
        # 检查是否可执行
        local executable=$(ansible -i hosts.ini "$host" -m shell -a "test -x /etc/elasticsearch_exporter/bin/elasticsearch_exporter && echo 'executable' || echo 'not executable'" --become 2>/dev/null | grep -v "SUCCESS" || echo "not executable")
        
        if [ "$executable" = "executable" ]; then
            log_info "  ✓ 文件可执行"
        else
            log_error "  ✗ 文件不可执行"
        fi
        
        # 检查文件所有者
        local owner=$(ansible -i hosts.ini "$host" -m shell -a "stat -c '%U:%G' /etc/elasticsearch_exporter/bin/elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "unknown")
        echo "    所有者: $owner"
        
        # 测试二进制文件是否能运行
        log_info "  测试二进制文件..."
        local version_check=$(ansible -i hosts.ini "$host" -m shell -a "/etc/elasticsearch_exporter/bin/elasticsearch_exporter --version" --become 2>/dev/null | grep -v "SUCCESS" || echo "")
        
        if [ -n "$version_check" ]; then
            log_info "  ✓ 二进制文件可正常运行"
            echo "    版本信息: $version_check"
        else
            log_error "  ✗ 二进制文件无法运行"
            
            # 检查依赖
            log_info "  检查依赖库..."
            ansible -i hosts.ini "$host" -m shell -a "ldd /etc/elasticsearch_exporter/bin/elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" | head -10
        fi
        
        # 测试帮助信息
        log_info "  获取帮助信息..."
        local help_info=$(ansible -i hosts.ini "$host" -m shell -a "/etc/elasticsearch_exporter/bin/elasticsearch_exporter --help 2>&1 | head -5" --become 2>/dev/null | grep -v "SUCCESS" || echo "")
        if [ -n "$help_info" ]; then
            echo "    帮助信息: $help_info"
        fi
        
    else
        log_error "  ✗ 二进制文件不存在"
        
        # 检查目录是否存在
        local dir_exists=$(ansible -i hosts.ini "$host" -m shell -a "test -d /etc/elasticsearch_exporter && echo 'exists' || echo 'not found'" --become 2>/dev/null | grep -v "SUCCESS" || echo "not found")
        
        if [ "$dir_exists" = "exists" ]; then
            log_info "  目录存在，检查内容..."
            ansible -i hosts.ini "$host" -m shell -a "ls -la /etc/elasticsearch_exporter/" --become 2>/dev/null | grep -v "SUCCESS"
        else
            log_error "  目录不存在: /etc/elasticsearch_exporter"
        fi
    fi
    
    echo ""
}

# 检查用户和组
check_user_group() {
    local host="$1"
    
    log_info "检查 $host 上的用户和组..."
    
    # 检查prometheus用户是否存在
    local user_exists=$(ansible -i hosts.ini "$host" -m shell -a "id prometheus" --become 2>/dev/null | grep -v "SUCCESS" || echo "user not found")
    
    if echo "$user_exists" | grep -q "uid="; then
        log_info "  ✓ prometheus用户存在"
        echo "    用户信息: $user_exists"
    else
        log_error "  ✗ prometheus用户不存在"
    fi
    
    # 检查prometheus组是否存在
    local group_exists=$(ansible -i hosts.ini "$host" -m shell -a "getent group prometheus" --become 2>/dev/null | grep -v "SUCCESS" || echo "group not found")
    
    if echo "$group_exists" | grep -q "prometheus"; then
        log_info "  ✓ prometheus组存在"
        echo "    组信息: $group_exists"
    else
        log_error "  ✗ prometheus组不存在"
    fi
    
    echo ""
}

# 检查网络连接
check_network() {
    local host="$1"
    
    log_info "检查 $host 的网络连接..."
    
    # 检查到ES的连接
    local es_nodes=("*************:9200" "*************:9200" "*************:9200")
    
    for node in "${es_nodes[@]}"; do
        local connection=$(ansible -i hosts.ini "$host" -m shell -a "curl -s --connect-timeout 5 http://$node && echo 'OK' || echo 'FAIL'" --become 2>/dev/null | grep -v "SUCCESS" | tail -1 || echo "FAIL")
        
        if [ "$connection" = "OK" ]; then
            log_info "  ✓ 连接到 $node 正常"
        else
            log_warn "  ⚠ 连接到 $node 失败"
        fi
    done
    
    echo ""
}

# 手动测试elasticsearch_exporter
manual_test() {
    local host="$1"
    
    log_info "在 $host 上手动测试elasticsearch_exporter..."
    
    # 检查二进制文件是否存在
    local binary_exists=$(ansible -i hosts.ini "$host" -m shell -a "test -f /etc/elasticsearch_exporter/bin/elasticsearch_exporter && echo 'exists' || echo 'not found'" --become 2>/dev/null | grep -v "SUCCESS" || echo "not found")
    
    if [ "$binary_exists" = "exists" ]; then
        log_info "  手动运行测试（5秒）..."
        
        # 手动运行elasticsearch_exporter（后台运行5秒后杀死）
        ansible -i hosts.ini "$host" -m shell -a "timeout 5s /etc/elasticsearch_exporter/bin/elasticsearch_exporter --web.listen-address=0.0.0.0:9115 --es.uri=http://*************:9200 --log.level=debug 2>&1 || echo 'Test completed'" --become 2>/dev/null | grep -v "SUCCESS"
        
        echo ""
        log_info "  如果上面有错误信息，这可能是问题的原因"
    else
        log_error "  二进制文件不存在，无法进行手动测试"
    fi
    
    echo ""
}

# 重新安装二进制文件
reinstall_binary() {
    local host="$1"
    
    log_info "在 $host 上重新安装elasticsearch_exporter..."
    
    # 运行安装任务
    ansible-playbook -i hosts.ini 2029.elasticsearch_exporter.playbook.yml --tags install --limit "$host"
    
    if [ $? -eq 0 ]; then
        log_info "  ✓ 重新安装完成"
    else
        log_error "  ✗ 重新安装失败"
    fi
    
    echo ""
}

# 生成诊断报告
generate_diagnostic() {
    log_header "生成诊断报告"
    
    local report_file="/tmp/elasticsearch_exporter_diagnostic.txt"
    
    cat > "$report_file" << EOF
Elasticsearch Exporter诊断报告
生成时间: $(date)

=== 问题描述 ===
elasticsearch_exporter服务启动失败，退出代码为1

=== 可能原因 ===
1. 二进制文件不存在或损坏
2. 权限问题（prometheus用户无法执行）
3. 依赖库缺失
4. 配置参数错误
5. Elasticsearch连接问题

=== 检查结果 ===
EOF

    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        echo "" >> "$report_file"
        echo "$host:" >> "$report_file"
        
        # 检查二进制文件
        local binary_exists=$(ansible -i hosts.ini "$host" -m shell -a "test -f /etc/elasticsearch_exporter/bin/elasticsearch_exporter && echo 'exists' || echo 'not found'" --become 2>/dev/null | grep -v "SUCCESS" || echo "not found")
        echo "  二进制文件: $binary_exists" >> "$report_file"
        
        if [ "$binary_exists" = "exists" ]; then
            # 检查权限
            local permissions=$(ansible -i hosts.ini "$host" -m shell -a "ls -la /etc/elasticsearch_exporter/bin/elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "")
            echo "  权限: $permissions" >> "$report_file"
            
            # 检查可执行性
            local executable=$(ansible -i hosts.ini "$host" -m shell -a "test -x /etc/elasticsearch_exporter/bin/elasticsearch_exporter && echo 'executable' || echo 'not executable'" --become 2>/dev/null | grep -v "SUCCESS" || echo "not executable")
            echo "  可执行: $executable" >> "$report_file"
        fi
        
        # 检查用户
        local user_exists=$(ansible -i hosts.ini "$host" -m shell -a "id prometheus" --become 2>/dev/null | grep -v "SUCCESS" || echo "user not found")
        if echo "$user_exists" | grep -q "uid="; then
            echo "  prometheus用户: 存在" >> "$report_file"
        else
            echo "  prometheus用户: 不存在" >> "$report_file"
        fi
        
        # 检查ES连接
        local es_connection=$(ansible -i hosts.ini "$host" -m shell -a "curl -s --connect-timeout 5 http://*************:9200 && echo 'OK' || echo 'FAIL'" --become 2>/dev/null | grep -v "SUCCESS" | tail -1 || echo "FAIL")
        echo "  ES连接: $es_connection" >> "$report_file"
    done
    
    echo "" >> "$report_file"
    echo "=== 建议解决方案 ===" >> "$report_file"
    echo "1. 如果二进制文件不存在，运行: ansible-playbook -i hosts.ini 2029.elasticsearch_exporter.playbook.yml --tags install" >> "$report_file"
    echo "2. 如果权限问题，运行: ansible -i hosts.ini 'all' -m shell -a 'chown prometheus:prometheus /etc/elasticsearch_exporter/bin/elasticsearch_exporter && chmod +x /etc/elasticsearch_exporter/bin/elasticsearch_exporter' --become" >> "$report_file"
    echo "3. 如果用户不存在，运行: ansible -i hosts.ini 'all' -m user -a 'name=prometheus system=yes shell=/bin/false home=/var/lib/prometheus createhome=no' --become" >> "$report_file"
    echo "4. 如果ES连接失败，检查Elasticsearch服务状态和网络连接" >> "$report_file"
    
    log_info "诊断报告已生成: $report_file"
    cat "$report_file"
}

# 主函数
main() {
    echo "========================================"
    echo "Elasticsearch Exporter二进制文件检查"
    echo "检查时间: $(date)"
    echo "========================================"
    echo ""
    
    # 检查前置条件
    if [ ! -f "hosts.ini" ]; then
        log_error "hosts.ini 文件不存在"
        exit 1
    fi
    
    if ! command -v ansible &> /dev/null; then
        log_error "ansible 未安装"
        exit 1
    fi
    
    # 执行检查
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        log_header "检查 $host"
        check_binary "$host"
        check_user_group "$host"
        check_network "$host"
        manual_test "$host"
    done
    
    generate_diagnostic
    
    log_info "检查完成！"
    echo ""
    echo "如果发现问题，可以尝试:"
    echo "1. 重新安装: ansible-playbook -i hosts.ini 2029.elasticsearch_exporter.playbook.yml --tags install"
    echo "2. 修复权限: ansible -i hosts.ini 'all' -m shell -a 'chown prometheus:prometheus /etc/elasticsearch_exporter/bin/elasticsearch_exporter && chmod +x /etc/elasticsearch_exporter/bin/elasticsearch_exporter' --become"
    echo "3. 使用简化配置: ./scripts/fix_elasticsearch_exporter_simple.sh"
}

# 执行主函数
main "$@"
