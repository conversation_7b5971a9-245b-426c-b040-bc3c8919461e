.init_before_script: &init_before_script
- command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )
- ssh-agent -s > SSH-AGENT
- eval $(cat SSH-AGENT)
- echo "${DEPLOY_BOT_SSH_PRIVATE_KEY}" | base64 -d -w0 | tr -d '\r' | ssh-add -
- echo "${PROD_DEPLOY_SSH_PRIVATE_KEY}" | base64 -d -w0 | tr -d '\r' | ssh-add -
- echo "${ANSIBLE_VAULT_PASSWORD_PROD_INFRA}" | base64 -d > "${ANSIBLE_VAULT_PASSWORD_FILE}"
- echo $(sha256sum "${ANSIBLE_VAULT_PASSWORD_FILE}")
- echo "${ID_ED25519_GH_PROD_GHCI_SSH_KEY}" | base64 -d -w0 | tr -d '\r' > ssh.key
- chmod 400 ssh.key
- echo ${ID_ED25519_GH_PROD_GHCI_SSH_PASSPHRASE} | base64 -d -w0 | tr -d '\r' > SSH_PASSPHRASE
- chmod 400 SSH_PASSPHRASE
- sshpass -f SSH_PASSPHRASE -P 'key:' ssh-add ssh.key
- export ANSIBLE_INVENTORY=hosts.ini
- export ANSIBLE_HOST_KEY_CHECKING=false
- |
  cat > /etc/ansible/ansible.cfg << EOF
  [ssh_connection]
  ssh_args =
    -o ControlMaster=auto
    -o ControlPersist=60s
    -o PreferredAuthentications=password
    -o PasswordAuthentication=yes
    -o StrictHostKeyChecking=accept-new
    -o ProxyCommand="ssh -o StrictHostKeyChecking=no -W %h:%p -q -p 22222 ghci@************"
  EOF

variables:
  SSHPASS:
    description: 传给 sshpass 的密码
  ANSIBLE_INVENTORY:
    description: inventory 文件
    value: hosts.ini
  DEPLOY_NORMALIZE_HOSTS:
    description: 要标准化的主机名，见 deploy/hosts.ini 中的定义
  ANSIBLE_VAULT_PASSWORD_FILE_not_ready:
    description: vault 的密码文件

1001.init_users:
  only:
  - main
  stage: normalize
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *init_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    1002.create_user.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_NORMALIZE_HOSTS=${DEPLOY_NORMALIZE_HOSTS:-not_init_user} \
    -u ${ANSIBLE_USER:-root} \
    -b -k || sleep 3600


.ghci_before_script: &ghci_before_script
- command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )
- ssh-agent -s > SSH-AGENT
- eval $(cat SSH-AGENT)
- echo "${PROD_DEPLOY_SSH_PRIVATE_KEY}" | base64 -d -w0 | tr -d '\r' | ssh-add -
- echo "${ANSIBLE_VAULT_PASSWORD_PROD_INFRA}" | base64 -d > "${ANSIBLE_VAULT_PASSWORD_FILE}"
- echo $(sha256sum "${ANSIBLE_VAULT_PASSWORD_FILE}")
- echo "${ID_ED25519_GH_PROD_GHCI_SSH_KEY}" | base64 -d -w0 | tr -d '\r' > ssh.key
- chmod 400 ssh.key
- echo ${ID_ED25519_GH_PROD_GHCI_SSH_PASSPHRASE} | base64 -d -w0 | tr -d '\r' > SSH_PASSPHRASE
- chmod 400 SSH_PASSPHRASE
- sshpass -f SSH_PASSPHRASE -P 'key:' ssh-add ssh.key
- export ANSIBLE_INVENTORY=hosts.ini
- export ANSIBLE_HOST_KEY_CHECKING=false
- export ANSIBLE_BECOME_PASS=$(echo $ANSIBLE_BECOME_PASS_BASE64 | base64 -d -w0 | tr -d '\r')

1002.update_users:
  only:
  - main
  stage: normalize
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    1002.create_user.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_NORMALIZE_HOSTS=${DEPLOY_NORMALIZE_HOSTS:-all_servers} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

1003.install_package:
  only:
  - main
  stage: normalize
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    1003.install_package.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_NORMALIZE_HOSTS=${DEPLOY_NORMALIZE_HOSTS:-all_servers} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

1004.init_vdb_filesystem:
  only:
  - main
  stage: normalize
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    1004.init_vdb.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_INIT_VDB_HOSTS=${DEPLOY_INIT_VDB_HOSTS:-not_init_vdb} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

10041.extend_vdb_filesystem:
  only:
  - main
  stage: normalize
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    10041.extend_vdb.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_EXTEND_VDB_HOSTS=${DEPLOY_EXTEND_VDB_HOSTS:-to_extend_vdb} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

1005.server_normalize:
  only:
  - main
  stage: normalize
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    1005.server_normalize.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_NORMALIZE_HOSTS=${DEPLOY_NORMALIZE_HOSTS:-all_servers} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

10051.server_hosts:
  only:
  - main
  stage: normalize
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    10051.server_hosts.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_NORMALIZE_HOSTS=${DEPLOY_NORMALIZE_HOSTS:-all_servers} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

1006.supervisor_centos:
  only:
  - main
  stage: normalize
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    1006.supervisor_centos.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_CENTOSSUPERVISOR_HOSTS=${DEPLOY_CENTOSSUPERVISOR_HOSTS:-SZ-PUSH-001,SZ-ES-001,SZ-CVT-001,SZ-REC-001,SZ-CAL-001,SZ-TASK-001} \
    -u ${ANSIBLE_USER:-ghci} \
    -b
