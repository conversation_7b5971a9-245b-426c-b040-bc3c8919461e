---
- name: create json_exporter system group
  group:
    name: json_exporter
    system: true
    state: present
    gid: 3245

- name: create data directory
  file:
    path: /data
    state: directory
    owner: root
    group: root
    mode: 0755

- name: create json_exporter system user
  user:
    name: json_exporter
    system: true
    shell: "/usr/sbin/nologin"
    group: json_exporter
    createhome: false
    home: "{{ JSON_EXPORTER_DB_DIR }}"
    uid: 3245

- name: create json_exporter data directory
  file:
    path: "{{ JSON_EXPORTER_DB_DIR }}"
    state: directory
    recurse: true
    owner: json_exporter
    group: json_exporter
    mode: 0755

- name: create json_exporter configuration directories
  file:
    path: "{{ item }}"
    state: directory
    owner: root
    group: json_exporter
    mode: 0770
  with_items:
  - "{{ JSON_EXPORTER_CONFIG_DIR }}"

- name: json_exporter url
  debug:
    msg: "{{JSON_EXPORTER_DOWNLOAD_URL}}"

- name: download json_exporter binary to local folder
  become: false
  get_url:
    url: "{{JSON_EXPORTER_DOWNLOAD_URL}}"
    dest: "/tmp/json_exporter-{{JSON_EXPORTER_VERSION}}.tar.gz"
    checksum: "{{JSON_EXPORTER_CHECKSUM}}"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: install unzip
  package:
    name: unzip
    state: present

- name: unpack json_exporter binaries
  unarchive:
    src: "/tmp/json_exporter-{{JSON_EXPORTER_VERSION}}.tar.gz"
    dest: "/tmp"
    remote_src: true
  check_mode: false

- name: copy official json_exporter binaries
  copy:
    src: "/tmp/json_exporter-{{JSON_EXPORTER_VERSION}}.linux-amd64/{{item}}"
    dest: "/usr/local/bin/{{item}}"
    mode: '755'
    owner: root
    group: root
    remote_src: true
  with_items:
  - json_exporter
  notify:
  - restart json_exporter

- name: create systemd service unit
  template:
    src: json_exporter.service.j2
    dest: /etc/systemd/system/json_exporter.service
    owner: root
    group: root
    mode: 0644
  notify:
  - restart json_exporter

- name: ensure start json_exporter on boot
  systemd:
    name: json_exporter
    daemon_reload: true
    enabled: true
