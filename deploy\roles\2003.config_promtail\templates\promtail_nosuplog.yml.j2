# file: /etc/promtail/config.yml
{{ ansible_managed | comment }}

server:
  http_listen_port: {{PROMTAIL_HTTP_PORT}}
  grpc_listen_port: 0
positions:
  filename: "{{PROMTAIL_CONFIG_DIR}}/positions.yaml"
clients:
- url: {{LOKI_API_URL}}
  external_labels:
    host: {{ansible_hostname}}
scrape_configs:
# - job_name: local_file_sd
#   file_sd_configs:
#   - refresh_interval: 10s
#     files:
#     - "{{PROMTAIL_CONFIG_DIR}}/conf.d/*.yml"
- job_name: tmplog
  static_configs:
  - targets:
    - localhost
    labels:
      job: tmplogs
      __path__: /tmp/*log
- job_name: system
  static_configs:
  - targets:
    - localhost
    labels:
      job: varlogs
      __path__: /var/log/*log
- job_name: datalogs
  static_configs:
  - targets:
    - localhost
    labels:
      job: datalogs-info
      __path__: /data/logs/*/*.log
  - targets:
    - localhost
    labels:
      job: datalogs-error
      __path__: /data/logs/*/error/*.log
  - targets:
    - localhost
    labels:
      job: datalogs-warn
      __path__: /data/logs/*/warn/*.log
  - targets:
    - localhost
    labels:
      job: traefik-log
      __path__: /var/log/traefik/access.log
  pipeline_stages:
  - regex:
      source: filename
      expression: /data/logs/(?P<service>.*?)/.*
  - regex:
      expression: '(?P<date>\d{4,4}-\d{2,2}-\d{2,2}) (?P<time>\d{2,2}:\d{2,2}:\d{2,2}.\d{3,3})'
  - regex:
      expression: '(?P<timestamp>\d{4,4}-\d{2,2}-\d{2,2} \d{2,2}:\d{2,2}:\d{2,2}.\d{3,3}) (?P<protocol>[[\s\S]*?]) (?P<level>\w*) (?P<message>((?:.|\n)*))'
  - labels:
      service: service
      level:
      date:
  - timestamp:
      source: timestamp
      location: "Asia/Shanghai"
      format: "2006-01-02 15:04:05.000"
