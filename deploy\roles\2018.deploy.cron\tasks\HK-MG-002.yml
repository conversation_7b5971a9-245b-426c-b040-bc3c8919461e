- name: create jar data directory
  file:
    path: "/data/java-jar-release"
    state: directory
    recurse: true
    owner: wukong
    group: wukong
    mode: 0755

- name: create sync_jar_data.sh
  template:
    src: sync_jar_data.sh.j2
    dest: /data/sync_jar_data.sh
    owner: wukong
    group: wukong
    mode: 0755
    
- name: Ensure a rsync jar job1 that runs 
  cron:
    name: "rsyn_jar_job1"
    job: "{{jar_cron_inline1}}"
    user: wukong

- name: Ensure a rsync jar job2 that runs 
  cron:
    name: "rsyn_jar_job2"
    job: "{{jar_cron_inline2}}"
    user: wukong
