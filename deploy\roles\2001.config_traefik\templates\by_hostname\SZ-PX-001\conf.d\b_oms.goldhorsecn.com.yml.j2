{{ansible_managed|comment}}
http:
  middlewares:
    szBackendOmsIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    oms-center.goldhorsecn.com:
      service: szBackendOms
      rule: Host(`oms-center.goldhorsecn.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
        domains:
        - main: oms-center.goldhorsecn.com
  services:
    szBackendOms:
      loadBalancer:
        servers:
        - url: http://SZ-NEWS-001:8251
