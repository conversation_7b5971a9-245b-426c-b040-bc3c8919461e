---
#- name: installing RabbitMQ server
#  apt:
#    name:
#      - rabbitmq-server
#    state: present
#    update_cache: true

- name: "Import Erlang/OTP GPG signing key"
  apt_key:
    url: "{{ erlang_deb_gpg_url }}"
    state: present
    validate_certs: false
  register: _add_apt_key
  until: _add_apt_key is succeeded
  retries: 5
  delay: 2

- name: "Add Erlang/OTP repository"
  template:
    src: "{{ erlang_deb_repo_tpl }}"
    dest: "/etc/apt/sources.list.d/{{ erlang_deb_repo_tpl | basename | regex_replace('\\.j2$', '') }}"
    force: true
    backup: true

- name: "Add Erlang/OTP repository pinning preferences"
  template:
    src: "{{ erlang_deb_pinning_tpl }}"
    dest: "/etc/apt/preferences.d/{{ erlang_deb_pinning_tpl | basename | regex_replace('\\.j2$', '') }}"
    force: true
    backup: true

- name: "Update cache"
  apt:
    update_cache: true
  changed_when: false

- name: "Install Erlang"
  apt:
    package:
      - erlang-nox
    state: present
    install_recommends: false

- name: Download RabbitMQ package.
  get_url:
    url: "{{ rabbitmq_deb_url }}"
    dest: "/tmp/{{ rabbitmq_deb }}"

- name: Ensure RabbitMQ is installed.
  apt:
    deb: "/tmp/{{ rabbitmq_deb }}"
    state: present

- name: ensuring that the RabbitMQ service is running
  service:
    daemon_reload: yes
    enabled: yes
    name: rabbitmq-server
    state: started


