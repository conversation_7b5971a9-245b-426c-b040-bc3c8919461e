{{ ansible_managed | comment }}

[Mysql]
Db = "Yearning"
Host = "127.0.0.1"
Port = "33260"
Password = "{{mysql_root_password}}"
User = "root"

[General]
SecretKey = "dbcjqheupqjsuwab"
Hours = 4
Lang = "zh_CN"  # en_US,zh_CN

[Oidc]
Enable = false
ClientId = "yearning"
ClientSecret = "fefehelj23jlj22f3jfjdfd"
Scope = "openid profile"
AuthUrl = "https://keycloak.xxx.ca/auth/realms/master/protocol/openid-connect/auth"
TokenUrl = "https://keycloak.xxx.ca/auth/realms/master/protocol/openid-connect/token"
UserUrl = "https://keycloak.xxx.ca/auth/realms/master/protocol/openid-connect/userinfo"
RedirectUrL = "http://127.0.0.1:8858/oidc/_token-login"
UserNameKey = "preferred_username"
RealNameKey = "name"
EmailKey = "email"
SessionKey = "session_state"
