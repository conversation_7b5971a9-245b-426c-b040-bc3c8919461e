---
- name: find vdb1
  shell:
    cmd: lsblk /dev/vdb | grep vdb1
  register: found_vdb1
  ignore_errors: true

- name: find /data
  shell:
    cmd: lsblk | grep /data
  register: found_data
  ignore_errors: true

- name: create partition vdb1
  shell:
    cmd: echo -e 'g\nn\np\n1\n\n\nw' | fdisk /dev/vdb
  when: found_vdb1 is not succeeded and found_data is not succeeded
  register: create_vdb1
  ignore_errors: true

- name: format vdb1
  shell:
    cmd: mkfs.xfs /dev/vdb1
  when: create_vdb1 is succeeded
  register: format_vdb1
  ignore_errors: true

- name: create data directory
  file:
    path: /data
    state: directory

- name: mount
  mount:
    src: /dev/vdb1
    state: mounted
    path: /data
    fstype: xfs
    backup: true
  when: format_vdb1 is succeeded
  ignore_errors: true
