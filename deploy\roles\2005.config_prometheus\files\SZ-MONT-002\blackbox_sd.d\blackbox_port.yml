#- labels:
#    job: port_status
#    type: probe
#  targets:
#  - SZ-PX-001:56349
#  - SZ-PX-001:28282
#  - SZ-PUSH-001:15692
- labels:
    instance: caddy-jarsync
    job: port_status
    type: probe
  targets:
  - SZ-ES-001:32709
- labels:
    instance: biz-platform-admin-workflow
    job: port_status
    type: probe
  targets:
  - SZ-OPACC-001:8102
- labels:
    instance: biz-platform-admin-open-account
    job: port_status
    type: probe
  targets:
  - SZ-OPACC-001:8103
- labels:
    instance: biz-platform-proxy-service
    job: port_status
    type: probe
  targets:
  - SZ-OPACC-001:8104
- labels:
    instance: biz-platform-app
    job: port_status
    type: probe
  targets:
  - SZ-OPACC-001:8105
- labels:
    instance: biz-platform-job
    job: port_status
    type: probe
  targets:
  - SZ-OPACC-001:8106
- labels:
    instance: biz-platform-admin-securities-service
    job: port_status
    type: probe
  targets:
  - SZ-OPACC-001:8107
- labels:
    instance: biz-platform-risk-control
    job: port_status
    type: probe
  targets:
  - SZ-OPACC-001:8109
- labels:
    instance: news-convert-core
    job: port_status
    type: probe
  targets:
  - SZ-NEWS-001:8201
- labels:
    instance: news-service-core
    job: port_status
    type: probe
  targets:
  - SZ-NEWS-001:8202
- labels:
    instance: mktquot-convert-hkex
    job: port_status
    type: probe
  targets:
  - SZ-CVT-001:8001
- labels:
    instance: mktquot-convert-us
    job: port_status
    type: probe
  targets:
  - SZ-CVT-001:8006
- labels:
    instance: mktquot-convert-us-bf
    job: port_status
    type: probe
  targets:
  - SZ-CVT-001:8007
- labels:
    instance: mktquot-convert-a
    job: port_status
    type: probe
  targets:
  - SZ-CSC-001:8004
- labels:
    instance: mktquot-receive-a
    job: port_status
    type: probe
  targets:
  - SZ-CSC-002:8015
- labels:
    instance: mktquot-receive-hkex
    job: port_status
    type: probe
  targets:
  - SZ-REC-001:8012
- labels:
    instance: mktquot-receive-us
    job: port_status
    type: probe
  targets:
  - SZ-REC-001:8017
- labels:
    instance: mktquot-receive-us-bf
    job: port_status
    type: probe
  targets:
  - SZ-ES-001:8018
- labels:
    instance: mktquot-service-hk
    job: port_status
    type: probe
  targets:
  - SZ-CAL-001:8021
- labels:
    instance: mktquot-service-us
    job: port_status
    type: probe
  targets:
  - SZ-CAL-001:8022
- labels:
    instance: mktquot-service-a
    job: port_status
    type: probe
  targets:
  - SZ-CSC-001:8020
- labels:
    instance: mktquot-push-hkex
    job: port_status
    type: probe
  targets:
  - SZ-PUSH-001:8010
- labels:
    instance: mktquot-push-us
    job: port_status
    type: probe
  targets:
  - SZ-PUSH-001:8011
- labels:
    instance: mktquot-push-a
    job: port_status
    type: probe
  targets:
  - SZ-CSC-003:8009
#- labels:
#    instance: f10-convert-core
#    job: port_status
#    type: probe
#  targets:
#  - SZ-ES-001:8030
- labels:
    instance: f10-core
    job: port_status
    type: probe
  targets:
  - SZ-ES-001:8041
#  - SZ-NEWS-001:8041
- labels:
    instance: minute-a-core
    job: port_status
    type: probe
  targets:
  - SZ-CSC-003:8052
- labels:
    instance: minute-hkex-core
    job: port_status
    type: probe
  targets:
  - SZ-STORE-001:8047
- labels:
    instance: minute-us-core
    job: port_status
    type: probe
  targets:
  - SZ-STORE-002:8048
- labels:
    instance: mktquot-calculate-core
    job: port_status
    type: probe
  targets:
  - SZ-CAL-001:8024
- labels:
    instance: platform-core
    job: port_status
    type: probe
  targets:
  - SZ-CAL-001:8029
- labels:
    instance: platform-tencent
    job: port_status
    type: probe
  targets:
  - SZ-CAL-001:8051
- labels:
    instance: mktquot-stockcode-core
    job: port_status
    type: probe
  targets:
  - SZ-TASK-001:8028
  - SZ-DERIV-001:8028
- labels:
    instance: mktquot-task
    job: port_status
    type: probe
  targets:
  - SZ-ES-001:8023
- labels:
    instance: mktmgr-core
    job: port_status
    type: probe
  targets:
  - SZ-CAL-001:8031
- labels:
    instance: mktquot-gateway-core
    job: port_status
    type: probe
  targets:
  - SZ-PUSH-001:8000
- labels:
    instance: mktquot-hbase-core
    job: port_status
    type: probe
  targets:
  - SZ-TASK-001:8026
- labels:
    instance: mktquot-optional-core
    job: port_status
    type: probe
  targets:
  - SZ-TASK-001:8027
- labels:
    instance: monitoring-core
    job: port_status
    type: probe
  targets:
  - SZ-PUSH-001:8049
- labels:
    instance: mktquot-service-delay
    job: port_status
    type: probe
  targets:
  - SZ-CSC-003:8043
- labels:
    instance: minute-us-delay
    job: port_status
    type: probe
  targets:
  - SZ-CSC-003:8044
- labels:
    instance: receive-hkex-delay
    job: port_status
    type: probe
  targets:
  - SZ-CSC-001:8014
- labels:
    instance: convert-hkex-delay
    job: port_status
    type: probe
  targets:
  - SZ-CSC-002:8038
- labels:
    instance: delay-calculate-core
    job: port_status
    type: probe
  targets:
  - SZ-CSC-002:8025
- labels:
    instance: mktquot-a-calculate-core
    job: port_status
    type: probe
  targets:
  - SZ-TASK-001:8040
- labels:
    instance: nacos
    job: port_status
    type: probe
  targets:
  - SZ-STORE-003:8848
- labels:
    instance: xxljob
    job: port_status
    type: probe
  targets:
  - SZ-STORE-003:8080
- labels:
    instance: szPush001OpMqttWs
    job: port_status
    type: probe
  targets:
  - SZ-PUSH-001:15675
- labels:
    instance: szPush001OpAccBackendMqtt
    job: port_status
    type: probe
  targets:
  - SZ-PUSH-001:1883
- labels:
    instance: szPush001OpAccBackendAmqp
    job: port_status
    type: probe
  targets:
  - SZ-PUSH-001:5672
- labels:
    instance: szOpacc001Minio
    job: port_status
    type: probe
  targets:
  - SZ-OPACC-001:42924
- labels:
    instance: szNews001Minio
    job: port_status
    type: probe
  targets:
  - SZ-NEWS-001:42924
- labels:
    instance: szgqlDmz
    job: port_status
    type: probe
  targets:
  - SZ-PX-001:46349
- labels:
    instance: szgqlSecret
    job: port_status
    type: probe
  targets:
  - SZ-PX-001:26301
- labels:
    instance: szgqlDmzth
    job: port_status
    type: probe
  targets:
  - SZ-PX-001:56349
- labels:
    instance: szgqlSecretth
    job: port_status
    type: probe
  targets:
  - SZ-PX-001:56301
- labels:
    instance: szgqllpoa
    job: port_status
    type: probe
  targets:
  - SZ-PX-001:38000
- labels:
    instance: szseata
    job: port_status
    type: probe
  targets:
  - SZ-OPACC-001:8091
- labels:
    instance: minute-a-delay-core
    job: port_status
    type: probe
  targets:
  - SZ-MKTAPI-001:8056
- labels:
    instance: mktquot-a-delay-calculate
    job: port_status
    type: probe
  targets:
  - SZ-CSC-003:8057
- labels:
    instance: mktquot-convert-a-delay
    job: port_status
    type: probe
  targets:
  - SZ-TASK-001:8055
- labels:
    instance: mktquot-receive-a-delay
    job: port_status
    type: probe
  targets:
  - SZ-MKTAPI-001:8016
- labels:
    instance: mktquot-convert-us-delay
    job: port_status
    type: probe
  targets:
  - SZ-CSC-003:8054
- labels:
    instance: clickhouse001
    job: port_status
    type: probe
  targets:
  - SZ-CLHOUSE-001:8123
- labels:
    instance: clickhouse002
    job: port_status
    type: probe
  targets:
  - SZ-CLHOUSE-002:8123
- labels:
    instance: clickhouse003
    job: port_status
    type: probe
  targets:
  - SZ-CLHOUSE-003:8123
#- labels:
#    instance: mktquot-struct-core
#    job: port_status
#    type: probe
#  targets:
#  - SZ-MKTAPI-001:8060
#- labels:
#    instance: mktquot-struct-task
#    job: port_status
#    type: probe
#  targets:
#  - SZ-MKTAPI-001:8059
- labels:
    instance: mktquot-task-data
    job: port_status
    type: probe
  targets:
  - SZ-DERIV-001:8032
- labels:
    instance: oms-center-service
    job: port_status
    type: probe
  targets:
  - SZ-NEWS-001:8251
- labels:
    instance: user-center-gateway
    job: port_status
    type: probe
  targets:
  - SZ-NEWS-001:8220
- labels:
    instance: user-center-auth
    job: port_status
    type: probe
  targets:
  - SZ-NEWS-001:8221
- labels:
    instance: user-center-service
    job: port_status
    type: probe
  targets:
  - SZ-NEWS-001:8222
- labels:
    instance: mktquot-push-delay
    job: port_status
    type: probe
  targets:
  - SZ-PUSH-001:8058
- labels:
    instance: mktquot-service-open
    job: port_status
    type: probe
  targets:
  - SZ-CSC-002:8064
  - SZ-CSC-003:8064
- labels:
    instance: mktquot-init
    job: port_status
    type: probe
  targets:
  - SZ-CVT-001:8035
- labels:
    instance: es
    job: port_status
    type: probe
  targets:
#  - SZ-STORE-001:9200
#  - SZ-STORE-002:9200
  - SZ-STORE-003:9200
#- labels:
#    instance: struct-quotation-api
#    job: port_status
#    type: probe
#  targets:
#  - SZ-NEWS-001:8061
#- labels:
#    instance: biz-platform-struct-core
#    job: port_status
#    type: probe
#  targets:
#  - SZ-NEWS-001:8108