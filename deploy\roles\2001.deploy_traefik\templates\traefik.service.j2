# file: /etc/systemd/system/traefik.service
{{ansible_managed | comment}}

[Unit]
Description=Traefik Proxy
Documentation=https://doc.traefik.io/traefik/
ConditionPathExists=/etc/traefik/traefik.yml
After=network.target

[Service]
EnvironmentFile=/etc/traefik/traefik.env
CapabilityBoundingSet=CAP_NET_ADMIN CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_ADMIN CAP_NET_BIND_SERVICE
NoNewPrivileges=true
ExecStart=/usr/local/bin/traefik --configfile /etc/traefik/traefik.yml
User=traefik
Restart=always
RestartSec=3
LimitNOFILE=65535

[Install]
WantedBy=multi-user.target

# EOF
