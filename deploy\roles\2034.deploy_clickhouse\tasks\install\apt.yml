---
#Main installation actions
#https://clickhouse.yandex/docs/en/getting_started/index.html#installation

#- name: Install by APT | Apt-key add repo key
#  apt_key:
#    keyserver: "{{ clickhouse_repo_keyserver }}"
#    id: "{{ clickhouse_repo_key }}"
#  become: true
#  tags: [install]
#
#- name: Install by APT | Remove old repo
#  apt_repository:
#    repo: "{{ clickhouse_repo_old }}"
#    state: absent
#  become: true
#  when: clickhouse_repo_old is defined
#  tags: [install]
#
#- name: Install by APT | Repo installation
#  apt_repository:
#    repo: "{{ clickhouse_repo }}"
#    state: present
#  become: true
#  tags: [install]

#- name: Install by APT | Package installation
#  apt:
#    name: "{{ clickhouse_package | map('regex_replace', '$', '=' + clickhouse_version) | list }}"
#    state: present
#    update_cache: false
#  become: true
#  when: clickhouse_version != 'latest'
#  tags: [install]
#
#- name: Install by APT | Package installation
#  apt:
#    name: "{{ clickhouse_package }}"
#    state: "{{ clickhouse_version }}"
#    update_cache: false
#  become: true
#  when: clickhouse_version == 'latest'
#  tags: [install]
#- name: download clickhouse-common-static binary
#  become: true
#  get_url:
#    use_proxy: '{{USE_PROXY|default(true)}}'
#    url: "https://filescfcdn.fwdev.top/common/clickhouse-common-static_22.2.2.1_amd64.deb"
#    dest: "/opt/clickhouse-common-static_22.2.2.1_amd64.deb"
#  register: _download_binary
#  until: _download_binary is succeeded
#  retries: 5
#  delay: 2
#- name: download clickhouse-server binary
#  become: true
#  get_url:
#    use_proxy: '{{USE_PROXY|default(true)}}'
#    url: "https://filescfcdn.fwdev.top/common/clickhouse-server_22.2.2.1_all.deb"
#    dest: "/opt/clickhouse-server_22.2.2.1_all.deb"
#  register: _download_binary
#  until: _download_binary is succeeded
#  retries: 5
#  delay: 2
- name: download clickhouse binary
  get_url:
    use_proxy: true
    url: "https://filescfcdn.fwdev.top/common/{{item}}"
    dest: "/opt/{{item}}"
  with_items:
  - "clickhouse-common-static_22.2.2.1_amd64.deb"
  - "clickhouse-server_22.2.2.1_all.deb"
  - "clickhouse-client_22.2.2.1_all.deb"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2
- name: install deb on ubuntu
  apt:
    deb: "{{item}}"
  with_items:
  - "/opt/clickhouse-common-static_22.2.2.1_amd64.deb"
  - "/opt/clickhouse-server_22.2.2.1_all.deb"
  - "/opt/clickhouse-client_22.2.2.1_all.deb"
