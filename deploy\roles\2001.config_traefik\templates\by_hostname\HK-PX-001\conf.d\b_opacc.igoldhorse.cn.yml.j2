{{ansible_managed|comment}}
http:
  middlewares:
    redirectOpaccIgoldhorseCnToCom:
      redirectRegex:
        permanent: true
        regex: "^https://opacc.igoldhorse.cn/(.*)"
        replacement: "https://opacc.igoldhorse.com/${1}"
  routers:
    opacc.igoldhorse.cn:
      service: hkPx001CaddyOpAccH5
      rule: Host(`opacc.igoldhorse.cn`,`opacc.igoldhorse.com`)
      middlewares:
      - gzip
      - redirectOpaccIgoldhorseCnToCom
      tls:
        certResolver: httpResolver
        domains:
        - main: opacc.igoldhorse.cn
        - main: opacc.igoldhorse.com
  services:
    hkPx001CaddyOpAccH5:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
