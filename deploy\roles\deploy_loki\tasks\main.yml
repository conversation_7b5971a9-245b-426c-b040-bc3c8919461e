---
- name: create loki system group
  group:
    name: loki
    system: true
    state: present
    gid: 3233

- name: create data directory
  file:
    path: /data
    state: directory
    owner: root
    group: root
    mode: 0755

- name: create loki system user
  user:
    name: loki
    system: true
    shell: "/usr/sbin/nologin"
    group: loki
    createhome: false
    home: "{{ LOKI_DB_DIR }}"
    uid: 3233

- name: create loki data directory
  file:
    path: "{{ LOKI_DB_DIR }}"
    state: directory
    recurse: true
    owner: loki
    group: loki
    mode: 0755

- name: create loki configuration directories
  file:
    path: "{{ item }}"
    state: directory
    owner: root
    group: loki
    mode: 0770
  with_items:
  - "{{ LOKI_CONFIG_DIR }}"

- name: loki url
  debug:
    msg: "{{LOKI_DOWNLOAD_URL}}"

- name: download loki binary to local folder
  become: false
  get_url:
    url: "{{LOKI_DOWNLOAD_URL}}"
    dest: "/tmp/loki-{{LOKI_VERSION}}.zip"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: install unzip
  package:
    name: unzip
    state: present

- name: unpack loki binaries
  unarchive:
    src: "/tmp/loki-{{LOKI_VERSION}}.zip"
    dest: "/tmp"
    remote_src: true
  check_mode: false

- name: copy official loki binaries
  copy:
    src: "/tmp/{{item}}"
    dest: "/usr/local/bin/loki"
    mode: '755'
    owner: root
    group: root
    remote_src: true
  with_items:
  - loki-linux-amd64
  notify:
  - restart loki

- name: create systemd service unit
  template:
    src: loki.service.j2
    dest: /etc/systemd/system/loki.service
    owner: root
    group: root
    mode: 0644
  notify:
  - restart loki

- name: ensure loki start at boot
  systemd:
    name: loki
    enabled: true
    daemon_reload: true

- name: configure loki
  template:
    src: loki.yml.j2
    dest: "{{LOKI_CONFIG_DIR}}/loki.yml"
    force: true
    owner: root
    group: loki
    mode: 0640
    validate: "/usr/local/bin/loki -verify-config -config.file=%s -config.expand-env"
  notify:
  - reload loki
  when: inventory_hostname.startswith('SZ')

- name: configure  hk loki
  template:
    src: loki.yml.hk.j2
    dest: "{{LOKI_CONFIG_DIR}}/loki.yml"
    force: true
    owner: root
    group: loki
    mode: 0640
    validate: "/usr/local/bin/loki -verify-config -config.file=%s -config.expand-env"
  notify:
  - reload loki
  when: inventory_hostname.startswith('HK')