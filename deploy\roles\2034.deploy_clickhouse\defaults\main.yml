---
# defaults file for clickhouse

clickhouse_version: 'latest'
clickhouse_service_ensure: 'started'
clickhouse_service_enable: true

#Flag for using with requirements check
clickhouse_supported: false
#Type of installation (package,source)
clickhouse_setup: 'package'
#Flag for remove clickhouse from host(disabled by default)
clickhouse_remove: false
#Flag for full remove(incl db and config), use clickhouse_remove + clickhouse_remove_full
clickhouse_remove_full: false

clickhouse_profiles_default:
 default:
   max_memory_usage: 10000000000
   use_uncompressed_cache: 0
   load_balancing: random
   max_partitions_per_insert_block: 100
 readonly:
   readonly: 1

clickhouse_profiles_custom: {}

clickhouse_listen_host_default:
  - "::1"
  - "127.0.0.1"

clickhouse_listen_host_custom: []

clickhouse_listen_host: "{{ clickhouse_listen_host_default + clickhouse_listen_host_custom }}"

clickhouse_http_port: 8123

clickhouse_tcp_port: 9000

clickhouse_https_port: 8443
clickhouse_tcp_secure_port: 9440

clickhouse_interserver_http: 9009

clickhouse_networks_default:
  - "::1"
  - "127.0.0.1"

clickhouse_users_default:
  - { name: "default", password: "", networks: "{{ clickhouse_networks_default }}", profile: "default", quota: "default", comment: "Default user for login if user not defined" }
  - { name: "readonly", password: "", networks: "{{ clickhouse_networks_default }}", profile: "readonly", quota: "default", comment: "Example of user with readonly access" }

clickhouse_ready_retries: 3
clickhouse_ready_delay: 5

#Full form { name:'<username>', password: '<plainpassword>', password_sha256_hex: '<sha256password>',
#   networks:"<{} with nets>", profile:'<profile>', quota:'<quota>', dbs: '{} with dbs', comment: '<some comment>'}
#Req: name, password or password_256_hex
clickhouse_users_custom: {}

clickhouse_quotas_intervals_default:
  - { duration: 3600, queries: 0, errors: 0, result_rows: 0, read_rows: 0, execution_time: 0 }

clickhouse_quotas_default:
 - { name: "default", intervals: "{{ clickhouse_quotas_intervals_default }}", comment: "Default quota - count only" }

clickhouse_quotas_custom: {}

clickhouse_dbs_default: []
clickhouse_dbs_custom: []

clickhouse_dbs: "{{ clickhouse_dbs_default + clickhouse_dbs_custom }}"

clickhouse_logger:
  level: trace
  log: "{{ clickhouse_path_logdir }}/clickhouse-server.log"
  errorlog: "{{ clickhouse_path_logdir }}/clickhouse-server.err.log"
  size: 1000M
  count: 10

clickhouse_config_dictionaries_lazy_load: true

clickhouse_config:
  max_connections: 2048
  keep_alive_timeout: 3
  max_concurrent_queries: 100
  uncompressed_cache_size: 8589934592
  mark_cache_size: 5368709120
  builtin_dictionaries_reload_interval: 3600
  dictionaries_lazy_load: "{{ clickhouse_config_dictionaries_lazy_load }}"
  max_session_timeout: 3600
  default_session_timeout: 60

clickhouse_dicts: []

clickhouse_path_base: "/data"

clickhouse_path_user_files: "{{ clickhouse_path_base }}/clickhouse/{{ inventory_hostname }}/user_files/"

clickhouse_mlock_status: false

max_partitions_per_insert_block: 100

clickhouse_kafka_config: []

clickhouse_kafka_topics_config: []

clickhouse_merge_tree_config: []
