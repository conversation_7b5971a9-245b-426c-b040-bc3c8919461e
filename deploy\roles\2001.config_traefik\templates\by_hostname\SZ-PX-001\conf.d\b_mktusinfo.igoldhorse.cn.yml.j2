{{ansible_managed|comment}}
http:
  middlewares:
    szPx001MktIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    mktusinfo.igoldhorse.cn:
      service: szPx001Mktusinfo
      rule: Host(`mktusinfo.igoldhorse.cn`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
  services:
    szPx001Mktusinfo:
      loadBalancer:
        servers:
        - url: http://SZ-CAL-001:8022
