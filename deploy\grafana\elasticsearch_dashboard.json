{"dashboard": {"id": null, "title": "Elasticsearch Cluster Monitoring", "tags": ["elasticsearch", "monitoring"], "timezone": "browser", "panels": [{"id": 1, "title": "Cluster Health Status", "type": "stat", "targets": [{"expr": "elasticsearch_cluster_health_status{color=\"green\"}", "legendFormat": "Green", "refId": "A"}, {"expr": "elasticsearch_cluster_health_status{color=\"yellow\"}", "legendFormat": "Yellow", "refId": "B"}, {"expr": "elasticsearch_cluster_health_status{color=\"red\"}", "legendFormat": "Red", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.5}, {"color": "red", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Cluster Nodes", "type": "stat", "targets": [{"expr": "elasticsearch_cluster_health_number_of_nodes", "legendFormat": "Total Nodes", "refId": "A"}, {"expr": "elasticsearch_cluster_health_number_of_data_nodes", "legendFormat": "Data Nodes", "refId": "B"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Shards Status", "type": "graph", "targets": [{"expr": "elasticsearch_cluster_health_active_shards", "legendFormat": "Active Shards", "refId": "A"}, {"expr": "elasticsearch_cluster_health_unassigned_shards", "legendFormat": "Unassigned Shards", "refId": "B"}, {"expr": "elasticsearch_cluster_health_initializing_shards", "legendFormat": "Initializing Shards", "refId": "C"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}, {"id": 4, "title": "JVM Heap Usage", "type": "graph", "targets": [{"expr": "(elasticsearch_jvm_memory_used_bytes{area=\"heap\"} / elasticsearch_jvm_memory_max_bytes{area=\"heap\"}) * 100", "legendFormat": "Heap Usage % - {{instance}}", "refId": "A"}], "yAxes": [{"max": 100, "min": 0, "unit": "percent"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "Disk Usage", "type": "graph", "targets": [{"expr": "(elasticsearch_filesystem_data_size_bytes - elasticsearch_filesystem_data_available_bytes) / elasticsearch_filesystem_data_size_bytes * 100", "legendFormat": "Disk Usage % - {{instance}}", "refId": "A"}], "yAxes": [{"max": 100, "min": 0, "unit": "percent"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 6, "title": "Documents Count", "type": "graph", "targets": [{"expr": "elasticsearch_indices_docs", "legendFormat": "Documents - {{index}}", "refId": "A"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}