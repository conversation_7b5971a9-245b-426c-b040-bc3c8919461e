#!/bin/bash

# Elasticsearch 监控部署脚本
# 用法: ./deploy_elasticsearch_monitoring.sh [target_hosts]

set -e

# 默认目标主机
DEFAULT_HOSTS="SZ-ES-001,SZ-MONT-002,HK-MG-002"
TARGET_HOSTS="${1:-$DEFAULT_HOSTS}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查 ansible 是否安装
    if ! command -v ansible-playbook &> /dev/null; then
        log_error "ansible-playbook 未找到，请先安装 Ansible"
        exit 1
    fi
    
    # 检查 hosts.ini 文件
    if [ ! -f "hosts.ini" ]; then
        log_error "hosts.ini 文件未找到"
        exit 1
    fi
    
    # 检查 playbook 文件
    if [ ! -f "2029.elasticsearch_exporter.playbook.yml" ]; then
        log_error "Elasticsearch exporter playbook 未找到"
        exit 1
    fi
    
    log_info "前置条件检查通过"
}

# 验证 ES 集群连通性
check_elasticsearch_connectivity() {
    log_info "检查 Elasticsearch 集群连通性..."
    
    ES_NODES=("*************:9200" "*************:9200" "*************:9200")
    
    for node in "${ES_NODES[@]}"; do
        if curl -s --connect-timeout 5 "http://$node/_cluster/health" > /dev/null; then
            log_info "✓ ES 节点 $node 连通正常"
        else
            log_warn "✗ ES 节点 $node 连通失败"
        fi
    done
}

# 部署 Elasticsearch Exporter
deploy_exporter() {
    log_info "开始部署 Elasticsearch Exporter 到: $TARGET_HOSTS"
    
    ansible-playbook -i hosts.ini 2029.elasticsearch_exporter.playbook.yml \
        -e "DEPLOY_ELASTICSEARCH_EXPORTER_HOSTS=$TARGET_HOSTS" \
        --diff
    
    if [ $? -eq 0 ]; then
        log_info "Elasticsearch Exporter 部署成功"
    else
        log_error "Elasticsearch Exporter 部署失败"
        exit 1
    fi
}

# 更新 Prometheus 配置
update_prometheus_config() {
    log_info "更新 Prometheus 配置..."
    
    # 重载 Prometheus 配置
    ansible-playbook -i hosts.ini 2005.prometheus.playbook.yml \
        --tags config \
        --diff
    
    if [ $? -eq 0 ]; then
        log_info "Prometheus 配置更新成功"
    else
        log_error "Prometheus 配置更新失败"
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署结果..."
    
    # 检查服务状态
    ansible -i hosts.ini "$TARGET_HOSTS" -m shell -a "systemctl is-active elasticsearch_exporter" \
        --become
    
    # 检查指标端点
    ansible -i hosts.ini "$TARGET_HOSTS" -m uri -a "url=http://localhost:9114/metrics" \
        --become
    
    log_info "部署验证完成"
}

# 显示后续步骤
show_next_steps() {
    log_info "部署完成！后续步骤："
    echo ""
    echo "1. 检查 Prometheus targets:"
    echo "   访问 http://your-prometheus:9090/targets"
    echo ""
    echo "2. 验证 Elasticsearch 指标:"
    echo "   curl http://monitoring-server:9114/metrics | grep elasticsearch"
    echo ""
    echo "3. 检查告警规则:"
    echo "   访问 http://your-prometheus:9090/rules"
    echo ""
    echo "4. 配置 Grafana 仪表板:"
    echo "   导入 Dashboard ID: 266 (Elasticsearch Overview)"
    echo ""
    echo "5. 测试告警:"
    echo "   停止一个 ES 节点验证告警是否触发"
    echo ""
}

# 主函数
main() {
    echo "========================================"
    echo "Elasticsearch 监控部署脚本"
    echo "目标主机: $TARGET_HOSTS"
    echo "========================================"
    echo ""
    
    check_prerequisites
    check_elasticsearch_connectivity
    deploy_exporter
    update_prometheus_config
    verify_deployment
    show_next_steps
    
    log_info "所有步骤完成！"
}

# 执行主函数
main "$@"
