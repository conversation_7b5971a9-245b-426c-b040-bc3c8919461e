groups:
- name: kafka
  rules:
  - alert: KafkaTopicsReplicas
    expr: sum(kafka_topic_partition_in_sync_replica) by (topic) < 3
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: Kafka topics replicas < 3 (instance {{ $labels.instance }})
      description: "Kafka topic in-sync partition\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
  - alert: KafkaBrokerDown
    expr: kafka_brokers < 3
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: "Kafka broker *{{ $labels.instance }}* alert status"
      description: "One of the Kafka broker *{{ $labels.instance }}* is down."