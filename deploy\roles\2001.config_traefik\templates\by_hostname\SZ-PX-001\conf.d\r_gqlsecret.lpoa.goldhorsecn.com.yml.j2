{{ansible_managed|comment}}
http:
  routers:
    gqlsecret-lpoa-root-goldhorsecn:
      rule: Host(`gqlsecret.lpoa.goldhorsecn.com`) && Method(`GET`, `POST`) && Path(`/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      - requestAuthError
      - requestAuthSecret
      tls:
        certResolver: httpResolver
    gqlsecret-lpoa-root-goldhorsecn-cors:
      rule: Host(`gqlsecret.lpoa.goldhorsecn.com`) && Method(`OPTIONS`) && Path(`/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      tls:
        certResolver: httpResolver
    gqlsecret-lpoa-auth-goldhorsecn:
      rule: Host(`gqlsecret.lpoa.goldhorsecn.com`) && Method(`POST`) && Path(`/api/v1/gql/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      - requestAuthError
      - requestAuthSecret
      tls:
        certResolver: httpResolver
    gqlsecret-lpoa-goldhorsecn-cors:
      rule: Host(`gqlsecret.lpoa.goldhorsecn.com`) && Method(`OPTIONS`) && Path(`/api/v1/gql/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      - requestAuthError
      tls:
        certResolver: httpResolver
    gqlsecret-lpoa-intranet-goldhorsecn:
      rule: Host(`gqlsecret.lpoa.goldhorsecn.com`) && PathPrefix(`/-/ping/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      tls:
        certResolver: httpResolver
