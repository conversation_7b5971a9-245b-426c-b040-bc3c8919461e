---
users:
- name: wukong
  ssh_pubkey: '{{lookup("file", "../ssh_keys/id_ed25519_gh_prod_wukong.pub")|trim}}'
  pwd: "$6$tDLinEjNRgXdPWiz$s5FoTmYorh9XV6oPnyoZeSMVBCV2uts8sJIKdB/mnjYZkYmW4byeA0SjvjEKwVD9XbPvc9g2rTjfmBbSBNggA."
  enable_sudo: false
  state: present
- name: ghci
  ssh_pubkey: '{{lookup("file", "../ssh_keys/id_ed25519_gh_prod_ghci.pub")|trim}}'
  pwd: "$6$gQx30VR.xj0yhKzT$cAK64O1hcJeAbW7TGMxRtpBjhErzoleZ/zzejL.oNfIzjHdSJouHH4sPazc6ZqBvsKUI/gwCYjFwsxCSUv5FZ0"
  enable_sudo: true
  state: present
  comment: "for automatic CI/CD with privilege"
- name: ghops
  ssh_pubkey: '{{lookup("file", "../ssh_keys/id_rsa_gh_prod_ghops.pub")|trim}}'
  pwd: "$6$BCUri2/jXKJHxeHD$.NDbevGy1O9A8gtB6GBuSZhDMBjr0Oqn/O7waWLwjt4CznTNPhb50/Ef4NsPn2h21sUqAfUMM0LeOMeKtEY./."
  enable_sudo: false
  state: present
  comment: "daily usage, limit sudo"
- name: ghroot
  ssh_pubkey: '{{lookup("file", "../ssh_keys/id_rsa_gh_prod_ghroot.pub")|trim}}'
  pwd: "$6$mTJg8NGNpBuFwCw9$RiaMbnm4Rc9b0uRiR9KoYiETptorAhYOZ/S7CvASOcxmFJyTdqcTF.6IWOQFmRp2WSMB8ZxGJuPEmACVHcEzJ/"
  enable_sudo: true
  state: present
  comment: "privilege usage with sudo"
#- name: ubuntu
#  ssh_pubkey: '{{lookup("file", "../ssh_keys/ubuntu_prod_rsa.pub")|trim}}'
#  pwd: "$6$MoXBMLfjN7CB8Jtl$qZbRFQNMfXJeI9WcFfMqWwofgDTalIoTpkXU8rCVYSnfVyZx2Op.9//WDLLlNM0f7Y6OwxCiucUHeFBIhrkr9."
#  enable_sudo: true
#  state: present
user_ssh_keys: []
