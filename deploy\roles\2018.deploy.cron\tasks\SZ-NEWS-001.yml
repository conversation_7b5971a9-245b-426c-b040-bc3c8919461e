- name: install shc
  apt:
    name: shc
    state: present
- name: Ensure  directories
  file:
    path: "/data/mysqlbackup"
    state: directory
    mode: 0750
    recurse: yes
- name: Ensure  directories
  file:
    path: "/home/<USER>"
    state: directory
    mode: 0750
    recurse: yes
- name: create backup_mysql.sh
  template:
    src: backup_mysql.sh.j2
    dest: /home/<USER>/backup_mysql.sh
    mode: 0750
- name: shc files
  shell: "shc -r -f /home/<USER>/backup_mysql.sh" 
- name: "absent some sh file"
  file:
    dest: "/home/<USER>/{{item}}"
    state: absent
  with_items:
  - backup_mysql.sh
  - backup_mysql.sh.x.c
- name: Ensure a  mydumper backup job that runs 
  cron:
    name: "mydumper_cron"
    job: "{{mydumper_cron_inline}}"
    day: "*/1"
    hour: "8,12,16,20"
    minute: 30