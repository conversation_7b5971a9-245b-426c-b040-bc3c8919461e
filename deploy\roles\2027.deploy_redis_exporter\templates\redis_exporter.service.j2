{{ ansible_managed | comment }}
[Unit]
Description=prometheus redis exporter
Requires=network-online.target
After=network-online.target

[Service]
PrivateTmp={{ redis_exporter_private_tmp }}
User={{ redis_exporter_user }}
Group={{ redis_exporter_group }}
RuntimeDirectory=redis_exporter
ExecStart={{ redis_exporter_bin_path }}/redis_exporter {% for option in redis_exporter_options %}{% if option %}-{{ option }} {% endif %}{% endfor %}

StandardOutput={{ redis_exporter_log_output }}
StandardError={{ redis_exporter_log_output }}
ExecReload=/bin/kill -HUP $MAINPID
KillSignal=SIGTERM
Restart=always

[Install]
WantedBy=multi-user.target
