{{ansible_managed|comment}}
http:
  routers:
    # Dmz
    gqlDmz_base.trade.igoldhorse.cn:
      rule: Host(`base.trade.igoldhorse.cn`, `cn.trade.igoldhorse.cn`) && Method(`POST`) && Path(`/dmz/api/v1/gql/`)
      service: gqlDmz
      middlewares:
      - gzip
      - requestAuthError
      - requestAuthDmz
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.igoldhorse.cn
        - main: cn.trade.igoldhorse.cn
    gqlDmz_base.trade.igoldhorse.cn_cors:
      rule: Host(`base.trade.igoldhorse.cn`, `cn.trade.igoldhorse.cn`) && Method(`OPTIONS`) && Path(`/dmz/api/v1/gql/`)
      service: gqlDmz
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.igoldhorse.cn
        - main: cn.trade.igoldhorse.cn
    gqlDmz_base.trade.igoldhorse.cn_ping:
      rule: Host(`base.trade.igoldhorse.cn`, `cn.trade.igoldhorse.cn`) && Method(`GET`) && Path(`/dmz/-/ping/`)
      service: gqlDmz
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.igoldhorse.cn
        - main: cn.trade.igoldhorse.cn
    # Secret
    gqlSecret_base.trade.igoldhorse.cn:
      rule: Host(`base.trade.igoldhorse.cn`, `cn.trade.igoldhorse.cn`) && Method(`POST`) && Path(`/secret/api/v1/gql/`)
      service: gqlSecret
      middlewares:
      - gzip
      - requestAuthError
      - requestAuthSecret
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.igoldhorse.cn
        - main: cn.trade.igoldhorse.cn
    gqlSecret_base.trade.igoldhorse.cn_cors:
      rule: Host(`base.trade.igoldhorse.cn`, `cn.trade.igoldhorse.cn`) && Method(`OPTIONS`) && Path(`/secret/api/v1/gql/`)
      service: gqlSecret
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.igoldhorse.cn
        - main: cn.trade.igoldhorse.cn
    gqlSecret_base.trade.igoldhorse.cn_ping:
      rule: Host(`base.trade.igoldhorse.cn`, `cn.trade.igoldhorse.cn`) && Method(`GET`) && Path(`/secret/-/ping/`)
      service: gqlSecret
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.igoldhorse.cn
        - main: cn.trade.igoldhorse.cn
    mqttSecret_sz.quot.igoldhorse.cn:
      rule: Host(`sz.quot.igoldhorse.cn`) && Path(`/ws`)
      service: mqttSecret
      middlewares:
      - requestAuthError
      - requestAuthSecret
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.igoldhorse.cn
        - main: cn.trade.igoldhorse.cn
    mqttNoSecret_sz.quot.igoldhorse.cn:
      rule: Host(`sz.noquot.igoldhorse.cn`) && Path(`/ws`)
      service: mqttSecret
      tls:
        certResolver: httpResolver
