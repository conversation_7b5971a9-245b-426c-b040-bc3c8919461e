# API接口耗时监控配置指南

## 🎯 概述

本指南介绍如何配置基于日志的API接口耗时监控，实现对接口性能的实时监控和告警。支持多种日志格式，能够自动识别耗时超过1秒的慢接口并触发告警。

## 📊 监控能力

### 支持的日志格式

1. **标准格式**: `接口: /api/user/login 耗时: 1200ms`
2. **JSON格式**: `{"api_path":"/api/user/login","response_time":1200}`
3. **Nginx格式**: 标准Nginx访问日志 + 响应时间
4. **自定义格式**: 最后一列为耗时的任意格式

### 监控指标

- **api_response_time_ms**: API响应时间直方图
- **api_slow_requests_total**: 慢请求计数器 (>1000ms)

### 告警规则

- **SlowAPIRequests**: 检测到慢请求 (>1s)
- **HighSlowAPIRequestRate**: 慢请求频率过高 (5分钟内>5次)
- **HighAPIResponseTimeP95**: P95响应时间过高 (>1s)
- **HighAPIResponseTimeP99**: P99响应时间过高 (>2s)

## 🚀 快速配置

### 步骤1: 确定日志格式

```bash
# 查看日志样例
tail -f /var/log/application/api.log

# 示例输出:
# 2024-01-15 10:00:01 INFO 接口: /api/user/login 耗时: 150ms 状态: 200
# 2024-01-15 10:00:02 WARN 接口: /api/order/create 耗时: 1200ms 状态: 200
```

### 步骤2: 运行配置脚本

```bash
cd deploy
chmod +x scripts/*.sh

# 自动检测格式并配置
./scripts/setup_api_monitoring.sh auto /var/log/application/*.log

# 或指定格式
./scripts/setup_api_monitoring.sh standard /var/log/application/*.log
./scripts/setup_api_monitoring.sh json /var/log/api/*.json
./scripts/setup_api_monitoring.sh nginx /var/log/nginx/access.log
```

### 步骤3: 验证配置

```bash
# 运行测试脚本
./scripts/test_api_monitoring.sh

# 检查Promtail指标
curl http://localhost:19080/metrics | grep api_response_time

# 检查Prometheus规则
curl http://localhost:9090/api/v1/rules | grep SlowAPI
```

## 🔧 手动配置

### Promtail配置

编辑 `/etc/promtail/promtail.yml`，添加以下配置：

```yaml
scrape_configs:
# API性能监控 - 标准格式
- job_name: api_performance_monitoring
  static_configs:
  - targets:
    - localhost
    labels:
      job: api-performance
      __path__: /var/log/application/*.log
  pipeline_stages:
  # 提取API路径和响应时间
  - regex:
      expression: '.*接口[:：]\s*(?P<api_path>[^\s]+).*耗时[:：]\s*(?P<response_time>\d+(?:\.\d+)?)\s*(?P<time_unit>ms|毫秒|s|秒).*'
  
  # 转换时间单位为毫秒
  - template:
      source: response_time_ms
      template: |
        {{- if eq .time_unit "s" -}}
          {{ mul .response_time 1000 }}
        {{- else if eq .time_unit "秒" -}}
          {{ mul .response_time 1000 }}
        {{- else -}}
          {{ .response_time }}
        {{- end -}}
  
  # 添加标签
  - labels:
      api_path: api_path
  
  # 记录响应时间直方图
  - metrics:
      api_response_time_ms:
        type: Histogram
        description: API response time in milliseconds
        source: response_time_ms
        config:
          buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000]
  
  # 慢请求计数器
  - match:
      selector: '{job="api-performance"}'
      stages:
      - template:
          source: is_slow
          template: |
            {{- if gt (float64 .response_time_ms) 1000.0 -}}
              1
            {{- else -}}
              0
            {{- end -}}
      - metrics:
          api_slow_requests_total:
            type: Counter
            description: Total slow API requests (>1s)
            source: is_slow
            config:
              value: "1"
              action: inc
```

### Prometheus告警规则

创建 `/etc/prometheus/rule.d/api_performance.rule.yml`：

```yaml
groups:
- name: api-performance
  rules:
  # 慢接口告警
  - alert: SlowAPIRequests
    expr: increase(promtail_custom_api_slow_requests_total[5m]) > 0
    for: 0m
    labels:
      severity: warning
      api_path: "{{ $labels.api_path }}"
    annotations:
      summary: "检测到慢接口请求 ({{ $labels.api_path }})"
      description: "接口 {{ $labels.api_path }} 在过去5分钟内有 {{ $value }} 次耗时超过1秒的请求"

  # 慢接口频率告警
  - alert: HighSlowAPIRequestRate
    expr: increase(promtail_custom_api_slow_requests_total[5m]) > 5
    for: 2m
    labels:
      severity: critical
      api_path: "{{ $labels.api_path }}"
    annotations:
      summary: "接口性能严重下降 ({{ $labels.api_path }})"
      description: "接口 {{ $labels.api_path }} 在过去5分钟内有 {{ $value }} 次慢请求"

  # P95响应时间告警
  - alert: HighAPIResponseTimeP95
    expr: histogram_quantile(0.95, rate(promtail_custom_api_response_time_ms_bucket[5m])) > 1000
    for: 3m
    labels:
      severity: warning
    annotations:
      summary: "API P95响应时间过高"
      description: "API P95响应时间为 {{ $value }}ms，超过1秒阈值"
```

## 📝 日志格式示例

### 标准格式
```
2024-01-15 10:00:01 INFO 接口: /api/user/login 耗时: 150ms 状态: 200
2024-01-15 10:00:02 WARN 接口: /api/order/create 耗时: 1200ms 状态: 200
2024-01-15 10:00:03 ERROR 接口: /api/payment/process 耗时: 2.5s 状态: 500
```

### JSON格式
```json
{"timestamp":"2024-01-15T10:00:01Z","api_path":"/api/user/login","response_time":150,"status_code":200}
{"timestamp":"2024-01-15T10:00:02Z","api_path":"/api/order/create","response_time":1200,"status_code":200}
```

### Nginx格式
```
************* - - [15/Jan/2024:10:00:01 +0800] "POST /api/user/login HTTP/1.1" 200 1024 "-" "Mozilla/5.0" 0.150
************* - - [15/Jan/2024:10:00:02 +0800] "POST /api/order/create HTTP/1.1" 200 2048 "-" "Mozilla/5.0" 1.200
```

## 🔍 监控查询

### Prometheus查询示例

```promql
# 查看API响应时间P95
histogram_quantile(0.95, rate(promtail_custom_api_response_time_ms_bucket[5m]))

# 查看慢请求率
rate(promtail_custom_api_slow_requests_total[5m])

# 查看最慢的接口
topk(10, histogram_quantile(0.95, rate(promtail_custom_api_response_time_ms_bucket[5m])) by (api_path))

# 查看慢请求最多的接口
topk(10, rate(promtail_custom_api_slow_requests_total[5m]) by (api_path))
```

### Grafana仪表板

推荐的面板配置：

1. **API响应时间趋势**: 时间序列图显示P50/P95/P99
2. **慢请求统计**: 单值面板显示慢请求总数
3. **接口性能排行**: 表格显示最慢的接口
4. **告警状态**: 告警面板显示当前活跃告警

## 🛠️ 故障排除

### 常见问题

1. **指标未出现**
   ```bash
   # 检查Promtail日志
   journalctl -u promtail -f
   
   # 检查正则表达式匹配
   ./scripts/test_api_monitoring.sh
   ```

2. **告警不触发**
   ```bash
   # 检查Prometheus规则
   promtool check rules /etc/prometheus/rule.d/api_performance.rule.yml
   
   # 重载Prometheus配置
   curl -X POST http://localhost:9090/-/reload
   ```

3. **日志格式不匹配**
   ```bash
   # 查看日志样例
   tail -n 10 /var/log/application/api.log
   
   # 测试正则表达式
   echo "your_log_line" | grep -E "接口[:：].*耗时[:：].*[0-9]+"
   ```

## 📈 性能优化

### 配置优化建议

1. **调整采集间隔**: 根据日志量调整scrape_interval
2. **优化正则表达式**: 使用更精确的匹配模式
3. **限制标签基数**: 避免高基数标签（如用户ID）
4. **设置合理阈值**: 根据业务需求调整慢请求阈值

### 资源使用

- **内存使用**: 每个API路径约占用1-2MB内存
- **存储空间**: 每天约产生100MB-1GB监控数据
- **CPU使用**: 正则解析约占用5-10% CPU

## 🔄 维护

### 定期检查

- 每日检查告警状态和慢接口统计
- 每周分析API性能趋势
- 每月评估监控规则有效性

### 配置更新

- 根据新增接口更新监控配置
- 定期优化告警阈值
- 清理无用的历史数据

---

**配置完成后，您将获得：**
- 实时API性能监控
- 自动慢接口检测和告警
- 详细的性能分析数据
- 可视化的监控仪表板
