---
- name: Enable overcommit in sysctl
  sysctl:
    name: vm.overcommit_memory
    value: 1
    state: present
    reload: yes
    ignoreerrors: yes

- name: Create redis user
  user:
    name: redis
    comment: "Redis"
    shell: /bin/false
    state: present
    system: yes
    createhome: no

- name: apt install ruby 
  apt:
    name:
    - ruby
    - rubygems
    state: present
    update_cache: true

- gem:
    name: redis
    state: present

- name: Install redis-trib from local file.
  copy:
    src: redis-trib.rb
    dest: /usr/local/bin/redis-trib
    mode: 0750

- name: Check if redis is installed
  stat:
    path: /usr/local/bin/redis-server
  register: is_installed
  changed_when: no

- name: Install required packages to build
  apt:
    name: "{{ redis_cluster_default_packages }}"
    state: present

- name: download redis binary to local folder
  become: false
  get_url:
    url: "http://download.redis.io/releases/redis-{{redis_cluster_version}}.tar.gz"
    dest: "/tmp/redis-{{ redis_cluster_version }}.tar.gz"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: unpack redis binaries
  unarchive:
    src: "/tmp/redis-{{ redis_cluster_version }}.tar.gz"
    dest: "/tmp"
    creates: "/tmp/redis-{{ redis_cluster_version }}"
    remote_src: true
  check_mode: false

- name: Run 'install' target
  make:
    chdir: "/tmp/redis-{{ redis_cluster_version }}"
    target: install
    file: "/tmp/redis-{{ redis_cluster_version }}/Makefile"
    params:
      PREFIX: "/tmp/redis-{{ redis_cluster_version }}"
  become: yes

- name: copy official redis binaries
  copy:
    src: "/tmp/redis-{{ redis_cluster_version }}/src/{{ item }}"
    dest: "/usr/local/bin/{{ item }}"
    mode: '755'
    owner: root
    group: root
    remote_src: true
  with_items:
    - redis-benchmark
    - redis-check-aof
    - redis-check-rdb
    - redis-cli
    - redis-sentinel
    - redis-server
