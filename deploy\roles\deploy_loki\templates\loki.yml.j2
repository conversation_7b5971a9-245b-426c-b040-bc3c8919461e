{{ ansible_managed | comment }}

target: all,table-manager
auth_enabled: false
server:
  http_listen_port: {{LOKI_HTTP_PORT}}
  grpc_listen_port: 9096
  grpc_server_max_recv_msg_size: 1572864000
  grpc_server_max_send_msg_size: 1572864000
  grpc_server_max_concurrent_streams: 1000
  http_server_idle_timeout: 120s
  http_server_write_timeout: 1m 
ingester:
  lifecycler:
    address: 127.0.0.1
    ring:
      kvstore:
        store: inmemory
      replication_factor: 1
    final_sleep: 0s
  chunk_idle_period: 1h       # Any chunk not receiving new logs in this time will be flushed
  max_chunk_age: 1h           # All chunks will be flushed when they hit this age, default is 1h
  chunk_target_size: 1048576  # Loki will attempt to build chunks up to 1.5MB, flushing first if chunk_idle_period or max_chunk_age is reached first
  chunk_retain_period: 30s    # Must be greater than index read cache TTL if using an index cache (Default index read cache TTL is 5m)
  max_transfer_retries: 0     # Chunk transfers disabled
schema_config:
  configs:
    - from: 2022-08-01
      store: boltdb-shipper
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h
storage_config:
  boltdb_shipper:
    active_index_directory: "{{LOKI_DB_DIR}}/boltdb-shipper-active"
    cache_location: "{{LOKI_DB_DIR}}/boltdb-shipper-cache"
    cache_ttl: 24h         # Can be increased for faster performance over longer query periods, uses more disk space
    shared_store: filesystem
  filesystem:
    directory: "{{LOKI_DB_DIR}}/chunks"
compactor:
  working_directory: "{{LOKI_DB_DIR}}/boltdb-shipper-compactor"
  shared_store: filesystem
  compaction_interval: 10m
  retention_enabled: true
  retention_delete_delay: 30m
  delete_request_cancel_period: 25m
  retention_delete_worker_count: 150

frontend:
  compress_responses: true
  max_outstanding_per_tenant: 1024

frontend_worker:
  frontend_address: 127.0.0.1:9096
  grpc_client_config:
    max_send_msg_size: 1572864000
  parallelism: 9

limits_config:
  max_streams_per_user: 200000
  reject_old_samples: true
  reject_old_samples_max_age: 48h
  ingestion_rate_mb: 15
  ingestion_burst_size_mb: 20
  max_query_length: 504h
  max_query_parallelism: 256
  retention_period: 43680h
  retention_stream:
    - period: 72h
      priority: 1
      selector: '{job=~"varlogs|tmplogs"}'
    - period: 4368h
      priority: 1
      selector: '{job=~"supervisorlogs"}'
    - period: 8736h
      priority: 1
      selector: '{job=~"datalogs-error|datalogs-info|datalogs-warn"}'
    - period: 4368h
      priority: 1
      selector: '{job=~"pm2logs|traefik-log|services_varlogs"}'
chunk_store_config:
  max_look_back_period: 0s
table_manager:
  retention_deletes_enabled: true
  retention_period: 43680h
ruler:
  storage:
    type: local
    local:
      directory: "{{LOKI_DB_DIR}}/rules"
  rule_path: "{{LOKI_DB_DIR}}/rules-temp"
  alertmanager_url: http://ALERT:9093
  ring:
    kvstore:
      store: inmemory
  enable_api: true
