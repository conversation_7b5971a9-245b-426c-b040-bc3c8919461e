{{ansible_managed|comment}}
tcp:
  routers:
    testgql:
      service: test_fwdev
      rule: |
        HostSNI(
          `gqldmz.test.fwdev.top`,
          `gqlsecret.test.fwdev.top`,
          `gqldmz.mr.fwdev.top`,
          `gqlsecret.mr.fwdev.top`,
          `quotationsecret.test.fwdev.top`,
          `quote.test.fwdev.top`,
          `quotationnosecret.test.fwdev.top`,
          `hyprod.fwdev.top`,
          `gqldmz-ayers.test.fwdev.top`,
          `gqlsecret-ayers.test.fwdev.top`,
          `gqlsecret-rest.test.fwdev.top`,
          `openaccount.fwdev.top`,
          `h5login.fwdev.top`,
          `biz-platform.fwdev.top`,
          `news.fwdev.top`,
          `bmp.fwdev.top`,
          `oms.fwdev.top`,
          `oms-media.fwdev.top`,
          `easyview-push.gh.test.fwdev.top`,
          `lifecycle.gh.test.fwdev.top`,
          `open-api-gateway.fwdev.top`,
          `openapi-auth.fwdev.top`,
          `lpoa.vpn.fwdev.top`,
          `lpoa.test.fwdev.top`,
          `gqldmz-lpoa.test.fwdev.top`,
          `webtrade.test.fwdev.top`,
          `webtrade-socket.test.fwdev.top`,
          `gqlsecret-lpoa.test.fwdev.top`,
          `webtrade.fwdev.top`,
          `webtrade-lit.fwdev.top`,
          `help.fwdev.top`,
          `apidoc.fwdev.top`,
          `japidoc.fwdev.top`,
          `jfapidoc.fwdev.top`,
          `papidoc.fwdev.top`,
          `bapidoc.fwdev.top`,
          `fcn-pages.fwdev.top`,
          `balinfo.fwdev.top`,
          `statistics.fwdev.top`,
          `currency.fwdev.top`,
          `lpoa-currency.fwdev.top`,
          `user-center.fwdev.top`,
          `oms-center.fwdev.top`,
          `acclogout.fwdev.top`,
          `fundrisk.fwdev.top`,
          `immigrant.fwdev.top`,
          `fund.fwdev.top`,
          `meetingactive.fwdev.top`,
          `jhs.fwdev.top`,
          `derivative-risk.fwdev.top`,
          `pi-auth.fwdev.top`,
          `static.fwdev.top`,
          `edda.fwdev.top`,
          `struct-quotation-api.fwdev.top`,
          `struct-order-api.fwdev.top`,
          `k-line-mobile.fwdev.top`,
          `k-line-pc.fwdev.top`,
          `pre-proxy.fwdev.top`,
          )
      tls:
        passthrough: true

http:
  routers:
    cert:
      service: cert
      rule: |
        Host(
          `risk.demo.fwdev.top`,
          `openaccount.fwdev.top`,
          `h5login.fwdev.top`,
          `biz-platform.fwdev.top`,
          `news.fwdev.top`,
          `files.fwdev.top`,
          `bmp.fwdev.top`,
          `oms.fwdev.top`,
          `oms-media.fwdev.top`,
          `easyview-push.gh.test.fwdev.top`,
          `lifecycle.gh.test.fwdev.top`,
          `open-api-gateway.fwdev.top`,
          `openapi-auth.fwdev.top`,
          `lpoa.vpn.fwdev.top`,
          `lpoa.test.fwdev.top`,
          `gqldmz-lpoa.test.fwdev.top`,
          `gqlsecret-lpoa.test.fwdev.top`,
          `webtrade.test.fwdev.top`,
          `gqlsecret-rest.test.fwdev.top`,
          `risk-marks.fwdev.top`,
          `webtrade.fwdev.top`,
          `help.fwdev.top`,
          `apidoc.fwdev.top`,
          `japidoc.fwdev.top`,
          `jfapidoc.fwdev.top`,
          `papidoc.fwdev.top`,
          `bapidoc.fwdev.top`,
          `balinfo.fwdev.top`,
          `statistics.fwdev.top`,
          `fcn-pages.fwdev.top`,
          `currency.fwdev.top`,
          `lpoa-currency.fwdev.top`,
          `user-center.fwdev.top`,
          `oms-center.fwdev.top`,
          `acclogout.fwdev.top`,
          `fundrisk.fwdev.top`,
          `immigrant.fwdev.top`,
          `fund.fwdev.top`,
          `meetingactive.fwdev.top`,
          `jhs.fwdev.top`,
          `derivative-risk.fwdev.top`,
          `pi-auth.fwdev.top`,
          `static.fwdev.top`,
          `edda.fwdev.top`,
          `struct-quotation-api.fwdev.top`,
          `struct-order-api.fwdev.top`,
          `k-line-mobile.fwdev.top`,
          `k-line-pc.fwdev.top`,
          `pre-proxy.fwdev.top`,
          )
