{{ansible_managed|comment}}
http:
  middlewares:
    hkPx001ImmigrantIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    immigrant.igoldhorse.com:
      service: hkPx001CaddyImmigrant
      rule: Host(`immigrant.igoldhorse.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
  services:
    hkPx001CaddyImmigrant:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
