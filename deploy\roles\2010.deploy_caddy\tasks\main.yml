---
- name: create caddy system group
  group:
    name: caddy
    system: true
    state: present
    gid: 4733

- name: create data directory
  file:
    path: /data
    state: directory
    owner: root
    group: root
    mode: 0755

- name: create caddy system user
  user:
    name: caddy
    system: true
    shell: "/usr/sbin/nologin"
    group: caddy
    groups:
    - wukong
    createhome: false
    home: "{{ CADDY_DB_DIR }}"
    uid: 4733

- name: create caddy data directory
  file:
    path: "{{ CADDY_DB_DIR }}"
    state: directory
    recurse: true
    owner: caddy
    group: caddy
    mode: 0755

- name: create caddy configuration directories
  file:
    path: "{{ item }}"
    state: directory
    owner: root
    group: caddy
    mode: 0770
  with_items:
  - "{{ CADDY_CONFIG_DIR }}"

- name: caddy url
  debug:
    msg: "{{CADDY_DOWNLOAD_URL}}"

- name: download caddy binary to local folder
  become: false
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "{{CADDY_DOWNLOAD_URL}}"
    dest: "/tmp/caddy.tar.gz"
    checksum: '{{CADDY_CHECKSUM}}'
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: unpack caddy binaries
  unarchive:
    src: "/tmp/caddy.tar.gz"
    dest: "/tmp"
    remote_src: true
  check_mode: false

- name: copy official caddy binaries
  copy:
    src: "/tmp/{{item}}"
    dest: "/usr/local/bin/caddy"
    mode: '755'
    owner: root
    group: root
    remote_src: true
  with_items:
  - caddy
  notify:
  - restart caddy

- name: create systemd service unit
  template:
    src: caddy.service.j2
    dest: /etc/systemd/system/caddy.service
    owner: root
    group: root
    mode: 0644
  notify:
  - restart caddy

- name: enabled caddy service
  systemd:
    name: caddy
    daemon_reload: true
    enabled: true
