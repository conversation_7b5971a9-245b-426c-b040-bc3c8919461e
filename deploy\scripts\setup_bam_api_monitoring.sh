#!/bin/bash

# BAM API性能监控配置脚本
# 专门针对ResponseResultAdvice日志格式
# 用法: ./setup_bam_api_monitoring.sh [log_path]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "BAM API性能监控配置脚本"
    echo ""
    echo "用法: $0 [log_path]"
    echo ""
    echo "支持的日志格式:"
    echo "  ResponseResultAdvice格式:"
    echo "  2025-08-01 10:59:28.381 [vt88g2oYUvvoyMoY] INFO (ResponseResultAdvice.java:131)"
    echo "  - request url:[</bam/v2/customerDetails/dataUpdateInfoForStatus>]"
    echo "  - use: [<808>]ms"
    echo ""
    echo "示例:"
    echo "  $0 /var/log/application/*.log"
    echo "  $0 /var/log/supervisor/*.log"
    echo ""
}

# 测试日志格式匹配
test_log_format() {
    local log_file="$1"
    
    if [ ! -f "$log_file" ]; then
        log_warn "日志文件不存在: $log_file"
        return 1
    fi
    
    log_info "测试日志格式匹配: $log_file"
    
    # 读取包含ResponseResultAdvice的日志行
    local sample_lines=$(grep "ResponseResultAdvice.*request url.*use:" "$log_file" | head -5)
    
    if [ -z "$sample_lines" ]; then
        log_warn "未找到ResponseResultAdvice格式的日志"
        return 1
    fi
    
    log_info "找到匹配的日志行:"
    echo "$sample_lines" | while IFS= read -r line; do
        # 提取API路径和响应时间
        local api_path=$(echo "$line" | sed -n 's/.*request url:\[<\([^>]*\)>\].*/\1/p')
        local response_time=$(echo "$line" | sed -n 's/.*use: \[<\([0-9]*\)>\]ms.*/\1/p')
        
        if [ -n "$api_path" ] && [ -n "$response_time" ]; then
            echo "  ✓ API: $api_path, 耗时: ${response_time}ms"
            
            # 检查是否为慢请求
            if [ "$response_time" -gt 1000 ]; then
                echo "    ⚠ 慢请求检测: 是 (>${response_time}ms)"
            else
                echo "    ✓ 慢请求检测: 否 (<=${response_time}ms)"
            fi
        else
            echo "  ✗ 解析失败: $line"
        fi
    done
    
    return 0
}

# 生成BAM API监控配置
generate_bam_config() {
    local log_path="$1"
    local config_file="/tmp/bam_api_monitoring_config.yml"
    
    log_info "生成BAM API监控配置..."
    
    cat > "$config_file" << EOF
# BAM API性能监控 - ResponseResultAdvice格式
- job_name: bam_api_performance_monitoring
  static_configs:
  - targets:
    - localhost
    labels:
      job: bam-api-performance
      __path__: $log_path
  pipeline_stages:
  # 解析服务名称
  - regex:
      source: filename
      expression: /var/log/[^/]*/(?P<service>.*?)\.log
  - labels:
      service: service
  
  # 提取BAM API路径和响应时间
  - match:
      selector: '{job="bam-api-performance"} |~ "ResponseResultAdvice.*request url.*use:"'
      stages:
      # 提取request url中的API路径和响应时间
      - regex:
          expression: '.*request url:\[<(?P<api_path>[^>]+)>\].*use: \[<(?P<response_time>\d+)>\]ms.*'
      
      # 提取请求ID和来源IP
      - regex:
          expression: '.*\[(?P<request_id>[^\]]+)\].*from: \[<(?P<client_ip>[^>]+)>\].*'
      
      # 添加标签
      - labels:
          api_path: api_path
          request_id: request_id
          client_ip: client_ip
      
      # 记录所有BAM API响应时间
      - metrics:
          bam_api_response_time_ms:
            type: Histogram
            description: BAM API response time in milliseconds
            source: response_time
            config:
              buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000, 30000]
      
      # 记录慢请求计数器 (>1000ms)
      - template:
          source: is_slow
          template: |
            {{- if gt (float64 .response_time) 1000.0 -}}
              1
            {{- else -}}
              0
            {{- end -}}
      
      - metrics:
          bam_api_slow_requests_total:
            type: Counter
            description: Total number of slow BAM API requests (>1s)
            source: is_slow
            config:
              value: "1"
              action: inc
      
      # 记录API请求总数
      - metrics:
          bam_api_requests_total:
            type: Counter
            description: Total number of BAM API requests
            config:
              action: inc
      
      # 记录按状态码分类的请求（如果日志中有状态信息）
      - template:
          source: status_category
          template: |
            {{- if gt (float64 .response_time) 5000.0 -}}
              timeout
            {{- else if gt (float64 .response_time) 2000.0 -}}
              slow
            {{- else if gt (float64 .response_time) 1000.0 -}}
              warning
            {{- else -}}
              normal
            {{- end -}}
      
      - labels:
          status_category: status_category
      
      - metrics:
          bam_api_requests_by_status_total:
            type: Counter
            description: BAM API requests by performance status
            source: status_category
            config:
              action: inc
EOF

    echo "$config_file"
}

# 部署配置
deploy_bam_config() {
    local config_file="$1"
    
    log_info "部署BAM API监控配置..."
    
    # 备份现有配置
    if [ -f "/etc/promtail/promtail.yml" ]; then
        sudo cp /etc/promtail/promtail.yml /etc/promtail/promtail.yml.backup.$(date +%Y%m%d_%H%M%S)
        log_info "已备份现有Promtail配置"
    fi
    
    # 更新Promtail配置
    log_info "更新Promtail配置..."
    ansible-playbook -i hosts.ini 2003.promtail.playbook.yml \
        --extra-vars "bam_api_monitoring_config=$config_file" \
        --tags config
    
    # 重载Prometheus配置
    log_info "重载Prometheus配置..."
    ansible-playbook -i hosts.ini 2005.prometheus.playbook.yml \
        --tags config
    
    log_info "配置部署完成"
}

# 验证配置
verify_bam_config() {
    log_info "验证BAM API监控配置..."
    
    # 检查Promtail指标
    local promtail_hosts=("localhost:19080" "SZ-ES-001:19080" "SZ-MONT-002:19080" "HK-MG-002:19080")
    
    for host in "${promtail_hosts[@]}"; do
        log_info "检查 $host..."
        
        if curl -s --connect-timeout 5 "http://$host/metrics" > /dev/null 2>&1; then
            local metrics=$(curl -s "http://$host/metrics")
            
            # 检查BAM API相关指标
            if echo "$metrics" | grep -q "bam_api_response_time_ms"; then
                log_info "  ✓ 发现 bam_api_response_time_ms 指标"
                local count=$(echo "$metrics" | grep "bam_api_response_time_ms_count" | wc -l)
                echo "    指标数量: $count"
            else
                log_warn "  ⚠ 未发现 bam_api_response_time_ms 指标"
            fi
            
            if echo "$metrics" | grep -q "bam_api_slow_requests_total"; then
                log_info "  ✓ 发现 bam_api_slow_requests_total 指标"
                local slow_count=$(echo "$metrics" | grep "bam_api_slow_requests_total" | tail -1 | awk '{print $2}')
                echo "    慢请求计数: $slow_count"
            else
                log_warn "  ⚠ 未发现 bam_api_slow_requests_total 指标"
            fi
            
            if echo "$metrics" | grep -q "bam_api_requests_total"; then
                log_info "  ✓ 发现 bam_api_requests_total 指标"
                local total_count=$(echo "$metrics" | grep "bam_api_requests_total" | tail -1 | awk '{print $2}')
                echo "    总请求计数: $total_count"
            else
                log_warn "  ⚠ 未发现 bam_api_requests_total 指标"
            fi
        else
            log_warn "  ✗ 无法连接到 $host"
        fi
        echo ""
    done
    
    # 检查Prometheus告警规则
    log_info "检查Prometheus告警规则..."
    local prometheus_hosts=("localhost:9090" "SZ-ES-001:9090" "SZ-MONT-002:9090")
    
    for host in "${prometheus_hosts[@]}"; do
        if curl -s --connect-timeout 5 "http://$host/api/v1/rules" > /dev/null 2>&1; then
            local rules=$(curl -s "http://$host/api/v1/rules")
            
            if echo "$rules" | grep -q "BAMSlowAPIRequests"; then
                log_info "  ✓ 发现 BAMSlowAPIRequests 告警规则"
            else
                log_warn "  ⚠ 未发现 BAMSlowAPIRequests 告警规则"
            fi
            break
        fi
    done
}

# 生成测试数据
generate_test_data() {
    local test_log="/tmp/bam_test.log"
    
    log_info "生成BAM测试日志数据..."
    
    cat > "$test_log" << EOF
2025-08-01 10:59:28.381 [vt88g2oYUvvoyMoY] INFO  (ResponseResultAdvice.java:131) - request url:[</bam/v2/customerDetails/dataUpdateInfoForStatus>], from: [<**************>] request info: { body: [<{"customerKey":"1452","nonce":"af7035bd6092fde07bcb6c02d6e8359e","publicEmail":"***@redbeaconam.com","publicSource":1,"timestamp":"*************","accountId":745,"language":0,"signature":"3a11d4539aad494b7e57f82990ba5cc1ce2776cb","rootOrgId":75,"deviceType":"2"}>], sha1: [<8ca8bc67f908d73fee838a61075ac8292feab942>], length: [<265>]},response info: { body: [<{"datas":{"updateStatus":2,"updateStopAccountCount":0,"updatedAccountCount":2,"updatingAccountCount":1},"message":"Successful call","status":10000}>], sha1: [<6258847b9eeaf0573d3ad2d14a2f744171ccfc18>], length: [<147>] }, start: [<*************>], end: [<*************>], use: [<808>]ms
2025-08-01 11:00:15.234 [abc123def456] INFO  (ResponseResultAdvice.java:131) - request url:[</bam/v2/user/login>], from: [<192.168.1.100>] request info: { body: [<{"username":"test","password":"***"}>], sha1: [<hash123>], length: [<50>]},response info: { body: [<{"status":"success","token":"***"}>], sha1: [<hash456>], length: [<100>] }, start: [<********00000>], end: [<********00150>], use: [<150>]ms
2025-08-01 11:01:30.567 [def789ghi012] WARN  (ResponseResultAdvice.java:131) - request url:[</bam/v2/report/generate>], from: [<10.0.0.50>] request info: { body: [<{"reportType":"monthly","params":{...}}>], sha1: [<hash789>], length: [<200>]},response info: { body: [<{"reportId":"12345","status":"processing"}>], sha1: [<hash012>], length: [<80>] }, start: [<*************>], end: [<********91500>], use: [<1500>]ms
2025-08-01 11:02:45.890 [ghi345jkl678] ERROR (ResponseResultAdvice.java:131) - request url:[</bam/v2/payment/process>], from: [<172.16.0.10>] request info: { body: [<{"amount":1000,"currency":"USD"}>], sha1: [<hash345>], length: [<100>]},response info: { body: [<{"error":"timeout","code":500}>], sha1: [<hash678>], length: [<50>] }, start: [<1754017365000>], end: [<1754017368000>], use: [<3000>]ms
2025-08-01 11:03:12.123 [jkl901mno234] INFO  (ResponseResultAdvice.java:131) - request url:[</bam/v2/health/check>], from: [<127.0.0.1>] request info: { body: [<{}>], sha1: [<hash901>], length: [<2>]},response info: { body: [<{"status":"ok"}>], sha1: [<hash234>], length: [<15>] }, start: [<1754017392000>], end: [<1754017392020>], use: [<20>]ms
EOF

    echo "$test_log"
}

# 主函数
main() {
    local log_path="${1:-}"
    
    if [ -z "$log_path" ]; then
        show_usage
        exit 1
    fi
    
    log_header "BAM API性能监控配置"
    echo "日志路径: $log_path"
    echo ""
    
    # 查找第一个存在的日志文件进行测试
    local test_file=""
    for file in $log_path; do
        if [ -f "$file" ]; then
            test_file="$file"
            break
        fi
    done
    
    if [ -n "$test_file" ]; then
        # 测试现有日志格式
        if test_log_format "$test_file"; then
            log_info "日志格式验证通过"
        else
            log_warn "日志格式验证失败，将使用测试数据"
            test_file=$(generate_test_data)
            test_log_format "$test_file"
        fi
    else
        log_warn "未找到日志文件，生成测试数据"
        test_file=$(generate_test_data)
        test_log_format "$test_file"
    fi
    
    echo ""
    
    # 生成配置
    local config_file=$(generate_bam_config "$log_path")
    
    if [ $? -eq 0 ]; then
        log_info "配置文件生成成功: $config_file"
        
        # 显示配置预览
        log_info "配置预览:"
        echo "----------------------------------------"
        head -n 30 "$config_file"
        echo "----------------------------------------"
        
        # 询问是否部署
        read -p "是否部署此配置? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            deploy_bam_config "$config_file"
            verify_bam_config
            
            log_info "BAM API性能监控配置完成！"
            echo ""
            echo "监控指标:"
            echo "  - bam_api_response_time_ms: BAM API响应时间直方图"
            echo "  - bam_api_slow_requests_total: 慢请求计数器 (>1000ms)"
            echo "  - bam_api_requests_total: 总请求计数器"
            echo "  - bam_api_requests_by_status_total: 按性能状态分类的请求"
            echo ""
            echo "告警规则:"
            echo "  - BAMSlowAPIRequests: 检测到慢请求"
            echo "  - BAMHighSlowAPIRequestRate: 慢请求频率过高"
            echo "  - BAMHighAPIResponseTimeP95: P95响应时间过高"
            echo "  - BAMHighAPIResponseTimeP99: P99响应时间过高"
            echo ""
            echo "Prometheus查询示例:"
            echo "  # 查看BAM API P95响应时间"
            echo "  histogram_quantile(0.95, rate(promtail_custom_bam_api_response_time_ms_bucket[5m]))"
            echo ""
            echo "  # 查看慢请求率"
            echo "  rate(promtail_custom_bam_api_slow_requests_total[5m])"
            echo ""
            echo "  # 查看最慢的接口TOP10"
            echo "  topk(10, histogram_quantile(0.95, rate(promtail_custom_bam_api_response_time_ms_bucket[5m])) by (api_path))"
        else
            log_info "配置已生成但未部署: $config_file"
        fi
    else
        log_error "配置生成失败"
        exit 1
    fi
    
    # 清理测试文件
    if [ -f "/tmp/bam_test.log" ]; then
        rm -f "/tmp/bam_test.log"
    fi
}

# 执行主函数
main "$@"
