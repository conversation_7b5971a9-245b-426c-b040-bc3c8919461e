- name: create promtail conf.d
  file:
    path: "{{PROMTAIL_CONFIG_DIR}}/conf.d"
    state: directory
    owner: root
    group: promtail
    mode: 0640

- name: create promtail positions.yaml
  file:
    path: "{{PROMTAIL_CONFIG_DIR}}/positions.yaml"
    owner: root
    group: promtail
    mode: 0660
    state: touch
  notify:
  - reload promtail

- name: configure promtail
  template:
    src: promtail_nosuplog.yml.j2
    dest: "{{PROMTAIL_CONFIG_DIR}}/promtail.yml"
    force: true
    owner: root
    group: promtail
    mode: 0640
  notify:
  - reload promtail
  when: hostvars[inventory_hostname]['suplog'] is defined and hostvars[inventory_hostname]['suplog'] == 'no'

- name: configure promtail
  template:
    src: promtail.yml.j2
    dest: "{{PROMTAIL_CONFIG_DIR}}/promtail.yml"
    force: true
    owner: root
    group: promtail
    mode: 0640
  notify:
  - reload promtail
  when: hostvars[inventory_hostname]['suplog'] is not defined
