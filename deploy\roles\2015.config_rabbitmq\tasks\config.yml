---
- name: config | Configuring RabbitMQ
  template:
    src: "{{ rabbitmq_config_file }}"
    dest: "/etc/rabbitmq/rabbitmq.config"
    mode: 0644
    owner: rabbitmq
    group: rabbitmq 
  become: true
  notify: "restart rabbitmq-server"

- name: config | Configuring RabbitMQ environemnt
  template:
    src: "{{ rabbitmq_config_env_file }}"
    dest: "/etc/rabbitmq/rabbitmq-env.conf"
    mode: 0644
    owner: rabbitmq
    group: rabbitmq 
  become: true
  notify: "restart rabbitmq-server"
