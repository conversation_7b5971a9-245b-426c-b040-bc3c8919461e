---
# vars file for rabbitmq
erlang_series: 24

erlang_deb_repo_url: https://dl.cloudsmith.io/public/rabbitmq/rabbitmq-erlang/deb
erlang_deb_gpg_url: https://dl.cloudsmith.io/public/rabbitmq/rabbitmq-erlang/gpg.E495BB49CC4BBE5B.key
erlang_deb_repo_tpl: etc/apt/sources.list.d/rabbitmq_erlang.list.j2
erlang_deb_pinning_tpl: etc/apt/preferences.d/erlang.j2
erlang_series_deb_version:

rabbitmq_version: "3.9.13"
rabbitmq_delayed_message_exchange_version: "3.9.0"
# Defines if setting up a rabbitmq cluster
rabbitmq_enable_clustering: false
# Defines the inventory host that should be considered master
rabbitmq_master: None

# Define extra vhosts to be created
rabbitmq_extra_vhosts: []
# - name: /
#   state: present

#rabbitmq_plugins: "rabbitmq_event_exchange,rabbitmq_stream,rabbitmq_management,rabbitmq_prometheus,rabbitmq_recent_history_exchange,rabbitmq_shovel_management,rabbitmq_top,rabbitmq_web_mqtt_examples,rabbitmq_tracing,rabbitmq_top"

rabbitmq_deb: "rabbitmq-server_{{ rabbitmq_version }}-1_all.deb"
rabbitmq_deb_url: "https://packagecloud.io/rabbitmq/rabbitmq-server/packages/{{ ansible_distribution | lower }}/{{ ansible_distribution_release }}/{{ rabbitmq_deb }}/download"

rabbitmq_delayed_ez: "rabbitmq_delayed_message_exchange-{{rabbitmq_delayed_message_exchange_version}}.ez"
rabbitmq_delayed_ez_url: "https://filescfcdn.fwdev.top/common/rabbitmq_delayed_message_exchange-{{rabbitmq_delayed_message_exchange_version}}.ez"