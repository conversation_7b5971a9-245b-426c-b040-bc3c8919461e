{{ansible_managed|comment}}
http:
  middlewares:
    redirectIgoldhorseCnToCom:
      redirectRegex:
        permanent: true
        regex: "^https://XX.igoldhorse.cn/(.*)"
        replacement: "https://XX.igoldhorse.com/${1}"
    test-compress:
      compress:
        minResponseBodyBytes: 1800000
    hkProxy001IpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "***************"
        - "**************"
        - "*************"
        # tongyu
        - "*************"
        - "**************"
        - "***************"
        - "*************"
        - "**************"
        - "*************"
        - "*************"
        - "*************"
        - "************"
        - "*************"
        - "**************"
        - "************"
        # tiger
        - "*************"
        - "***************"
        #Easytrade
        - "*************"
        #Lcm
        - "**************"
  routers:
    open-api-gateway.igoldhorse.cn:
      service: hkProxy001Preproxy
      rule: Host(`open-api-gateway.igoldhorse.cn`)
      middlewares:
      - hkProxy001IpWhiteList
      - test-compress
      tls:
        certResolver: httpResolver
        domains:
        - main: open-api-gateway.igoldhorse.cn
  services:
    hkProxy001Preproxy:
      loadBalancer:
        servers:
        - url: http://HK-PROXY-001:8210
        - url: http://HK-MG-002:8210