---
- name: create postgres_exporter system group
  group:
    name: postgres_exporter
    system: true
    state: present
    gid: 3248

- name: create postgres_exporter system user
  user:
    name: postgres_exporter
    system: true
    shell: "/usr/sbin/nologin"
    group: postgres_exporter
    createhome: false
    uid: 3248

- name: postgres_exporter url
  debug:
    msg: "{{POSTGRES_EXPORTER_DOWNLOAD_URL}}"

- name: download postgres_exporter binary to local folder
  become: false
  get_url:
    use_proxy: true
    url: "{{POSTGRES_EXPORTER_DOWNLOAD_URL}}"
    dest: "/tmp/postgres_exporter-{{POSTGRES_EXPORTER_VERSION}}.tar.gz"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: unpack postgres_exporter binaries
  unarchive:
    src: "/tmp/postgres_exporter-{{POSTGRES_EXPORTER_VERSION}}.tar.gz"
    dest: "/tmp"
    remote_src: true
  check_mode: false

- name: copy official postgres_exporter binaries
  copy:
    src: "/tmp/postgres_exporter-{{POSTGRES_EXPORTER_VERSION}}.linux-amd64/{{item}}"
    dest: "/usr/local/bin/{{item}}"
    mode: '755'
    owner: root
    group: root
    remote_src: true
  with_items:
  - postgres_exporter
  notify:
  - restart postgres_exporter

- name: create systemd service unit
  template:
    src: postgres_exporter.service.j2
    dest: /etc/systemd/system/postgres_exporter.service
    owner: root
    group: root
    mode: 0644
  notify:
  - restart postgres_exporter

- name: enabled postgres_exporter systemd
  systemd:
    name: postgres_exporter
    daemon-reload: true
    enabled: true
