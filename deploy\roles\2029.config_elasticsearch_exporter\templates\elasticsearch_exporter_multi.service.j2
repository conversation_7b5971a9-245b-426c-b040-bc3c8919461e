[Unit]
Description=Elasticsearch Exporter (Multi-Node)
Documentation=https://github.com/prometheus-community/elasticsearch_exporter
Wants=network-online.target
After=network-online.target

[Service]
Type=simple
User={{ elasticsearch_exporter_user | default('prometheus') }}
Group={{ elasticsearch_exporter_group | default('prometheus') }}
ExecStart={{ elasticsearch_exporter_bin_path | default('/etc/elasticsearch_exporter/bin') }}/elasticsearch_exporter --web.listen-address={{ elasticsearch_exporter_ip | default('0.0.0.0') }}:{{ elasticsearch_exporter_port | default('9114') }} --web.telemetry-path=/{{ elasticsearch_exporter_web_telemetry_path | default('metrics') }} --es.uri={{ elasticsearch_exporter_es_cluster_uris[0] | default('http://*************:9200') }} --es.all={{ elasticsearch_exporter_es_all | default(true) | lower }} --es.indices={{ elasticsearch_exporter_es_indices | default(true) | lower }} --es.indices_settings={{ elasticsearch_exporter_es_indices_settings | default(true) | lower }} --es.shards={{ elasticsearch_exporter_es_shards | default(true) | lower }} --es.snapshots={{ elasticsearch_exporter_es_snapshots | default(true) | lower }} --es.timeout={{ elasticsearch_exporter_es_timeout | default('5s') }} --log.level={{ elasticsearch_exporter_log_level | default('info') }} --log.format={{ elasticsearch_exporter_log_format | default('logfmt') }}

SyslogIdentifier=elasticsearch_exporter
Restart=always
RestartSec=1
StartLimitInterval=0

{% if elasticsearch_exporter_private_tmp | default(true) %}
PrivateTmp=yes
{% endif %}

ProtectHome=yes
NoNewPrivileges=yes

ProtectSystem=strict
ProtectControlGroups=true
ProtectKernelModules=true
ProtectKernelTunables=yes

[Install]
WantedBy=multi-user.target
