groups:
- name: api-performance
  rules:
  # 慢接口告警 - 基于Promtail计数器
  - alert: SlowAPIRequests
    expr: increase(promtail_custom_api_slow_requests_total[5m]) > 0
    for: 0m
    labels:
      severity: warning
      api_path: "{{ $labels.slow_api_path }}"
    annotations:
      summary: "检测到慢接口请求 ({{ $labels.slow_api_path }})"
      description: "接口 {{ $labels.slow_api_path }} 在过去5分钟内有 {{ $value }} 次耗时超过1秒的请求"

  # 慢接口频率告警 - 5分钟内超过5次
  - alert: HighSlowAPIRequestRate
    expr: increase(promtail_custom_api_slow_requests_total[5m]) > 5
    for: 2m
    labels:
      severity: critical
      api_path: "{{ $labels.slow_api_path }}"
    annotations:
      summary: "接口性能严重下降 ({{ $labels.slow_api_path }})"
      description: "接口 {{ $labels.slow_api_path }} 在过去5分钟内有 {{ $value }} 次慢请求，性能严重下降"

  # API响应时间P95告警 - 基于直方图
  - alert: HighAPIResponseTimeP95
    expr: histogram_quantile(0.95, rate(promtail_custom_api_response_time_ms_bucket[5m])) > 1000
    for: 3m
    labels:
      severity: warning
      api_path: "{{ $labels.api_path }}"
    annotations:
      summary: "API P95响应时间过高 ({{ $labels.api_path }})"
      description: "接口 {{ $labels.api_path }} 的P95响应时间为 {{ $value }}ms，超过1秒阈值"

  # API响应时间P99告警
  - alert: HighAPIResponseTimeP99
    expr: histogram_quantile(0.99, rate(promtail_custom_api_response_time_ms_bucket[5m])) > 2000
    for: 2m
    labels:
      severity: critical
      api_path: "{{ $labels.api_path }}"
    annotations:
      summary: "API P99响应时间严重过高 ({{ $labels.api_path }})"
      description: "接口 {{ $labels.api_path }} 的P99响应时间为 {{ $value }}ms，超过2秒阈值"

  # API错误率告警 - 结合慢请求和总请求
  - alert: HighAPIErrorRate
    expr: (
        rate(promtail_custom_api_slow_requests_total[5m]) / 
        rate(promtail_custom_api_response_time_ms_count[5m])
      ) * 100 > 10
    for: 5m
    labels:
      severity: warning
      api_path: "{{ $labels.api_path }}"
    annotations:
      summary: "API慢请求比例过高 ({{ $labels.api_path }})"
      description: "接口 {{ $labels.api_path }} 的慢请求比例为 {{ $value }}%，超过10%阈值"

  # 无响应时间数据告警
  - alert: NoAPIResponseTimeData
    expr: absent_over_time(promtail_custom_api_response_time_ms_count[10m])
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "API响应时间监控数据缺失"
      description: "过去10分钟内没有收到API响应时间监控数据，可能是日志采集或解析出现问题"

  # 特定接口监控 - 可以根据需要添加特定接口的监控
  - alert: CriticalAPISlowResponse
    expr: increase(promtail_custom_api_slow_requests_total{slow_api_path=~"/api/critical/.*"}[1m]) > 0
    for: 0m
    labels:
      severity: critical
      api_path: "{{ $labels.slow_api_path }}"
    annotations:
      summary: "关键接口响应缓慢 ({{ $labels.slow_api_path }})"
      description: "关键接口 {{ $labels.slow_api_path }} 出现慢响应，需要立即处理"
