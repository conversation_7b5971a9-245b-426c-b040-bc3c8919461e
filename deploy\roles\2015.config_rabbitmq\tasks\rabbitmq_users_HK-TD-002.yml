---
- name: rabbitmq_plugins - installing plugin(s)
  rabbitmq_plugin:
    names: "{{ rabbitmq_plugins }}"
  become: true
  when: rabbitmq_plugins
  notify: restart rabbitmq-server
  
- name: rabbitmq_extra_vhosts | Create vhosts
  rabbitmq_vhost:
    name: "{{ item['name'] }}"
    state: "{{ item['state'] }}"
  become: true
  with_items: "{{ rabbitmq_extra_vhosts }}"
  register: rabbitmq_created_vhosts

- name: rabbitmq_extra_vhosts | Check guest administrator is present # noqa 503
  command: rabbitmqctl -q list_users
  become: true
  when: rabbitmq_created_vhosts.changed
  changed_when: false
  register: rabbitmq_existing_users

- name: rabbitmq_extra_vhosts | Give access to new vhosts to guest administrator
  command: "rabbitmqctl -q set_permissions -p {{ item['name'] }} guest '.*' '.*' '.*'"
  become: true
  with_items: "{{ rabbitmq_created_vhosts.results|selectattr('changed')|list }}"
  when:
    - item['state'] == 'present'
    - rabbitmq_existing_users.stdout_lines | map('regex_search', '^guest\\s\\[.*administrator.*\\]$') | list | difference([None]) | length > 0

- name: rabbitmq_users | creating rabbitmq users
  rabbitmq_user:
    name: "{{ item['name'] }}"
    password: "{{ item['password'] }}"
    vhost: "{{ item['vhost']|default(omit) }}"
    configure_priv: "{{ item['configure_priv']|default(omit) }}"
    read_priv: "{{ item['read_priv']|default(omit) }}"
    write_priv: "{{ item['write_priv']|default(omit) }}"
    tags: "{{ item['tags']|default(omit) }}"
    permissions: "{{ item['permissions']|default(omit) }}"
    state: present
  become: true
  run_once: yes
  loop: "{{ rabbitmq_users }}"
  loop_control:
    label: "{{ item.name }}"
  ignore_errors: yes



