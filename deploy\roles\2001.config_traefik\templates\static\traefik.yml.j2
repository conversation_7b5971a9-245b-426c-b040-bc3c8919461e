# file: /etc/traefik/traefik.yml
{{ansible_managed|comment}}

global:
  checkNewVersion: false
  sendAnonymousUsage: false
experimental:
  http3: true
pilot:
  dashboard: false
ping: {}
api:
  insecure: true
metrics:
  prometheus: {}
tracing: {}
log:
  filePath: /data/log/traefik/traefik.log
  level: info
accessLog:
  filePath: /data/log/traefik/access.log
  bufferingSize: 1000
providers:
  file:
    directory: /etc/traefik/conf.d
    watch: true
entryPoints:
  tcp:
    address: :65432
  http:
    address: :80
    http:
      redirections:
        entryPoint:
          to: https
          scheme: https
    forwardedHeaders:
      insecure: true
  https:
    address: :443
    http3:
      advertisedPort: 443
    forwardedHeaders:
      insecure: true
  traefik:
    address: :28282
certificatesResolvers:
  httpResolver:
    acme:
      email: <EMAIL>
      storage: /etc/traefik/acme.json
      keyType: EC256
      httpChallenge:
        entryPoint: http
  buypassResolver:
    acme:
      email: <EMAIL>
      storage: /etc/traefik/acme.buypassResolver.json
      keyType: EC256
      caServer: https://api.buypass.com/acme/directory
      httpChallenge:
        entryPoint: http
  letsEncryptStageResolver:
    acme:
      email: <EMAIL>
      storage: /etc/traefik/acme.letsEncryptStageResolver.json
      keyType: EC256
      caServer: https://acme-staging-v02.api.letsencrypt.org/directory
      httpChallenge:
        entryPoint: http
  cloudflareDnsResolver:
    acme:
      email: <EMAIL>
      storage: /etc/traefik/acme.cloudflareDnsResolver.json
      keyType: EC256
      dnsChallenge:
        provider: cloudflare
