{{ansible_managed|comment}}
http:
  routers:
    gqlsecret-lpoa-root:
      rule: Host(`gqlsecret.lpoa.igoldhorse.cn`) && Method(`GET`, `POST`) && Path(`/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      - requestAuthError
      - requestAuthSecret
      tls:
        certResolver: httpResolver
    gqlsecret-lpoa-root-cors:
      rule: Host(`gqlsecret.lpoa.igoldhorse.cn`) && Method(`OPTIONS`) && Path(`/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      tls:
        certResolver: httpResolver
    gqlsecret-lpoa-auth:
      rule: Host(`gqlsecret.lpoa.igoldhorse.cn`) && Method(`POST`) && Path(`/api/v1/gql/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      - requestAuthError
      - requestAuthSecret
      tls:
        certResolver: httpResolver
    gqlsecret-lpoa-cors:
      rule: Host(`gqlsecret.lpoa.igoldhorse.cn`) && Method(`OPTIONS`) && Path(`/api/v1/gql/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      - requestAuthError
      tls:
        certResolver: httpResolver
    gqlsecret-lpoa-intranet:
      rule: Host(`gqlsecret.lpoa.igoldhorse.cn`) && PathPrefix(`/-/ping/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      tls:
        certResolver: httpResolver
