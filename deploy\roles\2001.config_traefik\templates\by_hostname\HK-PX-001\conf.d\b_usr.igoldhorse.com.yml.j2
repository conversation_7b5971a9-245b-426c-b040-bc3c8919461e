{{ansible_managed|comment}}
http:
  middlewares:
    hkMg002UsrIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    user-center.igoldhorse.com:
      service: hkMg002BackendUsr
      rule: Host(`user-center.igoldhorse.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
        domains:
        - main: user-center.igoldhorse.com
  services:
    hkMg002BackendUsr:
      loadBalancer:
        servers:
        - url: http://HK-MG-002:8220
