.ghci_before_script: &ghci_before_script
- command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )
- ssh-agent -s > SSH-AGENT
- eval $(cat SSH-AGENT)
- echo "${PROD_DEPLOY_SSH_PRIVATE_KEY}" | base64 -d -w0 | tr -d '\r' | ssh-add -
- echo "${ANSIBLE_VAULT_PASSWORD_PROD_INFRA}" | base64 -d > "${ANSIBLE_VAULT_PASSWORD_FILE}"
- echo $(sha256sum "${ANSIBLE_VAULT_PASSWORD_FILE}")
- echo "${ID_ED25519_GH_PROD_GHCI_SSH_KEY}" | base64 -d -w0 | tr -d '\r' > ssh.key
- chmod 400 ssh.key
- echo ${ID_ED25519_GH_PROD_GHCI_SSH_PASSPHRASE} | base64 -d -w0 | tr -d '\r' > SSH_PASSPHRASE
- chmod 400 SSH_PASSPHRASE
- sshpass -f SSH_PASSPHRASE -P 'key:' ssh-add ssh.key
- export ANSIBLE_INVENTORY=hosts.ini
- export ANSIBLE_HOST_KEY_CHECKING=false
- export ANSIBLE_BECOME_PASS=$(echo $ANSIBLE_BECOME_PASS_BASE64 | base64 -d -w0 | tr -d '\r')

.deploy_script: &deploy_script_prod
  - cd deploy
  - export INSTANCE_NAME=$(echo ${CI_JOB_NAME} | cut -d':' -f 1)
  - |
    sshpass -e \
    ansible-playbook \
    10001.restart.yml \
    -i hosts.ini \
    -e TODO=${TODO} \
    -e @../vault.vars.yml \
    -e DEPLOY_HOSTS=${DEPLOYHOSTS} \
    -e INSTANCE_NAME=${INSTANCE_NAME} \
    -u ${ANSIBLE_USER:-ghci} \
    -b \
    ${ARGS} \
    ;

#mysql:
#  only:
#  - main
#  stage: service_restart
#  when: manual
#  needs: []
#  parallel:
#    matrix:
#      - DESC: 重启
#        TODO: restart
#        DEPLOYHOSTS: HK-TD-002
#  before_script: *ghci_before_script
#  script: *deploy_script_prod

prometheus:
  only:
  - main
  stage: service_restart
  when: manual
  needs: []
  parallel:
    matrix:
      - DESC: 重启
        TODO: restart
        DEPLOYHOSTS: SZ-ES-001,HK-MG-002
  before_script: *ghci_before_script
  script: *deploy_script_prod

grafana-server:
  only:
  - main
  stage: service_restart
  when: manual
  needs: []
  parallel:
    matrix:
      - DESC: 重启
        TODO: restart
        DEPLOYHOSTS: SZ-ES-001,HK-MG-002
  before_script: *ghci_before_script
  script: *deploy_script_prod
