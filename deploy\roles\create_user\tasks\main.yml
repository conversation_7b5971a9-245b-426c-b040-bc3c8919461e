- name: create user
  user:
    name: "{{item.name}}"
    shell: /bin/bash
    create_home: true
    password: "{{item.pwd}}"
    append: true
    state: "{{item.state}}"
    comment: '{{item.comment|default("")}}'
    groups:
    - '{{sudo_group if item.enable_sudo else ""}}'
  with_items:
  - "{{users}}"

- name: add ssh pub keys
  authorized_key:
    user: "{{item.name}}"
    key: "{{item.ssh_pubkey}}"
    state: "{{item.state}}"
  with_items:
  - "{{users}}"
  - "{{user_ssh_keys|default([])}}"
  ignore_errors: true
