{{ansible_managed|comment}}
http:
  middlewares:
    hkPx001AcclogoutIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    acclogout.igoldhorse.com:
      service: hkPx001CaddyAcclogout
      rule: Host(`acclogout.igoldhorse.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
  services:
    hkPx001CaddyAcclogout:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
