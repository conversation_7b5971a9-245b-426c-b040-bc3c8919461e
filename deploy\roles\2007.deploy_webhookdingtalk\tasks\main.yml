---
- name: create webhookdingtalk system group
  group:
    name: webhookdingtalk
    system: true
    state: present
    gid: 3239

- name: create data directory
  file:
    path: /data
    state: directory
    owner: root
    group: root
    mode: 0755

- name: create webhookdingtalk system user
  user:
    name: webhookdingtalk
    system: true
    shell: "/usr/sbin/nologin"
    group: webhookdingtalk
    createhome: false
    home: "{{ WEBHOOKDINGTALK_DB_DIR }}"
    uid: 3239

- name: create webhookdingtalk data directory
  file:
    path: "{{ WEBHOOKDINGTALK_DB_DIR }}"
    state: directory
    recurse: true
    owner: webhookdingtalk
    group: webhookdingtalk
    mode: 0755

- name: create webhookdingtalk configuration directories
  file:
    path: "{{ item }}"
    state: directory
    owner: root
    group: webhookdingtalk
    mode: 0770
  with_items:
  - "{{ WEBHOOKDINGTALK_CONFIG_DIR }}"

- name: create webhookdingtalk templates directories
  file:
    path: "{{ item }}"
    state: directory
    owner: root
    group: webhookdingtalk
    mode: 0770
  with_items:
  - "{{ WEBHOOKDINGTALK_TEMPL_DIR }}"

- name: webhookdingtalk url
  debug:
    msg: "{{WEBHOOKDINGTALK_DOWNLOAD_URL}}"

- name: download webhookdingtalk binary to local folder
  become: false
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "{{WEBHOOKDINGTALK_DOWNLOAD_URL}}"
    dest: "/tmp/webhookdingtalk-{{WEBHOOKDINGTALK_VERSION}}.tar.gz"
    checksum: "{{WEBHOOKDINGTALK_CHECKSUM}}"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: unpack webhookdingtalk binaries
  unarchive:
    src: "/tmp/webhookdingtalk-{{WEBHOOKDINGTALK_VERSION}}.tar.gz"
    dest: "/tmp"
    remote_src: true
  check_mode: false

- name: copy official webhookdingtalk binaries
  copy:
    src: "/tmp/prometheus-webhook-dingtalk-{{WEBHOOKDINGTALK_VERSION}}.linux-amd64/{{item}}"
    dest: "/usr/local/bin/{{item}}"
    mode: '755'
    owner: root
    group: root
    remote_src: true
  with_items:
  - prometheus-webhook-dingtalk
  notify:
  - restart webhookdingtalk

- name: create systemd service unit
  template:
    src: webhookdingtalk.service.j2
    dest: /etc/systemd/system/webhookdingtalk.service
    owner: root
    group: root
    mode: 0644
  notify:
  - restart webhookdingtalk

- name: ensure on boot
  systemd:
    name: webhookdingtalk
    enabled: true
    daemon_reload: true
    state: started
