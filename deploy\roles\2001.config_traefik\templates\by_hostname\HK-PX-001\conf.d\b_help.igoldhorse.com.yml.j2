{{ansible_managed|comment}}
http:
  middlewares:
    hkPx001HelpIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    help.igoldhorse.com:
      service: hkPx001CaddyHelp
      rule: Host(`help.igoldhorse.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
  services:
    hkPx001CaddyHelp:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
