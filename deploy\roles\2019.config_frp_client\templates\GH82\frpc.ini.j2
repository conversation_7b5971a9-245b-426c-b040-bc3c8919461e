# /etc/frp/fprc.ini

{{ansible_managed|comment}}

[common]
#server_addr = hgsz.ignorelist.com
server_addr = hgsz.crabdance.com
server_port = {{FRPS_PORT}}
token = {{FRP_AUTH}}
protocol = kcp
enable_prometheus = true

dashboard_addr = 0.0.0.0
dashboard_port = {{FRPS_PORT+1}}
dashboard_user = {{FRPS_DASHBOARD_USER}}
dashboard_pwd = {{FRPS_DASHBOARD_PASSWORD}}

admin_addr = 0.0.0.0
admin_port = {{FRPS_PORT+2}}
admin_user = {{FRPS_ADMIN_USER}}
admin_pwd = {{FRPS_ADMIN_PASSWORD}}

[gh81:user:44645]
local_ip = ************
local_port = 44645
remote_port = 44645
type = tcp
use_encryption = true
use_compression = true

[gh81:device:45942]
local_ip = ************
local_port = 45942
remote_port = 45942
type = tcp
use_encryption = true
use_compression = true

[gh81:trade:17001]
local_ip = ************
local_port = 17001
remote_port = 17001
type = tcp
use_encryption = true
use_compression = true

[gh81:datahouse:21001]
local_ip = ************
local_port = 21001
remote_port = 21001
type = tcp
use_encryption = true
use_compression = true

[gh81:redis:63798]
local_ip = ************
local_port = 6379
remote_port = 63798
type = tcp
use_encryption = true
use_compression = true

[gh81:userstudio:35366]
local_ip = ************
local_port = 35366
remote_port = 35366
type = tcp
use_encryption = true
use_compression = true

[gh81:userstudio:10001]
local_ip = ************
local_port = 10001
remote_port = 10001
type = tcp
use_encryption = true
use_compression = true

[gh81:rsync:873]
local_ip = ************
local_port = 873
remote_port = 11873
type = tcp
use_encryption = true
use_compression = true

[fwgitlab:gitlab:10081]
local_ip = **************
local_port = 10081
remote_port = 10081
type = tcp
use_encryption = true
use_compression = true

[sshgitlab:gitlab:22228]
local_ip = **************
local_port = 22
remote_port = 22228
type = tcp
use_encryption = true
use_compression = true

[q16:register:5000]
local_ip = ************
local_port = 5000
remote_port = 5000
type = tcp
use_encryption = true
use_compression = true

[q16:nexus3:8081]
local_ip = ************
local_port = 8081
remote_port = 8081
type = tcp
use_encryption = true
use_compression = true
