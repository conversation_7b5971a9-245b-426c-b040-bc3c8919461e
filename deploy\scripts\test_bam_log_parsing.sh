#!/bin/bash

# BAM日志解析测试脚本
# 用法: ./test_bam_log_parsing.sh [log_file]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 生成测试日志
generate_test_log() {
    local test_log="/tmp/bam_test_parsing.log"
    
    cat > "$test_log" << EOF
2025-08-01 10:59:28.381 [vt88g2oYUvvoyMoY] INFO  (ResponseResultAdvice.java:131) - request url:[</bam/v2/customerDetails/dataUpdateInfoForStatus>], from: [<132.147.99.130>] request info: { body: [<{"customerKey":"1452","nonce":"af7035bd6092fde07bcb6c02d6e8359e","publicEmail":"***@redbeaconam.com","publicSource":1,"timestamp":"*************","accountId":745,"language":0,"signature":"3a11d4539aad494b7e57f82990ba5cc1ce2776cb","rootOrgId":75,"deviceType":"2"}>], sha1: [<8ca8bc67f908d73fee838a61075ac8292feab942>], length: [<265>]},response info: { body: [<{"datas":{"updateStatus":2,"updateStopAccountCount":0,"updatedAccountCount":2,"updatingAccountCount":1},"message":"Successful call","status":10000}>], sha1: [<6258847b9eeaf0573d3ad2d14a2f744171ccfc18>], length: [<147>] }, start: [<*************>], end: [<*************>], use: [<808>]ms
2025-08-01 11:00:15.234 [abc123def456] INFO  (ResponseResultAdvice.java:131) - request url:[</bam/v2/user/login>], from: [<192.168.1.100>] request info: { body: [<{"username":"test","password":"***"}>], sha1: [<hash123>], length: [<50>]},response info: { body: [<{"status":"success","token":"***"}>], sha1: [<hash456>], length: [<100>] }, start: [<*************>], end: [<*************>], use: [<150>]ms
2025-08-01 11:01:30.567 [def789ghi012] WARN  (ResponseResultAdvice.java:131) - request url:[</bam/v2/report/generate>], from: [<10.0.0.50>] request info: { body: [<{"reportType":"monthly","params":{...}}>], sha1: [<hash789>], length: [<200>]},response info: { body: [<{"reportId":"12345","status":"processing"}>], sha1: [<hash012>], length: [<80>] }, start: [<*************>], end: [<************0>], use: [<1500>]ms
2025-08-01 11:02:45.890 [ghi345jkl678] ERROR (ResponseResultAdvice.java:131) - request url:[</bam/v2/payment/process>], from: [<172.16.0.10>] request info: { body: [<{"amount":1000,"currency":"USD"}>], sha1: [<hash345>], length: [<100>]},response info: { body: [<{"error":"timeout","code":500}>], sha1: [<hash678>], length: [<50>] }, start: [<1754017365000>], end: [<1754017368000>], use: [<3000>]ms
2025-08-01 11:03:12.123 [jkl901mno234] INFO  (ResponseResultAdvice.java:131) - request url:[</bam/v2/health/check>], from: [<127.0.0.1>] request info: { body: [<{}>], sha1: [<hash901>], length: [<2>]},response info: { body: [<{"status":"ok"}>], sha1: [<hash234>], length: [<15>] }, start: [<1754017392000>], end: [<1754017392020>], use: [<20>]ms
2025-08-01 11:04:05.456 [mno567pqr890] WARN  (ResponseResultAdvice.java:131) - request url:[</bam/v2/data/export>], from: [<10.1.1.100>] request info: { body: [<{"format":"csv","filters":{...}}>], sha1: [<hash567>], length: [<150>]},response info: { body: [<{"downloadUrl":"https://..."}>], sha1: [<hash890>], length: [<60>] }, start: [<1754017445000>], end: [<1754017446200>], use: [<1200>]ms
2025-08-01 11:05:18.789 [pqr123stu456] INFO  (ResponseResultAdvice.java:131) - request url:[</bam/v2/config/get>], from: [<192.168.10.50>] request info: { body: [<{"configType":"system"}>], sha1: [<hash123>], length: [<30>]},response info: { body: [<{"config":{...}}>], sha1: [<hash456>], length: [<500>] }, start: [<1754017518000>], end: [<1754017518080>], use: [<80>]ms
EOF

    echo "$test_log"
}

# 测试正则表达式匹配
test_regex_matching() {
    local log_file="$1"
    
    log_header "测试正则表达式匹配"
    
    # 定义正则表达式
    local regex='.*request url:\[<([^>]+)>\].*use: \[<([0-9]+)>\]ms.*'
    local full_regex='.*request url:\[<(?P<api_path>[^>]+)>\].*from: \[<(?P<client_ip>[^>]+)>\].*use: \[<(?P<response_time>\d+)>\]ms.*'
    
    log_info "使用正则表达式: $regex"
    echo ""
    
    local total_lines=0
    local matched_lines=0
    local slow_requests=0
    
    while IFS= read -r line; do
        if echo "$line" | grep -q "ResponseResultAdvice.*request url.*use:"; then
            ((total_lines++))
            
            # 提取API路径和响应时间
            if echo "$line" | grep -qE "$regex"; then
                ((matched_lines++))
                
                local api_path=$(echo "$line" | sed -n 's/.*request url:\[<\([^>]*\)>\].*/\1/p')
                local response_time=$(echo "$line" | sed -n 's/.*use: \[<\([0-9]*\)>\]ms.*/\1/p')
                local client_ip=$(echo "$line" | sed -n 's/.*from: \[<\([^>]*\)>\].*/\1/p')
                local request_id=$(echo "$line" | sed -n 's/.*\[\([^\]]*\)\] [A-Z]*.*/\1/p')
                
                echo "✓ 匹配成功:"
                echo "  API路径: $api_path"
                echo "  响应时间: ${response_time}ms"
                echo "  客户端IP: $client_ip"
                echo "  请求ID: $request_id"
                
                # 检查是否为慢请求
                if [ "$response_time" -gt 1000 ]; then
                    ((slow_requests++))
                    echo "  ⚠ 慢请求: 是 (>${response_time}ms > 1000ms)"
                else
                    echo "  ✓ 慢请求: 否 (${response_time}ms <= 1000ms)"
                fi
                echo ""
            else
                echo "✗ 匹配失败: $line"
                echo ""
            fi
        fi
    done < "$log_file"
    
    log_info "匹配统计:"
    echo "  总日志行数: $total_lines"
    echo "  成功匹配: $matched_lines"
    echo "  慢请求数: $slow_requests"
    echo "  匹配率: $(( matched_lines * 100 / total_lines ))%"
    
    if [ $matched_lines -eq $total_lines ]; then
        log_info "✓ 所有日志行都成功匹配"
        return 0
    else
        log_warn "⚠ 部分日志行匹配失败"
        return 1
    fi
}

# 测试Promtail配置语法
test_promtail_config() {
    log_header "测试Promtail配置语法"
    
    local config_file="/tmp/test_promtail_config.yml"
    
    # 生成测试配置
    cat > "$config_file" << 'EOF'
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://localhost:3100/loki/api/v1/push

scrape_configs:
- job_name: bam_api_test
  static_configs:
  - targets:
    - localhost
    labels:
      job: bam-api-test
      __path__: /tmp/bam_test_parsing.log
  pipeline_stages:
  - match:
      selector: '{job="bam-api-test"} |~ "ResponseResultAdvice.*request url.*use:"'
      stages:
      - regex:
          expression: '.*request url:\[<(?P<api_path>[^>]+)>\].*from: \[<(?P<client_ip>[^>]+)>\].*use: \[<(?P<response_time>\d+)>\]ms.*'
      - labels:
          api_path: api_path
          client_ip: client_ip
      - metrics:
          bam_api_response_time_ms:
            type: Histogram
            description: BAM API response time
            source: response_time
            config:
              buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000]
      - template:
          source: is_slow
          template: |
            {{- if gt (float64 .response_time) 1000.0 -}}
              1
            {{- else -}}
              0
            {{- end -}}
      - metrics:
          bam_api_slow_requests_total:
            type: Counter
            description: Slow BAM API requests
            source: is_slow
            config:
              value: "1"
              action: inc
EOF

    # 检查配置语法
    if command -v promtail &> /dev/null; then
        log_info "检查Promtail配置语法..."
        if promtail -config.file="$config_file" -dry-run 2>/dev/null; then
            log_info "✓ Promtail配置语法正确"
        else
            log_error "✗ Promtail配置语法错误"
            promtail -config.file="$config_file" -dry-run
            return 1
        fi
    else
        log_warn "⚠ Promtail未安装，跳过语法检查"
    fi
    
    # 检查YAML语法
    if command -v python3 &> /dev/null; then
        log_info "检查YAML语法..."
        if python3 -c "import yaml; yaml.safe_load(open('$config_file'))" 2>/dev/null; then
            log_info "✓ YAML语法正确"
        else
            log_error "✗ YAML语法错误"
            return 1
        fi
    fi
    
    rm -f "$config_file"
    return 0
}

# 生成性能分析报告
generate_performance_report() {
    local log_file="$1"
    
    log_header "生成性能分析报告"
    
    local report_file="/tmp/bam_performance_report.txt"
    
    # 分析日志数据
    local total_requests=0
    local slow_requests=0
    local total_time=0
    local min_time=999999
    local max_time=0
    
    declare -A api_stats
    declare -A ip_stats
    
    while IFS= read -r line; do
        if echo "$line" | grep -q "ResponseResultAdvice.*request url.*use:"; then
            local api_path=$(echo "$line" | sed -n 's/.*request url:\[<\([^>]*\)>\].*/\1/p')
            local response_time=$(echo "$line" | sed -n 's/.*use: \[<\([0-9]*\)>\]ms.*/\1/p')
            local client_ip=$(echo "$line" | sed -n 's/.*from: \[<\([^>]*\)>\].*/\1/p')
            
            if [ -n "$api_path" ] && [ -n "$response_time" ]; then
                ((total_requests++))
                total_time=$((total_time + response_time))
                
                # 更新最小最大时间
                if [ "$response_time" -lt "$min_time" ]; then
                    min_time=$response_time
                fi
                if [ "$response_time" -gt "$max_time" ]; then
                    max_time=$response_time
                fi
                
                # 统计慢请求
                if [ "$response_time" -gt 1000 ]; then
                    ((slow_requests++))
                fi
                
                # 统计API路径
                if [ -n "${api_stats[$api_path]}" ]; then
                    api_stats[$api_path]=$((${api_stats[$api_path]} + 1))
                else
                    api_stats[$api_path]=1
                fi
                
                # 统计IP
                if [ -n "${ip_stats[$client_ip]}" ]; then
                    ip_stats[$client_ip]=$((${ip_stats[$client_ip]} + 1))
                else
                    ip_stats[$client_ip]=1
                fi
            fi
        fi
    done < "$log_file"
    
    # 计算平均响应时间
    local avg_time=0
    if [ "$total_requests" -gt 0 ]; then
        avg_time=$((total_time / total_requests))
    fi
    
    # 生成报告
    cat > "$report_file" << EOF
BAM API性能分析报告
生成时间: $(date)
日志文件: $log_file

=== 总体统计 ===
总请求数: $total_requests
慢请求数: $slow_requests (>1000ms)
慢请求率: $(( slow_requests * 100 / total_requests ))%

响应时间统计:
- 最小值: ${min_time}ms
- 最大值: ${max_time}ms
- 平均值: ${avg_time}ms

=== API路径统计 ===
EOF

    # 添加API统计
    for api in "${!api_stats[@]}"; do
        echo "$api: ${api_stats[$api]} 次" >> "$report_file"
    done
    
    echo "" >> "$report_file"
    echo "=== 客户端IP统计 ===" >> "$report_file"
    
    # 添加IP统计
    for ip in "${!ip_stats[@]}"; do
        echo "$ip: ${ip_stats[$ip]} 次" >> "$report_file"
    done
    
    echo "" >> "$report_file"
    echo "=== 监控建议 ===" >> "$report_file"
    
    if [ "$slow_requests" -gt 0 ]; then
        echo "⚠ 发现 $slow_requests 个慢请求，建议:" >> "$report_file"
        echo "  1. 检查慢请求对应的API接口性能" >> "$report_file"
        echo "  2. 分析数据库查询和外部服务调用" >> "$report_file"
        echo "  3. 考虑添加缓存或优化算法" >> "$report_file"
    else
        echo "✓ 未发现慢请求，API性能良好" >> "$report_file"
    fi
    
    if [ "$max_time" -gt 5000 ]; then
        echo "⚠ 发现超长响应时间(${max_time}ms)，需要重点关注" >> "$report_file"
    fi
    
    log_info "性能分析报告已生成: $report_file"
    cat "$report_file"
    
    return 0
}

# 主函数
main() {
    local log_file="${1:-}"
    
    echo "========================================"
    echo "BAM日志解析测试"
    echo "测试时间: $(date)"
    echo "========================================"
    echo ""
    
    # 如果没有提供日志文件，生成测试数据
    if [ -z "$log_file" ] || [ ! -f "$log_file" ]; then
        log_warn "未提供日志文件或文件不存在，使用测试数据"
        log_file=$(generate_test_log)
        log_info "测试日志文件: $log_file"
        echo ""
    fi
    
    # 执行测试
    if test_regex_matching "$log_file"; then
        log_info "✓ 正则表达式测试通过"
    else
        log_error "✗ 正则表达式测试失败"
    fi
    echo ""
    
    if test_promtail_config; then
        log_info "✓ Promtail配置测试通过"
    else
        log_error "✗ Promtail配置测试失败"
    fi
    echo ""
    
    generate_performance_report "$log_file"
    echo ""
    
    log_info "测试完成！"
    echo ""
    echo "下一步操作:"
    echo "1. 运行 ./setup_bam_api_monitoring.sh 配置监控"
    echo "2. 检查 Promtail 指标: curl http://localhost:19080/metrics"
    echo "3. 查看 Prometheus 规则: curl http://localhost:9090/api/v1/rules"
    echo ""
    
    # 清理测试文件
    if [ -f "/tmp/bam_test_parsing.log" ]; then
        rm -f "/tmp/bam_test_parsing.log"
    fi
}

# 执行主函数
main "$@"
