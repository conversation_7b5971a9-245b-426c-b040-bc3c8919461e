# https://github.com/prometheus-community/elasticsearch_exporter
groups:
- name: elasticsearch
  rules:
  - alert: ElasticsearchClusterRed
    expr: elasticsearch_cluster_health_status{color="red"} == 1
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: Elasticsearch cluster red (instance {{ $labels.instance }})
      description: "Elasticsearch cluster health is RED\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchClusterYellow
    expr: elasticsearch_cluster_health_status{color="yellow"} == 1
    for: 0m
    labels:
      severity: warning
    annotations:
      summary: Elasticsearch cluster yellow (instance {{ $labels.instance }})
      description: "Elasticsearch cluster health is YELLOW\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchNodeDown
    expr: up{job="elasticsearch"} == 0
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: Elasticsearch node down (instance {{ $labels.instance }})
      description: "Elasticsearch node is down\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchDiskOutOfSpace
    expr: elasticsearch_filesystem_data_available_bytes / elasticsearch_filesystem_data_size_bytes * 100 < 10
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: Elasticsearch disk out of space (instance {{ $labels.instance }})
      description: "The disk usage is over 90%\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchDiskSpaceLow
    expr: elasticsearch_filesystem_data_available_bytes / elasticsearch_filesystem_data_size_bytes * 100 < 20
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: Elasticsearch disk space low (instance {{ $labels.instance }})
      description: "The disk usage is over 80%\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchClusterInitializingShards
    expr: elasticsearch_cluster_health_initializing_shards > 0
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: Elasticsearch cluster initializing shards (instance {{ $labels.instance }})
      description: "Elasticsearch is initializing shards\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchClusterRelocatingShards
    expr: elasticsearch_cluster_health_relocating_shards > 0
    for: 10m
    labels:
      severity: info
    annotations:
      summary: Elasticsearch cluster relocating shards (instance {{ $labels.instance }})
      description: "Elasticsearch is relocating shards\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchClusterUnassignedShards
    expr: elasticsearch_cluster_health_unassigned_shards > 0
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: Elasticsearch cluster unassigned shards (instance {{ $labels.instance }})
      description: "Elasticsearch cluster has unassigned shards\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchClusterPendingTasks
    expr: elasticsearch_cluster_health_number_of_pending_tasks > 0
    for: 15m
    labels:
      severity: warning
    annotations:
      summary: Elasticsearch cluster pending tasks (instance {{ $labels.instance }})
      description: "Elasticsearch cluster has pending tasks. Cluster works slowly.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchNoNewDocuments
    expr: increase(elasticsearch_indices_docs{es_data_node="true"}[10m]) < 1
    for: 0m
    labels:
      severity: warning
    annotations:
      summary: Elasticsearch no new documents (instance {{ $labels.instance }})
      description: "No new documents for 10 min!\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchTooFewNodesRunning
    expr: elasticsearch_cluster_health_number_of_nodes < 3
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: Elasticsearch too few nodes running (instance {{ $labels.instance }})
      description: "There are only {{$value}} < 3 Elasticsearch nodes running\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchHeapUsageTooHigh
    expr: (elasticsearch_jvm_memory_used_bytes{area="heap"} / elasticsearch_jvm_memory_max_bytes{area="heap"}) * 100 > 90
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: Elasticsearch Heap usage too high (instance {{ $labels.instance }})
      description: "The heap usage is over 90%\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchHeapUsageWarning
    expr: (elasticsearch_jvm_memory_used_bytes{area="heap"} / elasticsearch_jvm_memory_max_bytes{area="heap"}) * 100 > 80
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: Elasticsearch Heap usage warning (instance {{ $labels.instance }})
      description: "The heap usage is over 80%\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchDiskWatermarkLow
    expr: elasticsearch_cluster_health_active_shards > (elasticsearch_cluster_health_active_shards + elasticsearch_cluster_health_unassigned_shards) * 0.85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Elasticsearch disk watermark low (instance {{ $labels.instance }})
      description: "The cluster has hit the low watermark. Some shards will be re-allocated\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ElasticsearchDiskWatermarkHigh
    expr: elasticsearch_cluster_health_active_shards > (elasticsearch_cluster_health_active_shards + elasticsearch_cluster_health_unassigned_shards) * 0.90
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: Elasticsearch disk watermark high (instance {{ $labels.instance }})
      description: "The cluster has hit the high watermark. Some shards will be re-allocated\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
