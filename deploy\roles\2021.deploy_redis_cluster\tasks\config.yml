---
- name: Create log directory
  file:
    path: /var/log/redis
    state: directory
    owner: redis
    group: redis
    mode: 0755

- name: Check if redis log files exists
  stat: 
    path: "/var/log/redis/{{ item.port }}.log"
  with_items: "{{ redis_cluster_instances }}"
  register: check_logs

- name: Create log files
  file:
    path: "{{ item.invocation.module_args.path }}"
    state: touch
    owner: redis
    group: redis
    mode: 0755
  when: not item.stat.exists
  with_items: "{{ check_logs.results }}"

- name: Ensure log files permissions
  file:
    path: "/var/log/redis/{{ item.port }}.log"
    state: file
    owner: redis
    group: redis
    mode: 0755
  with_items: "{{ redis_cluster_instances }}"

- name: Ensure /var/run/redis directory exists
  file:
    path: /var/run/redis
    state: directory
    owner: redis
    group: redis
    mode: 0755

- name: Ensure /etc/redis directory and it's permissions exists
  file:
    path: /etc/redis
    state: directory
    owner: redis
    group: redis
    mode: 0755

- name: Ensure /var/lib/redis directory and it's permissions exists
  file:
    path: /var/lib/redis
    state: directory
    owner: redis
    group: redis
    mode: 0755
    
- name: Copy and build Redis config
  template:
    src: redis.conf.j2
    dest: /etc/redis/{{ item.port }}.conf
  with_items: "{{ redis_cluster_instances }}"
  notify: restart redis

- name: Copy and parse redis systemd unit file
  template:
    src: redis-server.service.j2
    dest: /lib/systemd/system/redis-server@.service
    mode: 0644
  with_items: "{{ redis_cluster_instances }}"
  notify: restart redis
   
- name: Ensure redis-server service is up
  systemd:
    name: "redis-server@{{ item.port }}"
    enabled: yes
    state: started
    daemon_reload: yes
  with_items: "{{ redis_cluster_instances }}"

 

