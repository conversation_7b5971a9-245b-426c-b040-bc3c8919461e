.ghci_before_script: &ghci_before_script
- command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )
- ssh-agent -s > SSH-AGENT
- eval $(cat SSH-AGENT)
- echo "${PROD_DEPLOY_SSH_PRIVATE_KEY}" | base64 -d -w0 | tr -d '\r' | ssh-add -
- echo "${ANSIBLE_VAULT_PASSWORD_PROD_INFRA}" | base64 -d > "${ANSIBLE_VAULT_PASSWORD_FILE}"
- echo $(sha256sum "${ANSIBLE_VAULT_PASSWORD_FILE}")
- echo "${ID_ED25519_GH_PROD_GHCI_SSH_KEY}" | base64 -d -w0 | tr -d '\r' > ssh.key
- chmod 400 ssh.key
- echo ${ID_ED25519_GH_PROD_GHCI_SSH_PASSPHRASE} | base64 -d -w0 | tr -d '\r' > SSH_PASSPHRASE
- chmod 400 SSH_PASSPHRASE
- sshpass -f SSH_PASSPHRASE -P 'key:' ssh-add ssh.key
- export ANSIBLE_INVENTORY=hosts.ini
- export ANSIBLE_HOST_KEY_CHECKING=false
- export ANSIBLE_BECOME_PASS=$(echo $ANSIBLE_BECOME_PASS_BASE64 | base64 -d -w0 | tr -d '\r')
- mkdir -p /etc/ansible/callback_plugins
- wget -P /etc/ansible/callback_plugins https://raw.githubusercontent.com/jlafon/ansible-profile/master/callback_plugins/profile_tasks.py
- |
  cat > /etc/ansible/ansible.cfg << EOF
  [defaults]
  callbacks_enabled = profile_tasks
  EOF


2001.traefik:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2001.traefik.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_TRAEFIK_HOSTS=${DEPLOY_TRAEFIK_HOSTS:-SH-PX-001,HK-PX-001,SZ-PX-001,HK-PROXY-001} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2002.node_exporter:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2002.node_exporter.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_NODE_EXPORTER_HOSTS=${DEPLOY_NODE_EXPORTER_HOSTS:-all_servers} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2003.promtail:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2003.promtail.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_PROMTAIL_HOSTS=${DEPLOY_PROMTAIL_HOSTS:-all_servers} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

20031.loki:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    pb_loki.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_LOKI_HOSTS=${DEPLOY_LOKI_HOSTS:-SZ-ES-001,HK-MG-002,SZ-MONT-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

20032.yearning:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    pb_yearning.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_YEARNING_HOSTS=${DEPLOY_YEARNING_HOSTS:-HK-MG-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2004.grafana:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2004.grafana.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_GRAFANA_HOSTS=${DEPLOY_GRAFANA_HOSTS:-SZ-ES-001,HK-MG-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2005.prometheus:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2005.prometheus.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_PROMETHEUS_HOSTS=${DEPLOY_PROMETHEUS_HOSTS:-SZ-MONT-002,HK-MG-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2006.alertmanager:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2006.alertmanager.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_ALERTMANAGER_HOSTS=${DEPLOY_ALERTMANAGER_HOSTS:-SZ-ES-001,HK-MG-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2007.webhookdingtalk:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2007.webhookdingtalk.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_WEBHOOKDINGTALK_HOSTS=${DEPLOY_WEBHOOKDINGTALK_HOSTS:-SZ-ES-001,HK-MG-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2008.haproxy:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2008.haproxy.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_HAPROXY_HOSTS=${DEPLOY_HAPROXY_HOSTS:-AYS-GW-001} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2009.rsync:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2009.rsync.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_RSYNC_HOSTS=${DEPLOY_RSYNC_HOSTS:-HK-TD-002,HK-MG-001} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2010.caddy:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2010.caddy.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_CADDY_HOSTS=${DEPLOY_CADDY_HOSTS:-HK-PX-001,SZ-ES-001,HK-MG-001,HK-MG-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2011.minio:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2011.minio.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_MINIO_HOSTS=${DEPLOY_MINIO_HOSTS:-HK-MG-002,SZ-PUSH-001,HK-TD-002,SZ-NEWS-001} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2012.jaeger_agent:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2012.jaeger_agent.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_JAEGER_AGENT_HOSTS=${DEPLOY_JAEGER_AGENT_HOSTS:-all_servers} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2013.blackbox_exporter:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2013.blackbox_exporter.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_BLACKBOX_EXPORTER_HOSTS=${DEPLOY_BLACKBOX_EXPORTER_HOSTS:-all_servers} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2014.redis_sentinel:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2014.redis_sentinel.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_REDIS_SENTINEL_HOSTS=${DEPLOY_REDIS_SENTINEL_HOSTS:-HK-TD-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2015.rabbitmq:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2015.rabbitmq.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_RABBITMQ_HOSTS=${DEPLOY_RABBITMQ_HOSTS:-HK-TD-002,SZ-OPACC-001,SZ-MKTAPI-001,HK-MG-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2016.nacos:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2016.nacos.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_NACOS_HOSTS=${DEPLOY_NACOS_HOSTS:-SZ-OPACC-001,HK-TD-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2012.mysql:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2012.mysql.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_MYSQL_HOSTS=${DEPLOY_MYSQL_HOSTS:-HK-TD-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2017.privoxy:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2017.privoxy.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_PRIVOXY_HOSTS=${DEPLOY_PRIVOXY_HOSTS:-SZ-ES-001,HK-MG-001} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2018.cron:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2018.cron.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_CRON_HOSTS=${DEPLOY_CRON_HOSTS:-SZ-OPACC-001} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2019.frp_client:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2019.frp_client.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_FRP_CLIENT_HOSTS=${DEPLOY_FRP_CLIENT_HOSTS:-SZ-CAL-001,gh81,gh82,HK-TD-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2020.frp_server:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2020.frp_server.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_FRP_SERVER_HOSTS=${DEPLOY_FRP_SERVER_HOSTS:-SH-PX-001,HK-PX-001,SZ-ES-001,HK-MG-002,SZ-OPACC-001,HK-MG-001,HK-TD-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2027.redis_exporter:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2027.redis_exporter.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_REDISEXPORTER_HOSTS=${DEPLOY_REDISEXPORTER_HOSTS:-HK-MG-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2028.mysql_exporter:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2028.mysql_exporter.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_MYSQLEXPORTER_HOSTS=${DEPLOY_MYSQLEXPORTER_HOSTS:-HK-TD-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2029.elasticsearch_exporter:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2029.elasticsearch_exporter.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_ELASTICSEARCH_EXPORTER_HOSTS=${DEPLOY_ELASTICSEARCH_EXPORTER_HOSTS:-SZ-ES-001} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2031.kafka_exporter:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2031.kafka_exporter.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_KAFKAEXPORTER_HOSTS=${DEPLOY_KAFKAEXPORTER_HOSTS:-SZ-KAFKA-001,SZ-KAFKA-002,SZ-KAFKA-003} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2032.deploy_mydumper:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2032.deploy_mydumper.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_MYDUMPER_HOSTS=${DEPLOY_MYDUMPER_HOSTS:-HK-TD-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2033.deploy_sqlpad:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2033.deploy_sqlpad.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_SQLPAD_HOSTS=${DEPLOY_SQLPAD_HOSTS:-HK-MG-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2034.deploy_clickhouse:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2034.deploy_clickhouse.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_CLHOUSE_HOSTS=${DEPLOY_CLHOUSE_HOSTS:-SZ-CLHOUSE-001,SZ-CLHOUSE-002,SZ-CLHOUSE-003} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2036.deploy_seata:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2036.deploy_seata.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_SEATA_HOSTS=${DEPLOY_SEATA_HOSTS:-HK-TD-002} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2039.rocketmq:
  only:
  - main
  stage: system_service
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2039.rocketmq.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_RKTMQ_HOSTS=${DEPLOY_RKTMQ_HOSTS:-SZ-DERIV-001} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2040.rocketmqexporter:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2040.rocketmqexpt.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_RKTMQEXPT_HOSTS=${DEPLOY_RKTMQEXPT_HOSTS:-SZ-DERIV-001} \
    -u ${ANSIBLE_USER:-ghci} \
    -b

2041.jmxexporter:
  only:
  - main
  stage: monitoring and logging
  tags:
  - prod-deploy
  when: manual
  needs: []
  before_script: *ghci_before_script
  script:
  - cd deploy
  - |
    sshpass -e \
    ansible-playbook \
    2041.jmxexporter.playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_JMXEXPORTER_HOSTS=${DEPLOY_JMXEXPORTER_HOSTS:-kafka} \
    -u ${ANSIBLE_USER:-ghci} \
    -b
