---
- name: create alertmanager system group
  group:
    name: alertmanager
    system: true
    state: present
    gid: 3235

- name: create data directory
  file:
    path: /data
    state: directory
    owner: root
    group: root
    mode: 0755

- name: create alertmanager system user
  user:
    name: alertmanager
    system: true
    shell: "/usr/sbin/nologin"
    group: alertmanager
    createhome: false
    home: "{{ ALERTMANAGER_DB_DIR }}"
    uid: 3235

- name: create alertmanager data directory
  file:
    path: "{{ ALERTMANAGER_DB_DIR }}"
    state: directory
    recurse: true
    owner: alertmanager
    group: alertmanager
    mode: 0755

- name: create alertmanager configuration directories
  file:
    path: "{{ item }}"
    state: directory
    owner: root
    group: alertmanager
    mode: 0770
  with_items:
  - "{{ ALERTMANAGER_CONFIG_DIR }}"

- name: alertmanager url
  debug:
    msg: "{{ALERTMANAGER_DOWNLOAD_URL}}"

- name: download alertmanager binary to local folder
  become: false
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "{{ALERTMANAGER_DOWNLOAD_URL}}"
    dest: "/tmp/alertmanager-{{ALERTMANAGER_VERSION}}.tar.gz"
    checksum: "{{ALERTMANAGER_CHECKSUM}}"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: install unzip
  package:
    name: unzip
    state: present

- name: unpack alertmanager binaries
  unarchive:
    src: "/tmp/alertmanager-{{ALERTMANAGER_VERSION}}.tar.gz"
    dest: "/tmp"
    remote_src: true
  check_mode: false

- name: copy official alertmanager binaries
  copy:
    src: "/tmp/alertmanager-{{ALERTMANAGER_VERSION}}.linux-amd64/{{item}}"
    dest: "/usr/local/bin/{{item}}"
    mode: '755'
    owner: root
    group: root
    remote_src: true
  with_items:
  - alertmanager
  - amtool
  notify:
  - restart alertmanager

- block:
    - name: create systemd service unit
      template:
        src: alertmanager.service.j2
        dest: /etc/systemd/system/alertmanager.service
        owner: root
        group: root
        mode: 0644
      notify:
      - restart alertmanager
      when: inventory_hostname == groups.alerthk[0]
    
    - name: create systemd service unit1
      template:
        src: alertmanager.service1.j2
        dest: /etc/systemd/system/alertmanager.service
        owner: root
        group: root
        mode: 0644
      notify:
      - restart alertmanager
      when: inventory_hostname == groups.alerthk[1]
  when: inventory_hostname.startswith('HK')

- block:
    - name: create systemd service unit
      template:
        src: alertmanager.service.j2
        dest: /etc/systemd/system/alertmanager.service
        owner: root
        group: root
        mode: 0644
      notify:
      - restart alertmanager
      when: inventory_hostname == groups.alertsz[0]
    
    - name: create systemd service unit1
      template:
        src: alertmanager.service1.j2
        dest: /etc/systemd/system/alertmanager.service
        owner: root
        group: root
        mode: 0644
      notify:
      - restart alertmanager
      when: inventory_hostname == groups.alertsz[1]
  when: inventory_hostname.startswith('SZ')

- name: ensure start alertmanager on boot
  systemd:
    name: alertmanager
    daemon_reload: true
    enabled: true
