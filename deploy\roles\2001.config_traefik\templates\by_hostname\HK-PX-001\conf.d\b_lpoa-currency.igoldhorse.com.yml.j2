{{ansible_managed|comment}}
http:
  middlewares:
    hkPx001FundIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    lpoa-currency.igoldhorse.com:
      service: hkPx001CaddyLpoaCurrency
      rule: Host(`lpoa-currency.igoldhorse.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
  services:
    hkPx001CaddyLpoaCurrency:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
