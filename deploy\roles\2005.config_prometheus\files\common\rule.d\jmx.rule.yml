groups:
  - name: jmx-alerting
    rules:
    # down了超过1分钟
    - alert: instance-down
      expr: up == 0
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "Instance {{ $labels.instance }} down"
        description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minutes."
    # 堆空间使用超过70%
    - alert: heap-usage-too-much
      expr: jvm_memory_bytes_used{area="heap"} / jvm_memory_bytes_max * 100 > 70
      for: 1m
      labels:
        severity: warning
      annotations:
        summary: "JVM Instance {{ $labels.instance }} memory usage > 70%"
        description: "{{ $labels.instance }} of job {{ $labels.job }} has been in status [heap usage > 70%] for more than 1 minutes. current usage ({{ $value }}%)"
    
    # 堆空间使用超过80%
    - alert: heap-usage-too-much
      expr: jvm_memory_bytes_used{area="heap"} / jvm_memory_bytes_max * 100 > 80
      for: 1m
      labels:
        severity: warning
      annotations:
        summary: "JVM Instance {{ $labels.instance }} memory usage > 80%"
        description: "{{ $labels.instance }} of job {{ $labels.job }} has been in status [heap usage > 80%] for more than 1 minutes. current usage ({{ $value }}%)"
    
    # 堆空间使用超过90%
    - alert: heap-usage-too-much
      expr: jvm_memory_bytes_used{area="heap"} / jvm_memory_bytes_max * 100 > 90
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "JVM Instance {{ $labels.instance }} memory usage > 90%"
        description: "{{ $labels.instance }} of job {{ $labels.job }} has been in status [heap usage > 90%] for more than 1 minutes. current usage ({{ $value }}%)"

#    # 在5分钟里，Old GC花费时间超过50%        
#    - alert: old-gc-time-too-much
#      expr: increase(jvm_gc_collection_seconds_sum{gc="G1 Old Generation"}[5m]) > 5 * 60 * 0.5
#      for: 5m
#      labels:
#        severity: warning
#      annotations:
#        summary: "JVM Instance {{ $labels.instance }} Old GC time > 50% running time"
#        description: "{{ $labels.instance }} of job {{ $labels.job }} has been in status [Old GC time > 50% running time] for more than 5 minutes. current seconds ({{ $value }}%)"

    # 在5分钟里，Old GC花费时间超过80%
    - alert: old-gc-time-too-much
      expr: increase(jvm_gc_collection_seconds_sum{gc="G1 Old Generation"}[5m]) > 5 * 60 * 0.8
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "JVM Instance {{ $labels.instance }} Old GC time > 80% running time"
        description: "{{ $labels.instance }} of job {{ $labels.job }} has been in status [Old GC time > 80% running time] for more than 5 minutes. current seconds ({{ $value }}%)"