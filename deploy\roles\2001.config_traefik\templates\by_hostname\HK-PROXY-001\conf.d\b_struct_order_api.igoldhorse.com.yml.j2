{{ansible_managed|comment}}
http:
  middlewares:
    hkPx001SoaIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
        - "*************"
        - "*************"
  routers:
    struct-order-api.igoldhorse.com:
      service: hkPx001SoaBackend
      rule: Host(`struct-order-api.igoldhorse.com`)
      middlewares:
      - hkPx001SoaIpWhiteList
      tls:
        certResolver: httpResolver
        domains:
        - main: struct-order-api.igoldhorse.com
  services:
    hkPx001SoaBackend:
      loadBalancer:
        servers:
        - url: http://HK-OPENAPI-001:8113
