- name: create traefik group
  group:
    name: traefik
    system: true
    state: present
    gid: 3434

- name: create traefik user
  user:
    name: traefik
    system: true
    shell: /usr/sbin/nologin
    group: traefik
    createhome: false
    uid: 3434

- name: create tmp directory
  file:
    path: /tmp/traefik
    state: directory

- name: download url
  debug:
    msg: 'download {{TRAEFIK_DOWNLOAD_URL}} to {{TRAEFIK_FILE_NAME}}'

- name: download traefik
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "{{TRAEFIK_DOWNLOAD_URL}}"
    dest: /tmp/traefik/{{TRAEFIK_FILE_NAME}}
    checksum: "{{TRAEFIK_CHECKSUM}}"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: extract traefik
  unarchive:
    src: "/tmp/traefik/{{TRAEFIK_FILE_NAME}}"
    dest: /usr/local/bin
    remote_src: true
    mode: '755'
    extra_opts:
    - traefik
  notify:
  - restart traefik

- name: create traefik etc
  file:
    path: /etc/traefik
    state: directory
    owner: traefik

- name: create traefik etc conf.d
  file:
    path: /etc/traefik/conf.d
    recurse: true
    state: directory
    owner: traefik

- name: ensure acme.json permission
  file:
    path: /etc/traefik/acme.json
    state: file
    owner: traefik
  ignore_errors: true

- name: create data log directory
  file:
    path: /data/log
    state: directory
    recurse: true

- name: create traefik log directory
  file:
    path: /data/log/traefik
    state: directory
    owner: traefik
    group: adm

- name: create traefik symlink in vars
  file:
    path: /var/log/traefik
    src: /data/log/traefik
    force: true
    state: link

- name: enable daily traefik logrotate config
  template:
    src: logrotate.d_traefik.j2
    dest: /etc/logrotate.d/traefik

- name: copy traefik systemd service
  template:
    src: traefik.service.j2
    dest: /etc/systemd/system/traefik.service
  notify:
  - restart traefik

- name: ensure traefik on boot
  systemd:
    name: traefik
    daemon_reload: true
    enabled: true
