- labels:
    instance: biz-platform-admin-workflow
    job: jvm
    type: jvm
  targets:
  - SZ-OPACC-001:8102
- labels:
    instance: biz-platform-admin-open-account
    job: jvm
    type: jvm
  targets:
  - SZ-OPACC-001:8103
- labels:
    instance: biz-platform-proxy-service
    job: jvm
    type: jvm
  targets:
  - SZ-OPACC-001:8104
- labels:
    instance: biz-platform-app
    job: jvm
    type: jvm
  targets:
  - SZ-OPACC-001:8105
- labels:
    instance: biz-platform-job
    job: jvm
    type: jvm
  targets:
  - SZ-OPACC-001:8106
- labels:
    instance: biz-platform-admin-securities-service
    job: jvm
    type: jvm
  targets:
  - SZ-OPACC-001:8107
- labels:
    instance: news-convert-core
    job: jvm
    type: jvm
  targets:
  - SZ-NEWS-001:8201
- labels:
    instance: news-service-core
    job: jvm
    type: jvm
  targets:
  - SZ-NEWS-001:8202
- labels:
    instance: mktquot-convert-hkex
    job: jvm
    type: jvm
  targets:
  - SZ-CVT-001:8001
- labels:
    instance: mktquot-convert-us
    job: jvm
    type: jvm
  targets:
  - SZ-CVT-001:8006
- labels:
    instance: mktquot-convert-us-bf
    job: jvm
    type: jvm
  targets:
  - SZ-CVT-001:8007
- labels:
    instance: mktquot-convert-a
    job: jvm
    type: jvm
  targets:
  - SZ-CSC-001:8004
- labels:
    instance: mktquot-receive-a
    job: jvm
    type: jvm
  targets:
  - SZ-CSC-002:8015
- labels:
    instance: mktquot-receive-hkex
    job: jvm
    type: jvm
  targets:
  - SZ-REC-001:8012
- labels:
    instance: mktquot-receive-us
    job: jvm
    type: jvm
  targets:
  - SZ-REC-001:8017
- labels:
    instance: mktquot-receive-us-bf
    job: jvm
    type: jvm
  targets:
  - SZ-ES-001:8018
- labels:
    instance: mktquot-service-hk
    job: jvm
    type: jvm
  targets:
  - SZ-CAL-001:8021
- labels:
    instance: mktquot-service-us
    job: jvm
    type: jvm
  targets:
  - SZ-CAL-001:8022
- labels:
    instance: mktquot-service-a
    job: jvm
    type: jvm
  targets:
  - SZ-CSC-001:8020
- labels:
    instance: mktquot-push-hkex
    job: jvm
    type: jvm
  targets:
  - SZ-PUSH-001:8010
- labels:
    instance: mktquot-push-us
    job: jvm
    type: jvm
  targets:
  - SZ-PUSH-001:8011
- labels:
    instance: mktquot-push-a
    job: jvm
    type: jvm
  targets:
  - SZ-CSC-003:8009
#- labels:
#    instance: f10-convert-core
#    job: jvm
#    type: jvm
#  targets:
#  - SZ-ES-001:8030
- labels:
    instance: f10-core
    job: jvm
    type: jvm
  targets:
  - SZ-ES-001:8041
#  - SZ-NEWS-001:8041
- labels:
    instance: minute-a-core
    job: jvm
    type: jvm
  targets:
  - SZ-CSC-003:8052
- labels:
    instance: minute-hkex-core
    job: jvm
    type: jvm
  targets:
  - SZ-STORE-001:8047
- labels:
    instance: minute-us-core
    job: jvm
    type: jvm
  targets:
  - SZ-STORE-002:8048
- labels:
    instance: mktquot-calculate-core
    job: jvm
    type: jvm
  targets:
  - SZ-CAL-001:8024
- labels:
    instance: platform-core
    job: jvm
    type: jvm
  targets:
  - SZ-CAL-001:8029
- labels:
    instance: platform-tencent
    job: jvm
    type: jvm
  targets:
  - SZ-CAL-001:8051
- labels:
    instance: mktquot-stockcode-core
    job: jvm
    type: jvm
  targets:
  - SZ-TASK-001:8028
  - SZ-DERIV-001:8028
- labels:
    instance: mktquot-task
    job: jvm
    type: jvm
  targets:
  - SZ-ES-001:8023
- labels:
    instance: mktmgr-core
    job: jvm
    type: jvm
  targets:
  - SZ-CAL-001:8031
- labels:
    instance: mktquot-gateway
    job: jvm
    type: jvm
  targets:
  - SZ-PUSH-001:8000
- labels:
    instance: mktquot-hbase-core
    job: jvm
    type: jvm
  targets:
  - SZ-TASK-001:8026
- labels:
    instance: mktquot-optional-core
    job: jvm
    type: jvm
  targets:
  - SZ-TASK-001:8027
- labels:
    instance: monitoring-core
    job: jvm
    type: jvm
  targets:
  - SZ-PUSH-001:8049
- labels:
    instance: mktquot-service-delay
    job: jvm
    type: jvm
  targets:
  - SZ-CSC-003:8043
- labels:
    instance: minute-us-delay
    job: jvm
    type: jvm
  targets:
  - SZ-CSC-003:8044
- labels:
    instance: receive-hkex-delay
    job: jvm
    type: jvm
  targets:
  - SZ-CSC-001:8014
- labels:
    instance: convert-hkex-delay
    job: jvm
    type: jvm
  targets:
  - SZ-CSC-002:8038
- labels:
    instance: delay-calculate-core
    job: jvm
    type: jvm
  targets:
  - SZ-CSC-002:8025
- labels:
    instance: mktquot-a-calculate-core
    job: jvm
    type: jvm
  targets:
  - SZ-TASK-001:8040
- labels:
    instance: minute-a-delay-core
    job: jvm
    type: jvm
  targets:
  - SZ-MKTAPI-001:8056
- labels:
    instance: mktquot-a-delay-calculate
    job: jvm
    type: jvm
  targets:
  - SZ-CSC-003:8057
- labels:
    instance: mktquot-convert-a-delay
    job: jvm
    type: jvm
  targets:
  - SZ-TASK-001:8055
- labels:
    instance: mktquot-receive-a-delay
    job: jvm
    type: jvm
  targets:
  - SZ-MKTAPI-001:8016
- labels:
    instance: mktquot-convert-us-delay
    job: jvm
    type: jvm
  targets:
  - SZ-CSC-003:8054
#- labels:
#    instance: mktquot-struct-core
#    job: jvm
#    type: jvm
#  targets:
#  - SZ-MKTAPI-001:8060
#- labels:
#    instance: mktquot-struct-task
#    job: jvm
#    type: jvm
#  targets:
#  - SZ-MKTAPI-001:8059
- labels:
    instance: mktquot-task-data
    job: jvm
    type: jvm
  targets:
  - SZ-DERIV-001:8032
- labels:
    instance: user-center-gateway
    job: jvm
    type: jvm
  targets:
  - SZ-NEWS-001:8220
- labels:
    instance: mktquot-push-delay
    job: jvm
    type: jvm
  targets:
  - SZ-PUSH-001:8058
#- labels:
#    instance: mktquot-init
#    job: jvm
#    type: jvm
#  targets:
#  - SZ-CVT-001:8035
#- labels:
#    instance: biz-platform-struct-core
#    job: jvm
#    type: jvm
#  targets:
#  - SZ-NEWS-001:8108