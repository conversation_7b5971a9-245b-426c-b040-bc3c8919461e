---
- name: Check clickhouse config, data and logs
  file:
    dest: "{{ item }}"
    owner: "{{ clickhouse_user | default('clickhouse') }}"
    group: "{{ clickhouse_group | default('clickhouse') }}"
    mode: "0755"
    state: directory
  loop:
    - "{{ clickhouse_path_logdir }}"
    - "{{ clickhouse_path_configdir }}"
    - "{{ clickhouse_path_tmp }}"
    - "{{ clickhouse_path_data }}"
    - "{{ clickhouse_path_user_files }}"
  notify: restart-ch
  become: true

- name: Config | Create config.d folder
  file:
    path: "{{ clickhouse_path_configdir }}/config.d"
    state: directory
    owner: "{{ clickhouse_user | default('clickhouse') }}"
    group: "{{ clickhouse_group | default('clickhouse') }}"
    mode: "0755"
  become: true

- name: Config | Create users.d folder
  file:
    path: "{{ clickhouse_path_configdir }}/users.d"
    state: directory
    owner: "{{ clickhouse_user | default('clickhouse') }}"
    group: "{{ clickhouse_group | default('clickhouse') }}"
    mode: "0755"
  become: true

- name: Config | Generate system config
  template:
    src: metrika.j2
    dest: "{{ clickhouse_path_configdir }}/metrika.xml"
    owner: "{{ clickhouse_user | default('clickhouse') }}"
    group: "{{ clickhouse_group | default('clickhouse') }}"
    mode: "0755"
  notify: restart-ch
  become: true

- name: Config | Generate system config
  template:
    src: config.j2
    dest: "{{ clickhouse_path_configdir }}/config.d/config.xml"
    owner: "{{ clickhouse_user | default('clickhouse') }}"
    group: "{{ clickhouse_group | default('clickhouse') }}"
    mode: "0755"
  notify: restart-ch
  become: true


- name: Config | Generate users config
  template:
    src: users.j2
    dest: "{{ clickhouse_path_configdir }}/users.d/users.xml"
    owner: "{{ clickhouse_user | default('clickhouse') }}"
    group: "{{ clickhouse_group | default('clickhouse') }}"
    mode: "0755"
  become: true



- name: Config | Generate remote_servers config
  template:
    src: remote_servers.j2
    dest: "{{ clickhouse_path_configdir }}/config.d/clickhouse_remote_servers.xml"
    owner: "{{ clickhouse_user | default('clickhouse') }}"
    group: "{{ clickhouse_group | default('clickhouse') }}"
    mode: "0755"
  notify: restart-ch
  become: true
  when: clickhouse_shards is defined

- name: Config | Generate macros config
  template:
    src: macros.j2
    dest: "{{ clickhouse_path_configdir }}/config.d/macros.xml"
    owner: "{{ clickhouse_user | default('clickhouse') }}"
    group: "{{ clickhouse_group | default('clickhouse') }}"
    mode: "0755"
  notify: restart-ch
  become: true
  when: clickhouse_macros is defined 

- name: Config | Generate zookeeper servers config
  template:
    src: zookeeper-servers.j2
    dest: "{{ clickhouse_path_configdir }}/config.d/zookeeper-servers.xml"
    owner: "{{ clickhouse_user | default('clickhouse') }}"
    group: "{{ clickhouse_group | default('clickhouse') }}"
    mode: "u=rw,og=r"
  notify: restart-ch
  become: true
  when: clickhouse_zookeeper_nodes is defined 
