{{ansible_managed|comment}}
http:
  middlewares:
    hkPx001CurrencyIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    currency.igoldhorse.com:
      service: hkPx001CaddyCurrency
      rule: Host(`currency.igoldhorse.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
  services:
    hkPx001CaddyCurrency:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
