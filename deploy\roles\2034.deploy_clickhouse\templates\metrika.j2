<yandex>
    <!-- 集群配置 -->
    <clickhouse_remote_servers>
        <!-- 3分片0备份 -->
        <cluster_3shards_0replicas>
            <!-- 数据分片1  -->
            <shard>
                <weight>1</weight>
                <!-- Optional. Whether to write data to just one of the replicas. Default: false (write data to all replicas).
                1. internal_replication为false时候，（前提是往分布表写入数据）会自动往同一shard下所有备份表写入相同数据，不需要任何其他外力，单独设置这个参数即可；
				但是会出现各备份之间数据不同步的情况，因为此种情况下往分布式表里面写数据，后台算法会先按照weight将数据分成shard数量堆，然后将对应堆的数据分别写入该shard下面的所有备份表中，
				有可能存在同样的数据写入A备份成功但是写入B备份失败的情况，这里是没有校验的；
                2. internal_replication为true时，一定要配合zookeeper和ReplicatedMergeTree引擎表使用，如果不配合这些，经本人测试查询数据时会出现严重错误，请切记
                -->
                <internal_replication>true</internal_replication>
                <replica>
                    <host>SZ-CLHOUSE-001</host>
                    <port>9000</port>
                    <user>default</user>
                    <password>123</password>
                </replica>
            </shard>
            <!-- 数据分片2  -->
            <shard>
                <weight>1</weight>
                <internal_replication>true</internal_replication>
                <replica>
                    <host>SZ-CLHOUSE-002</host>
                    <port>9000</port>
                    <user>default</user>
                    <password>123</password>
                </replica>
            </shard>
            <!-- 数据分片3  -->
            <shard>
                <weight>1</weight>
                <internal_replication>true</internal_replication>
                <replica>
                    <host>SZ-CLHOUSE-003</host>
                    <port>9000</port>
                    <user>default</user>
                    <password>123</password>
                </replica>
            </shard>
        </cluster_3shards_0replicas>
    </clickhouse_remote_servers>

    <zookeeper-servers>
        <node index="l">
            <host>*************</host>
            <port>2181</port>
        </node>
        <node index="2">
            <host>*************</host>
            <port>2181</port>
        </node>
        <node index="3">
            <host>*************</host>
            <port>2181</port>
        </node>
    </zookeeper-servers>

    <clickhouse_compression>
        <case>
            <min_part_size>10000000000</min_part_size>
            <min_part_size_ratio>0.01</min_part_size_ratio>
            <method>lz4</method>
        </case>
    </clickhouse_compression>
  <!--  <macros>
        <shard>01</shard>
        <replica>SZ-CLHOUSE-001_01</replica>
    </macros>-->
</yandex>
