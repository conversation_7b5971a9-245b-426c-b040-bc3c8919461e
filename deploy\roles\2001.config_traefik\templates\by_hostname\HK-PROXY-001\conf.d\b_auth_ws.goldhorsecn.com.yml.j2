{{ansible_managed|comment}}
http:
  middlewares:
    openapiAuth:
      forwardAuth:
        address: http://HK-PROXY-001:8210/open-api-auth/auth_api/traffic_authentication
        trustForwardHeader: true
#    hkProxy001IpWhiteList:
#      ipWhiteList:
#        sourceRange:
#        # TODO: add office ip
#        - "***************"
#        - "*************"
#        - "**************"
#        - "***************"
#        - "*************"
#        - "**************"
  routers:
    openapi-auth.goldhorsecn.com:
      service: hkProxy001AuthWs
      rule: Host(`openapi-auth.goldhorsecn.com`) && Path(`/ws`)
      middlewares:
      - gzip
      - openapiAuth
      tls:
        certResolver: httpResolver
        domains:
        domains:
        - main: openapi-auth.goldhorsecn.com
  services:
    hkProxy001AuthWs:
      loadBalancer:
        servers:
        - url: http://SZ-PUSH-001:15675

