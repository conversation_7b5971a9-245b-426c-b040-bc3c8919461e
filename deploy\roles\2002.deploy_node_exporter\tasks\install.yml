
- name: Create the node_exporter group
  group:
    name: "{{ _node_exporter_system_group }}"
    state: present
    system: true
  when: _node_exporter_system_group != "root"

- name: Create the node_exporter user
  user:
    name: "{{ _node_exporter_system_user }}"
    groups: "{{ _node_exporter_system_group }}"
    append: true
    shell: /usr/sbin/nologin
    system: true
    create_home: false
  when: _node_exporter_system_user != "root"


- name: Download node_exporter binary to local folder
  become: false
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "https://filescfcdn.fwdev.top/common/node_exporter-{{ node_exporter_version }}.linux-amd64.tar.gz"
    dest: "/tmp/node_exporter-{{ node_exporter_version }}.linux-amd64.tar.gz"
    checksum: '{{NODE_EXPORTER_CHECKSUM}}'
    mode: '0644'
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: Unpack node_exporter binary
  unarchive:
    src: "/tmp/node_exporter-{{ node_exporter_version }}.linux-amd64.tar.gz"
    dest: "/tmp"
    remote_src: true
    creates: "/tmp/node_exporter-{{ node_exporter_version }}.linux-amd64/node_exporter"

- name: Propagate node_exporter binaries
  copy:
    src: "/tmp/node_exporter-{{ node_exporter_version }}.linux-amd64/node_exporter"
    dest: "{{ _node_exporter_binary_install_dir }}/node_exporter"
    mode: 0755
    owner: root
    group: root
    remote_src: true
  notify: restart node_exporter
