---
## General
elasticsearch_exporter_version: 1.6.0
elasticsearch_exporter_release_system: linux-amd64

# Package paths
elasticsearch_exporter_release_name: "elasticsearch_exporter-{{ elasticsearch_exporter_version }}.{{ elasticsearch_exporter_release_system }}"
elasticsearch_exporter_package: "{{ elasticsearch_exporter_release_name}}.tar.gz"
elasticsearch_exporter_url: "https://github.com/prometheus-community/elasticsearch_exporter/releases/download/v{{ elasticsearch_exporter_version }}/{{ elasticsearch_exporter_package }}"
elasticsearch_exporter_download_path: /usr/src
elasticsearch_exporter_package_path: "{{ elasticsearch_exporter_download_path }}/{{ elasticsearch_exporter_package }}"
elasticsearch_exporter_src_bin: "{{ elasticsearch_exporter_download_path }}/{{ elasticsearch_exporter_release_name }}/elasticsearch_exporter"

# Set true to force the download and installation of the binary
elasticsearch_exporter_force_reinstall: false

## Service options
elasticsearch_exporter_private_tmp: true

# Owner
elasticsearch_exporter_user: prometheus
elasticsearch_exporter_group: prometheus

# start on boot
elasticsearch_exporter_service_enabled: True
# current state: started, stopped
elasticsearch_exporter_service_state: started

# Files & Paths
elasticsearch_exporter_log_output: journal
elasticsearch_exporter_root_path: /etc/elasticsearch_exporter
elasticsearch_exporter_bin_path: "{{ elasticsearch_exporter_root_path }}/bin"

# Flags (https://github.com/prometheus-community/elasticsearch_exporter#flags)
elasticsearch_exporter_port: 9114
elasticsearch_exporter_ip: 0.0.0.0
elasticsearch_exporter_log_level: info
elasticsearch_exporter_log_format: logfmt
elasticsearch_exporter_web_telemetry_path: metrics

# Elasticsearch connection settings
elasticsearch_exporter_es_uri: "http://*************:9200"
elasticsearch_exporter_es_cluster_uris:
  - "http://*************:9200"
  - "http://*************:9200"
  - "http://*************:9200"
elasticsearch_exporter_es_all: true
elasticsearch_exporter_es_indices: true
elasticsearch_exporter_es_indices_settings: true
elasticsearch_exporter_es_shards: true
elasticsearch_exporter_es_snapshots: true
elasticsearch_exporter_es_timeout: 5s
elasticsearch_exporter_es_ca: ""
elasticsearch_exporter_es_client_private_key: ""
elasticsearch_exporter_es_client_cert: ""
elasticsearch_exporter_es_insecure_skip_verify: false

# Additional options
elasticsearch_exporter_options:
  - "web.listen-address={{ elasticsearch_exporter_ip }}:{{ elasticsearch_exporter_port }}"
  - "web.telemetry-path=/{{ elasticsearch_exporter_web_telemetry_path }}"
  - "es.uri={{ elasticsearch_exporter_es_uri }}"
  - "es.all={{ elasticsearch_exporter_es_all | lower }}"
  - "es.indices={{ elasticsearch_exporter_es_indices | lower }}"
  - "es.indices_settings={{ elasticsearch_exporter_es_indices_settings | lower }}"
  - "es.shards={{ elasticsearch_exporter_es_shards | lower }}"
  - "es.snapshots={{ elasticsearch_exporter_es_snapshots | lower }}"
  - "es.timeout={{ elasticsearch_exporter_es_timeout }}"
  - "log.level={{ elasticsearch_exporter_log_level }}"
  - "log.format={{ elasticsearch_exporter_log_format }}"
