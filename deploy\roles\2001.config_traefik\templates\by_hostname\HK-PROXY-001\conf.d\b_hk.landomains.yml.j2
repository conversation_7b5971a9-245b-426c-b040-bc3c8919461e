{{ansible_managed|comment}}
http:
  routers:
    mktquot-service-open.lan.ghsz:
      service: ghmktquotserviceopen
      rule: Host(`mktquot-service-open.lan.ghsz`)
    mktquot-stock.lan.ghsz:
      service: ghmktquotstock
      rule: Host(`mktquot-stock.lan.ghsz`)
    f-open-api.lan.ghsz:
      service: ghfopenapi
      rule: Host(`f-open-api.lan.ghsz`)
    open-api-gateway.lan.ghhk:
      service: ghopenapigateway
      rule: Host(`open-api-gateway.lan.ghhk`)
    open-api-auth.lan.ghhk:
      service: ghopenapiauth
      rule: Host(`open-api-auth.lan.ghhk`)
  services:
    ghmktquotserviceopen:
      loadBalancer:
        healthcheck:
          path: /mktquot-service-open/actuator/info
        servers:
        - url: http://SZ-CSC-002:8064
        - url: http://SZ-CSC-003:8064
    ghmktquotstock:
      loadBalancer:
        healthcheck:
          path: /actuator/info
        servers:
        - url: http://SZ-TASK-001:8028
        - url: http://SZ-DERIV-001:8028
    ghfopenapi:
      loadBalancer:
        healthcheck:
          path: /actuator/info
        servers:
        - url: http://SZ-ES-001:8041
        #未部署- url: http://SZ-NEWS-001:8041
    ghopenapigateway:
      loadBalancer:
        healthcheck:
          path: /actuator/info
        servers:
        - url: http://HK-PROXY-001:8210
        - url: http://HK-MG-002:8210
    ghopenapiauth:
      loadBalancer:
        healthcheck:
          path: /open-api-auth/actuator/info
        servers:
        - url: http://HK-PROXY-001:8211
        - url: http://HK-MG-002:8211