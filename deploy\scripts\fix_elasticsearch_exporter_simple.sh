#!/bin/bash

# 简化修复Elasticsearch Exporter - 使用单节点配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 创建简化的服务文件
create_simple_service() {
    log_header "创建简化的服务配置"
    
    local service_content='[Unit]
Description=Elasticsearch Exporter
Documentation=https://github.com/prometheus-community/elasticsearch_exporter
Wants=network-online.target
After=network-online.target

[Service]
Type=simple
User=prometheus
Group=prometheus
ExecStart=/etc/elasticsearch_exporter/bin/elasticsearch_exporter --web.listen-address=0.0.0.0:9114 --web.telemetry-path=/metrics --es.uri=http://*************:9200 --es.all=true --es.indices=true --es.indices_settings=true --es.shards=true --es.snapshots=true --es.timeout=5s --log.level=info --log.format=logfmt
SyslogIdentifier=elasticsearch_exporter
Restart=always
RestartSec=1
StartLimitInterval=0

PrivateTmp=yes
ProtectHome=yes
NoNewPrivileges=yes
ProtectSystem=strict
ProtectControlGroups=true
ProtectKernelModules=true
ProtectKernelTunables=yes

[Install]
WantedBy=multi-user.target'

    # 创建临时文件
    echo "$service_content" > /tmp/elasticsearch_exporter_simple.service
    
    log_info "简化服务配置已创建"
}

# 部署服务配置
deploy_simple_service() {
    log_header "部署简化服务配置"
    
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        log_info "部署到 $host..."
        
        # 停止现有服务
        ansible -i hosts.ini "$host" -m systemd -a "name=elasticsearch_exporter state=stopped" --become || true
        
        # 复制新的服务文件
        ansible -i hosts.ini "$host" -m copy -a "src=/tmp/elasticsearch_exporter_simple.service dest=/etc/systemd/system/elasticsearch_exporter.service owner=root group=root mode=0644" --become
        
        # 重载systemd
        ansible -i hosts.ini "$host" -m systemd -a "daemon_reload=yes" --become
        
        log_info "  ✓ $host 配置已部署"
    done
}

# 启动服务
start_services() {
    log_header "启动Elasticsearch Exporter服务"
    
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        log_info "启动 $host 上的服务..."
        
        # 启动并启用服务
        ansible -i hosts.ini "$host" -m systemd -a "name=elasticsearch_exporter state=started enabled=yes" --become
        
        # 等待服务启动
        sleep 3
        
        # 检查服务状态
        local status=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-active elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
        
        if [ "$status" = "active" ]; then
            log_info "  ✓ $host 服务启动成功"
        else
            log_error "  ✗ $host 服务启动失败"
            
            # 显示错误信息
            log_info "  错误详情:"
            ansible -i hosts.ini "$host" -m shell -a "systemctl status elasticsearch_exporter --no-pager -l" --become 2>/dev/null | grep -v "SUCCESS" | head -10
            
            log_info "  最近日志:"
            ansible -i hosts.ini "$host" -m shell -a "journalctl -u elasticsearch_exporter --no-pager -n 5" --become 2>/dev/null | grep -v "SUCCESS"
        fi
        
        echo ""
    done
}

# 验证服务
verify_services() {
    log_header "验证服务状态"
    
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    local all_ok=true
    
    for host in "${hosts[@]}"; do
        log_info "验证 $host..."
        
        # 检查服务状态
        local status=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-active elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
        
        if [ "$status" = "active" ]; then
            log_info "  ✓ 服务运行正常"
            
            # 检查端口监听
            sleep 5
            local port_check=$(ansible -i hosts.ini "$host" -m shell -a "netstat -tlnp | grep :9114" --become 2>/dev/null | grep -v "SUCCESS" | wc -l || echo "0")
            
            if [ "$port_check" -gt 0 ]; then
                log_info "  ✓ 端口9114监听正常"
            else
                log_warn "  ⚠ 端口9114未监听"
                all_ok=false
            fi
            
            # 检查指标端点
            sleep 5
            local metrics_check=$(ansible -i hosts.ini "$host" -m uri -a "url=http://localhost:9114/metrics timeout=10" 2>/dev/null | grep -c "status.*200" || echo "0")
            
            if [ "$metrics_check" -gt 0 ]; then
                log_info "  ✓ 指标端点正常"
                
                # 检查ES连接
                local es_up=$(ansible -i hosts.ini "$host" -m shell -a "curl -s http://localhost:9114/metrics | grep 'elasticsearch_up ' | head -1" 2>/dev/null | grep -v "SUCCESS" || echo "")
                if [ -n "$es_up" ]; then
                    echo "    ES连接状态: $es_up"
                    
                    if echo "$es_up" | grep -q "elasticsearch_up 1"; then
                        log_info "    ✓ ES连接正常"
                    else
                        log_warn "    ⚠ ES连接异常"
                        all_ok=false
                    fi
                fi
            else
                log_warn "  ⚠ 指标端点异常"
                all_ok=false
            fi
        else
            log_error "  ✗ 服务未运行"
            all_ok=false
        fi
        
        echo ""
    done
    
    if [ "$all_ok" = true ]; then
        log_info "✓ 所有服务验证通过"
    else
        log_warn "⚠ 部分服务存在问题，请检查日志"
    fi
}

# 测试ES连接
test_es_connection() {
    log_header "测试Elasticsearch连接"
    
    local es_node="*************:9200"
    
    log_info "测试连接到 $es_node..."
    
    if curl -s --connect-timeout 5 "http://$es_node" > /dev/null; then
        log_info "  ✓ ES节点可达"
        
        # 获取集群信息
        local cluster_info=$(curl -s "http://$es_node/_cluster/health" 2>/dev/null)
        local status=$(echo "$cluster_info" | jq -r '.status' 2>/dev/null || echo "unknown")
        local nodes=$(echo "$cluster_info" | jq -r '.number_of_nodes' 2>/dev/null || echo "unknown")
        
        echo "    集群状态: $status"
        echo "    节点数量: $nodes"
        
        if [ "$status" = "green" ]; then
            log_info "  ✓ 集群状态良好"
        elif [ "$status" = "yellow" ]; then
            log_warn "  ⚠ 集群状态警告"
        elif [ "$status" = "red" ]; then
            log_error "  ✗ 集群状态严重"
        fi
    else
        log_error "  ✗ ES节点不可达"
        echo "    请检查Elasticsearch服务是否正常运行"
        echo "    检查命令: curl http://$es_node"
    fi
}

# 显示使用信息
show_usage_info() {
    log_header "使用信息"
    
    echo ""
    echo "Elasticsearch Exporter已配置完成！"
    echo ""
    echo "配置信息:"
    echo "  - 监控端口: 9114"
    echo "  - 指标路径: /metrics"
    echo "  - ES节点: http://*************:9200 (会自动发现其他节点)"
    echo ""
    echo "验证命令:"
    echo "  # 检查服务状态"
    echo "  ansible -i hosts.ini 'SZ-ES-001' -m shell -a 'systemctl status elasticsearch_exporter' --become"
    echo ""
    echo "  # 检查指标"
    echo "  curl http://SZ-ES-001:9114/metrics"
    echo ""
    echo "  # 检查ES连接状态"
    echo "  curl http://SZ-ES-001:9114/metrics | grep elasticsearch_up"
    echo ""
    echo "  # 检查集群健康状态"
    echo "  curl http://SZ-ES-001:9114/metrics | grep elasticsearch_cluster_health_status"
    echo ""
    echo "Prometheus配置:"
    echo "  - 在Prometheus中添加以下targets:"
    echo "    - SZ-ES-001:9114"
    echo "    - SZ-MONT-002:9114"
    echo "    - HK-MG-002:9114"
    echo ""
}

# 清理临时文件
cleanup() {
    rm -f /tmp/elasticsearch_exporter_simple.service
}

# 主函数
main() {
    echo "========================================"
    echo "Elasticsearch Exporter简化修复"
    echo "修复时间: $(date)"
    echo "========================================"
    echo ""
    
    # 检查前置条件
    if [ ! -f "hosts.ini" ]; then
        log_error "hosts.ini 文件不存在"
        exit 1
    fi
    
    if ! command -v ansible &> /dev/null; then
        log_error "ansible 未安装"
        exit 1
    fi
    
    # 执行修复步骤
    test_es_connection
    create_simple_service
    deploy_simple_service
    start_services
    verify_services
    show_usage_info
    cleanup
    
    log_info "修复完成！"
    echo ""
    echo "如果仍有问题，请检查:"
    echo "1. elasticsearch_exporter二进制文件是否存在: /etc/elasticsearch_exporter/bin/elasticsearch_exporter"
    echo "2. prometheus用户是否有执行权限"
    echo "3. Elasticsearch服务是否正常运行"
    echo "4. 网络连接是否正常"
}

# 执行主函数
main "$@"
