---
node_exporter_version: 1.3.0
NODE_EXPORTER_CHECKSUM: sha256:b16d5061d3bc3d0ad8232964071ee3ece7fc104afa817bb5f55570521756125d
node_exporter_binary_local_dir: ""
node_exporter_web_listen_address: "0.0.0.0:9100"

node_exporter_textfile_dir: "/var/lib/node_exporter"

node_exporter_tls_server_config: {}

node_exporter_http_server_config: {}

node_exporter_basic_auth_users: {}

node_exporter_enabled_collectors:
  - systemd
  - textfile:
      directory: "{{ node_exporter_textfile_dir }}"
#  - filesystem:
#      ignored-mount-points: "^/(sys|proc|dev)($|/)"
#      ignored-fs-types: "^(sys|proc|auto)fs$"

node_exporter_disabled_collectors: []
