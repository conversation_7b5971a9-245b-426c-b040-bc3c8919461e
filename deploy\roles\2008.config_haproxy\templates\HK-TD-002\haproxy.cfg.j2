# file: /etc/haproxy/haproxy.cf
{{ ansible_managed|comment }}

global
    log /dev/log   local0
    log /dev/log    local1 notice
    chroot /var/lib/haproxy
    stats socket /var/lib/haproxy/stats
    stats timeout 30s
    user haproxy
    group haproxy
    daemon
listen td-mysql
    bind 0.0.0.0:11060
    mode tcp
    timeout connect 10s
    timeout client 120s
    timeout server 120s
    server td-mysql rm-3nskg96930p74rru3.mysql.rds.aliyuncs.com:3306

