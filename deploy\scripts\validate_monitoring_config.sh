#!/bin/bash

# 监控配置验证脚本
# 用法: ./validate_monitoring_config.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 验证 Ansible 配置文件
validate_ansible_config() {
    log_header "验证 Ansible 配置"
    
    # 检查 playbook 文件
    local playbook="2029.elasticsearch_exporter.playbook.yml"
    if [ -f "$playbook" ]; then
        log_info "✓ Playbook 文件存在: $playbook"
        
        # 验证 YAML 语法
        if ansible-playbook --syntax-check "$playbook" > /dev/null 2>&1; then
            log_info "✓ Playbook 语法正确"
        else
            log_error "✗ Playbook 语法错误"
            return 1
        fi
    else
        log_error "✗ Playbook 文件不存在: $playbook"
        return 1
    fi
    
    # 检查角色目录
    local roles=("2029.deploy_elasticsearch_exporter" "2029.config_elasticsearch_exporter")
    for role in "${roles[@]}"; do
        if [ -d "roles/$role" ]; then
            log_info "✓ 角色目录存在: $role"
        else
            log_error "✗ 角色目录不存在: $role"
            return 1
        fi
    done
}

# 验证 Prometheus 配置
validate_prometheus_config() {
    log_header "验证 Prometheus 配置"
    
    # 检查告警规则文件
    local rule_file="roles/2005.config_prometheus/files/common/rule.d/elasticsearch.rule.yml"
    if [ -f "$rule_file" ]; then
        log_info "✓ 告警规则文件存在"
        
        # 验证规则语法（如果 promtool 可用）
        if command -v promtool &> /dev/null; then
            if promtool check rules "$rule_file" > /dev/null 2>&1; then
                log_info "✓ 告警规则语法正确"
            else
                log_error "✗ 告警规则语法错误"
                return 1
            fi
        else
            log_warn "⚠ promtool 不可用，跳过规则语法检查"
        fi
    else
        log_error "✗ 告警规则文件不存在"
        return 1
    fi
    
    # 检查 file_sd 配置
    local file_sd_configs=(
        "roles/2005.config_prometheus/files/common/file_sd.d/elasticsearch.yml"
        "roles/2005.config_prometheus/files/SZ-ES-001/file_sd.d/elasticsearch.yml"
        "roles/2005.config_prometheus/files/SZ-MONT-002/file_sd.d/elasticsearch.yml"
        "roles/2005.config_prometheus/files/HK-MG-002/file_sd.d/elasticsearch.yml"
    )
    
    for config in "${file_sd_configs[@]}"; do
        if [ -f "$config" ]; then
            log_info "✓ File SD 配置存在: $(basename $(dirname $config))"
        else
            log_warn "⚠ File SD 配置不存在: $config"
        fi
    done
    
    # 检查 blackbox 配置
    local blackbox_configs=(
        "roles/2005.config_prometheus/files/common/blackbox_sd.d/blackbox_elasticsearch.yml"
        "roles/2005.config_prometheus/files/SZ-ES-001/blackbox_sd.d/blackbox_elasticsearch.yml"
        "roles/2005.config_prometheus/files/SZ-MONT-002/blackbox_sd.d/blackbox_elasticsearch.yml"
        "roles/2005.config_prometheus/files/HK-MG-002/blackbox_sd.d/blackbox_elasticsearch.yml"
    )
    
    for config in "${blackbox_configs[@]}"; do
        if [ -f "$config" ]; then
            log_info "✓ Blackbox 配置存在: $(basename $(dirname $config))"
        else
            log_warn "⚠ Blackbox 配置不存在: $config"
        fi
    done
}

# 验证角色配置
validate_role_config() {
    log_header "验证角色配置"
    
    # 检查部署角色文件
    local deploy_role="roles/2029.deploy_elasticsearch_exporter"
    local required_files=(
        "$deploy_role/defaults/main.yml"
        "$deploy_role/tasks/main.yml"
        "$deploy_role/tasks/install.yml"
        "$deploy_role/tasks/config.yml"
        "$deploy_role/tasks/service.yml"
        "$deploy_role/handlers/main.yml"
        "$deploy_role/templates/elasticsearch_exporter.service.j2"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            log_info "✓ 文件存在: $(basename $file)"
        else
            log_error "✗ 文件不存在: $file"
            return 1
        fi
    done
    
    # 检查配置角色文件
    local config_role="roles/2029.config_elasticsearch_exporter"
    local config_files=(
        "$config_role/tasks/main.yml"
        "$config_role/handlers/main.yml"
        "$config_role/vars/main.yml"
        "$config_role/templates/elasticsearch_exporter_multi.service.j2"
        "$config_role/templates/elasticsearch_cluster_config.yml.j2"
    )
    
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            log_info "✓ 配置文件存在: $(basename $file)"
        else
            log_error "✗ 配置文件不存在: $file"
            return 1
        fi
    done
}

# 验证网络连通性
validate_network_connectivity() {
    log_header "验证网络连通性"
    
    # ES 节点连通性
    local es_nodes=("*************:9200" "*************:9200" "*************:9200")
    
    for node in "${es_nodes[@]}"; do
        if curl -s --connect-timeout 5 "http://$node" > /dev/null; then
            log_info "✓ ES 节点可达: $node"
        else
            log_warn "⚠ ES 节点不可达: $node"
        fi
    done
    
    # 监控服务器连通性（如果在同一网络）
    local monitoring_hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${monitoring_hosts[@]}"; do
        if ping -c 1 -W 3 "$host" > /dev/null 2>&1; then
            log_info "✓ 监控主机可达: $host"
        else
            log_warn "⚠ 监控主机不可达: $host"
        fi
    done
}

# 验证依赖工具
validate_dependencies() {
    log_header "验证依赖工具"
    
    local tools=("ansible" "ansible-playbook" "curl" "jq")
    
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            log_info "✓ $tool 已安装"
        else
            log_error "✗ $tool 未安装"
            return 1
        fi
    done
    
    # 检查 Ansible 版本
    local ansible_version=$(ansible --version | head -n1 | cut -d' ' -f2)
    log_info "Ansible 版本: $ansible_version"
    
    # 检查 Python 版本
    local python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    log_info "Python 版本: $python_version"
}

# 生成配置摘要
generate_config_summary() {
    log_header "配置摘要"
    
    echo "Elasticsearch 集群节点:"
    echo "  - http://*************:9200"
    echo "  - http://*************:9200"
    echo "  - http://*************:9200"
    echo ""
    
    echo "监控组件:"
    echo "  - Elasticsearch Exporter (端口: 9114)"
    echo "  - Prometheus (告警规则 + 采集配置)"
    echo "  - Blackbox Exporter (HTTP 健康检查)"
    echo ""
    
    echo "部署目标主机:"
    echo "  - SZ-ES-001"
    echo "  - SZ-MONT-002"
    echo "  - HK-MG-002"
    echo ""
    
    echo "配置文件位置:"
    echo "  - Playbook: 2029.elasticsearch_exporter.playbook.yml"
    echo "  - 告警规则: roles/2005.config_prometheus/files/common/rule.d/elasticsearch.rule.yml"
    echo "  - 部署脚本: scripts/deploy_elasticsearch_monitoring.sh"
    echo "  - 健康检查: scripts/check_elasticsearch_health.sh"
}

# 主函数
main() {
    echo "========================================"
    echo "Elasticsearch 监控配置验证"
    echo "验证时间: $(date)"
    echo "========================================"
    echo ""
    
    local validation_passed=true
    
    # 执行各项验证
    if ! validate_dependencies; then
        validation_passed=false
    fi
    echo ""
    
    if ! validate_ansible_config; then
        validation_passed=false
    fi
    echo ""
    
    if ! validate_prometheus_config; then
        validation_passed=false
    fi
    echo ""
    
    if ! validate_role_config; then
        validation_passed=false
    fi
    echo ""
    
    validate_network_connectivity
    echo ""
    
    generate_config_summary
    echo ""
    
    # 输出验证结果
    if [ "$validation_passed" = true ]; then
        log_info "✓ 所有配置验证通过，可以开始部署"
        echo ""
        echo "下一步："
        echo "  ./scripts/deploy_elasticsearch_monitoring.sh"
    else
        log_error "✗ 配置验证失败，请修复错误后重试"
        exit 1
    fi
}

# 执行主函数
main "$@"
