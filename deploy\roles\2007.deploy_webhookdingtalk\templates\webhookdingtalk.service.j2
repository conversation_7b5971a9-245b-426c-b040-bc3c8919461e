{{ ansible_managed | comment }}

[Unit]
Description=WebhookDingtalk
After=network-online.target

[Service]
Type=simple
User=webhookdingtalk
Group=webhookdingtalk
ExecReload=/bin/kill -HUP $MAINPID
ExecStart=/usr/local/bin/prometheus-webhook-dingtalk \
  --web.listen-address=":{{WEBHOOKDINGTALK_HTTP_PORT}}" \
  --web.enable-ui \
  --web.enable-lifecycle \
  --config.file={{WEBHOOKDINGTALK_CONFIG_DIR}}/webhookdingtalk.yml
LimitNOFILE=65000
Restart=always

[Install]
WantedBy=multi-user.target
