# file: /etc/haproxy/haproxy.cf
{{ ansible_managed|comment }}

global
    log /dev/log	local0
    log /dev/log	local1 notice
    chroot /var/lib/haproxy
    stats socket /run/haproxy/admin.sock mode 660 level admin expose-fd listeners
    stats timeout 30s
    user haproxy
    group haproxy
    daemon
frontend stats
    bind *:8404
    http-request use-service prometheus-exporter if { path /metrics }
    stats enable
    stats uri /stats
    stats refresh 10s
listen ayers_gts_api
    bind 0.0.0.0:16868
    mode tcp
    timeout connect 10s
    timeout client 120s
    timeout server 120s
    server ayers_gts_api 172.16.40.101:18012
