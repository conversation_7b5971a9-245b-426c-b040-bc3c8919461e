# /etc/frp/fprc.ini

{{ansible_managed|comment}}

[common]
server_addr = ************
server_port = {{FRPS_PORT}}
token = {{FRP_AUTH}}
protocol = kcp
enable_prometheus = true

dashboard_addr = 0.0.0.0
dashboard_port = 55558
dashboard_user = {{FRPS_DASHBOARD_USER}}
dashboard_pwd = {{FRPS_DASHBOARD_PASSWORD}}

admin_addr = 0.0.0.0
admin_port = 55559
admin_user = {{FRPS_ADMIN_USER}}
admin_pwd = {{FRPS_ADMIN_PASSWORD}}

[gh81:rsync:873]
local_ip = 127.0.0.1
local_port = 873
remote_port = 11873
type = tcp
use_encryption = true
use_compression = true

[UAT94:traefik]
local_ip = ************
local_port = 443
remote_port = 11443
type = tcp
use_encryption = true
use_compression = true
