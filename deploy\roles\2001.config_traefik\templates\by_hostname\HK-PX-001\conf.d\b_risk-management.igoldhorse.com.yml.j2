{{ansible_managed|comment}}
http:
  middlewares:
    stripRiskManagementApi:
      stripPrefix:
        prefixes:
        - "/ltv"
        - "/marks"
        - "/fstatic"
        - "/fserver"
    hkPx001RiskManagementIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
    hkPx001RiskManagementBasicAuth:
      basicAuth:
        realm: RiskManagment
        users:
        - "clive.lam:$apr1$gK3za9HJ$w5uedLeUwILAKZPiKeZVN/"
  routers:
    risk-management.igoldhorse.com:
      service: hkPx001CaddyRiskManagement
      rule: Host(`risk-management.igoldhorse.com`)
      middlewares:
      - gzip
      #- hstsHeader
      #- hkPx001RiskManagementBasicAuth
      tls:
        certResolver: httpResolver
    ltv_risk-management.igoldhorse.com_api:
      service: hkPx001CaddyRiskManagementLtvApi
      rule: Host(`risk-management.igoldhorse.com`) && PathPrefix(`/ltv/api`)
      middlewares:
      - gzip
      - hstsHeader
      - stripRiskManagementApi
      tls:
        certResolver: httpResolver
    marks_risk-management.igoldhorse.com_api:
      service: hkPx001CaddyRiskManagementApi
      rule: Host(`risk-management.igoldhorse.com`) && PathPrefix(`/marks/api`)
      middlewares:
      - gzip
      - hstsHeader
      - stripRiskManagementApi
      tls:
        certResolver: httpResolver
    marks_risk-management.igoldhorse.com_static:
      service: hkPx001CaddyRiskManagementApi
      rule: Host(`risk-management.igoldhorse.com`) && PathPrefix(`/fstatic/static`)
      middlewares:
      - gzip
      - stripRiskManagementApi
      tls:
        certResolver: httpResolver
    marks_risk-management.igoldhorse.com_stru:
      service: hkPx001CaddyRiskManagementApi
      rule: Host(`risk-management.igoldhorse.com`) && PathPrefix(`/fserver/structure`)
      middlewares:
      - gzip
      - stripRiskManagementApi
      tls:
        certResolver: httpResolver
  services:
    hkPx001CaddyRiskManagement:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
    hkPx001CaddyRiskManagementApi:
      loadBalancer:
        servers:
        - url: http://HK-MG-001:35000
    hkPx001CaddyRiskManagementLtvApi:
      loadBalancer:
        servers:
        - url: http://HK-MG-001:35010
