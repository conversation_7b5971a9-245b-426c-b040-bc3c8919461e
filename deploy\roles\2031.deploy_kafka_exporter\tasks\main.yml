---
- name: create kafka_exporter system group
  group:
    name: kafka_exporter
    system: true
    state: present
    gid: 3246

- name: create kafka_exporter system user
  user:
    name: kafka_exporter
    system: true
    shell: "/usr/sbin/nologin"
    group: kafka_exporter
    createhome: false
    uid: 3246

- name: kafka_exporter url
  debug:
    msg: "{{KAFKA_EXPORTER_DOWNLOAD_URL}}"

- name: download kafka_exporter binary to local folder
  become: false
  get_url:
    use_proxy: true
    url: "{{KAFKA_EXPORTER_DOWNLOAD_URL}}"
    dest: "/tmp/kafka_exporter-{{KAFKA_EXPORTER_VERSION}}.tar.gz"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: unpack kafka_exporter binaries
  unarchive:
    src: "/tmp/kafka_exporter-{{KAFKA_EXPORTER_VERSION}}.tar.gz"
    dest: "/tmp"
    remote_src: true
  check_mode: false

- name: copy official kafka_exporter binaries
  copy:
    src: "/tmp/kafka_exporter-{{KAFKA_EXPORTER_VERSION}}.linux-amd64/{{item}}"
    dest: "/usr/local/bin/{{item}}"
    mode: '755'
    owner: root
    group: root
    remote_src: true
  with_items:
  - kafka_exporter
  notify:
  - restart kafka_exporter

- name: create systemd service unit
  template:
    src: kafka_exporter.service.j2
    dest: /etc/systemd/system/kafka_exporter.service
    owner: root
    group: root
    mode: 0644
  notify:
  - restart kafka_exporter

- name: enabled kafka_exporter systemd
  systemd:
    name: kafka_exporter
    daemon-reload: true
    enabled: true
