---
- name: Create elasticsearch_exporter user
  user:
    name: "{{ elasticsearch_exporter_user }}"
    group: "{{ elasticsearch_exporter_group }}"
    system: true
    shell: /bin/false
    home: /var/lib/elasticsearch_exporter
    createhome: false
  become: true

- name: Create elasticsearch_exporter directories
  file:
    path: "{{ item }}"
    state: directory
    owner: "{{ elasticsearch_exporter_user }}"
    group: "{{ elasticsearch_exporter_group }}"
    mode: '0755'
  become: true
  loop:
    - "{{ elasticsearch_exporter_root_path }}"
    - "{{ elasticsearch_exporter_bin_path }}"

- name: Check if elasticsearch_exporter binary exists
  stat:
    path: "{{ elasticsearch_exporter_bin_path }}/elasticsearch_exporter"
  register: elasticsearch_exporter_binary_check

- name: Download elasticsearch_exporter
  get_url:
    url: "{{ elasticsearch_exporter_url }}"
    dest: "{{ elasticsearch_exporter_package_path }}"
    mode: '0644'
  become: true
  when: not elasticsearch_exporter_binary_check.stat.exists or elasticsearch_exporter_force_reinstall

- name: Extract elasticsearch_exporter
  unarchive:
    src: "{{ elasticsearch_exporter_package_path }}"
    dest: "{{ elasticsearch_exporter_download_path }}"
    remote_src: true
    owner: root
    group: root
    mode: '0755'
  become: true
  when: not elasticsearch_exporter_binary_check.stat.exists or elasticsearch_exporter_force_reinstall

- name: Copy elasticsearch_exporter binary
  copy:
    src: "{{ elasticsearch_exporter_src_bin }}"
    dest: "{{ elasticsearch_exporter_bin_path }}/elasticsearch_exporter"
    owner: "{{ elasticsearch_exporter_user }}"
    group: "{{ elasticsearch_exporter_group }}"
    mode: '0755'
    remote_src: true
  become: true
  when: not elasticsearch_exporter_binary_check.stat.exists or elasticsearch_exporter_force_reinstall
  notify: restart elasticsearch_exporter

- name: Create symlink to elasticsearch_exporter binary
  file:
    src: "{{ elasticsearch_exporter_bin_path }}/elasticsearch_exporter"
    dest: /usr/local/bin/elasticsearch_exporter
    state: link
  become: true

- name: Clean up downloaded files
  file:
    path: "{{ item }}"
    state: absent
  become: true
  loop:
    - "{{ elasticsearch_exporter_package_path }}"
    - "{{ elasticsearch_exporter_download_path }}/{{ elasticsearch_exporter_release_name }}"
  when: not elasticsearch_exporter_binary_check.stat.exists or elasticsearch_exporter_force_reinstall
