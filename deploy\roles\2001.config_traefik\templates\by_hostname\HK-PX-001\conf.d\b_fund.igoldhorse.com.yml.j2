{{ansible_managed|comment}}
http:
  middlewares:
    hkPx001FundIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    fund.igoldhorse.com:
      service: hkPx001CaddyFund
      rule: Host(`fund.igoldhorse.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
  services:
    hkPx001CaddyFund:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
