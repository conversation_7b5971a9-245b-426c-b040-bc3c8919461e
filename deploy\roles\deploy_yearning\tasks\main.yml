---
- name: create yearning system group
  group:
    name: yearning
    system: true
    state: present
    gid: 3241

- name: create yearning system user
  user:
    name: yearning
    system: true
    shell: "/usr/sbin/nologin"
    group: yearning
    uid: 3241

- name: create yearning configuration directories
  file:
    path: "{{ item }}"
    state: directory
    owner: root
    group: yearning
    mode: 0770
  with_items:
  - "{{ YEARNING_CONFIG_DIR }}"

- name: yearning url
  debug:
    msg: "{{YEARNING_DOWNLOAD_URL}}"

- name: download yearning binary to local folder
  become: false
  get_url:
    use_proxy: true
    url: "{{YEARNING_DOWNLOAD_URL}}"
    dest: "/tmp/yearning-{{YEARNING_VERSION}}.zip"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: install unzip
  package:
    name: unzip
    state: present

- name: unpack yearning binaries
  unarchive:
    src: "/tmp/yearning-{{YEARNING_VERSION}}.zip"
    dest: "/opt"
    remote_src: true
  check_mode: false
  notify:
  - restart yearning
#- name: copy official yearning binaries
#  shell:  mv /tmp/Yearning/* /opt/Yearning
#  notify:
#  - restart yearning

- name: create systemd service unit
  template:
    src: yearning.service.j2
    dest: /etc/systemd/system/yearning.service
    owner: root
    group: root
    mode: 0644
  notify:
  - restart yearning

- name: configure yearning
  template:
    src: conf.toml.j2
    dest: "{{YEARNING_CONFIG_DIR}}/conf.toml"
    force: true
    owner: root
    group: yearning
    mode: 0640
  notify:
  - restart yearning

- name:  yearning install
  shell:  yes | /opt/Yearning/Yearning  install -c /opt/Yearning/conf.toml
  
- name: ensure yearning start at boot
  systemd:
    name: yearning
    enabled: true
    daemon_reload: true


