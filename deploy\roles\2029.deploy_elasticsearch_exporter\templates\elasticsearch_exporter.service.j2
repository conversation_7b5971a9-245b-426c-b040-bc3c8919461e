[Unit]
Description=Elasticsearch Exporter
Documentation=https://github.com/prometheus-community/elasticsearch_exporter
Wants=network-online.target
After=network-online.target

[Service]
Type=simple
User={{ elasticsearch_exporter_user }}
Group={{ elasticsearch_exporter_group }}
ExecStart={{ elasticsearch_exporter_bin_path }}/elasticsearch_exporter \
{% for option in elasticsearch_exporter_options %}
{% if option != '' %}
  --{{ option }}{% if not loop.last %} \{% endif %}
{% endif %}
{% endfor %}

SyslogIdentifier=elasticsearch_exporter
Restart=always
RestartSec=1
StartLimitInterval=0

{% if elasticsearch_exporter_private_tmp %}
PrivateTmp=yes
{% endif %}

ProtectHome=yes
NoNewPrivileges=yes

ProtectSystem=strict
ProtectControlGroups=true
ProtectKernelModules=true
ProtectKernelTunables=yes

[Install]
WantedBy=multi-user.target
