{{ansible_managed|comment}}
tcp:
  middlewares:
    nvpnIpWhiteList:
      ipWhiteList:
        sourceRange:
        - "***********/24"
  routers:
    nvpn:
      service: fwdev
      rule: HostSNI(`n.vpn.fwdev.top`)
      tls:
        passthrough: true
  services:
    fwdev:
      loadBalancer:
        proxyProtocol:
          version: 2
        servers:
        - address: uatproxy.igoldhorse.com:1443
