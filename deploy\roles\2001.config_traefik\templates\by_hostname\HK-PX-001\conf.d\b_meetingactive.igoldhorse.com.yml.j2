{{ansible_managed|comment}}
http:
  middlewares:
    hkPx001MeetingactiveIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    meetingactive.igoldhorse.com:
      service: hkPx001CaddyMeetingactive
      rule: Host(`meetingactive.igoldhorse.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
  services:
    hkPx001CaddyMeetingactive:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
