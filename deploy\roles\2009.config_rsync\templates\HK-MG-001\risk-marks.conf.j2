{{ansible_managed|comment}}

[risk_marks]
uid = wukong
gid = wukong
comment = RISK MARKS
use chroot = yes
path = {{risk_marks_path}}
list = yes
read only = no
write only = yes
# refuse options = delete
secrets file = /etc/rsyncd.secrets
auth users = {{risk_marks_user}}

[risk_marks_uat_read]
uid = wukong
gid = wukong
comment = RISK MARKS
use chroot = yes
path = {{risk_marks_path}}
list = yes
read only = yes
# refuse options = delete
secrets file = /etc/rsyncd.secrets
auth users = {{risk_marks_user_uat_read}}
