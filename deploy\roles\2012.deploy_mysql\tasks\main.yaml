
- name: create /etc/mysql
  file:
    path: "{{item}}"
    state: directory
  with_items:
  - /etc/mysql
  - /etc/mysql/conf.d

- name: create lower_case_table_names before mysql install and start
  lineinfile:
    create: true
    path: /etc/mysql/conf.d/mysqld.cnf
    line: '{{item}}'
  with_items:
  - '[mysqld]'
  - 'lower_case_table_names = 1'

- name: apt install mysql server
  apt:
    name:
    - mysql-server
    - mysql-client
    - python3-pymysql
    state: present
    update_cache: true

- name: change mysqld conf
  lineinfile:
    create: true
    path: /etc/mysql/conf.d/mysqld.cnf
    regex: '{{item.regex}}'
    line: '{{item.line}}'
  with_items:
  - line: 'port = 33260'
    regex: 'port.*'
  - line: 'bind-address = 0.0.0.0'
    regex: 'bind-address.*'
  - line: 'group_concat_max_len = 102400'
    regex: 'group_concat_max_len.*'
  notify:
  - restart mysql

- name: change mysqld conf2
  lineinfile:
    create: true
    path: /etc/mysql/mysql.conf.d/mysqld.cnf
    regex: '{{item.regex}}'
    line: '{{item.line}}'
  with_items:
  - line: 'bind-address = 0.0.0.0'
    regex: 'bind-address.*'
  notify:
  - restart mysql

- name: Generate /root/.my.cnf for mysqladmin
  template:
    src: mysqladmin.my.cnf.j2
    dest: /root/.my.cnf
    owner: root
    group: root
    mode: 0400

- name: Ensure mysql service started
  systemd:
    daemon_reload: yes
    enabled: yes
    name: mysql
    state: started

- name: Change root password
  mysql_user:
    name: root
    host_all: true
    password: "{{ mysql_root_password }}"
    login_user: root
    login_unix_socket: "{{ mysql_socket }}"
    sql_log_bin: false
  ignore_errors: yes


- name: Removes all anonymous user accounts
  mysql_user:
    user: ""
    host_all: yes
    login_user: root
    login_password: "{{ mysql_root_password }}"
    login_unix_socket: "{{ mysql_socket }}"
    sql_log_bin: false
    state: absent

- name: performance per host task
  include_tasks: '{{ansible_hostname}}.yml'

