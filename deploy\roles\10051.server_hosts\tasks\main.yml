- name: static hosts with lan ip
  lineinfile:
    path: /etc/hosts
    regex: '.*{{item}}$'
    line: '{{hostvars[item].lan_ip}} {{item}}'
  when: hostvars[item].ansible_host is defined and hostvars[item].lan_ip is defined
  with_items: "{{groups.all_servers}}"

- name: lan domains with lan ip
  lineinfile:
    path: /etc/hosts
    regex: '.*{{item}}$'
    line: '{{hostvars[item].lan_ip}} {{item}}'
  when: hostvars[item].lan_ip is defined
  with_items: "{{groups.landomains}}"

- name: 'static hosts {{item.hostname}}'
  lineinfile:
    path: /etc/hosts
    regex: '.*{{item.hostname}}$'
    line: '{{vars[item.ip_field]}} {{item.hostname}}'
  when: vars[item.ip_field] is defined
  with_items:
  - hostname: n.vpn.fwdev.top
    ip_field: nvpn_ip
  - hostname: n.vpn.fwdev.ltd
    ip_field: nvpn_ip
  - hostname: GH-MINIO
    ip_field: minio_ip
  - hostname: LOKI
    ip_field: loki_ip
  - hostname: ALERT
    ip_field: alert_ip
  - hostname: AYERS_GTS_API
    ip_field: ayers_gts_api_ip
  - hostname: GREENB-IODB-PROXY
    ip_field: greenb_iodb_proxy_ip
  - hostname: LANPROXY
    ip_field: lanproxy_ip
