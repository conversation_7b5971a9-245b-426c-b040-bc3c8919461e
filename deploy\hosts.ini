[all:vars]
ansible_ssh_pipelining=yes
ansible_python_interpreter=/usr/bin/python3

[not_init_user:vars]
ansible_ssh_common_args='-o StrictHostKeyChecking=no -o PreferredAuthentications=password -o PasswordAuthentication=yes -o ProxyCommand="ssh -o StrictHostKeyChecking=no -W %h:%p -q -p 22222 ghci@************"'

[not_init_user]

[not_init_vdb:vars]
ansible_ssh_common_args='-o StrictHostKeyChecking=no -o ProxyCommand="ssh -o StrictHostKeyChecking=no -W %h:%p -q -p 22222 ghci@************"'

[not_init_vdb]

[ayers_servers]
AYS-GW-001    ansible_host=************ tags=[ubuntu,ayers_gateway]
GREENB-IODB-PROXY    ansible_host=************ tags=[ubuntu,greenb_iodb]

[hk_servers:vars]
lanproxy_ip=************

[hk_servers]
HK-PX-001    ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************ loki_ip=************ tags=[ubuntu,proxy]
HK-MG-001    ansible_host=************** lan_ip=************  nvpn_ip=************* minio_ip=************ loki_ip=************ ayers_gts_api_ip=*************** tags=[ubuntu,datahouse,timescaledb,redis,jumpproxy,ayers]
HK-MG-002    ansible_host=************   lan_ip=************  nvpn_ip=************* minio_ip=************ loki_ip=************ alert_ip=************ tags=[ubuntu,datahouse,prometheus,loki,alertmanager,grafana,frp_server,minio]
HK-TD-001    ansible_host=*************  lan_ip=************  nvpn_ip=************* minio_ip=************ loki_ip=************ ayers_gts_api_ip=*************** tags=[ubuntu,trade,ayers]
HK-TD-002    ansible_host=************   lan_ip=************  nvpn_ip=************* minio_ip=************ loki_ip=************ tags=[ubuntu,trade]
HK-TD-003    ansible_host=************** lan_ip=************* nvpn_ip=************* minio_ip=************ loki_ip=************ ayers_gts_api_ip=*************** greenb_iodb_proxy_ip=*************** tags=[ubuntu,trade]
HK-PROXY-001 ansible_host=************* lan_ip=************ nvpn_ip=************* minio_ip=************ loki_ip=************ tags=[ubuntu,proxy]
HK-OPENAPI-001 ansible_host=************** lan_ip=************* nvpn_ip=************* minio_ip=************ loki_ip=************ tags=[ubuntu,proxy]

[sh_servers]
# SH
SH-PX-001    ansible_host=*************  lan_ip=************  nvpn_ip=************  tags=[ubuntu,proxy]

[sz_servers:vars]
ansible_ssh_common_args='-o StrictHostKeyChecking=no -o ProxyCommand="ssh -o StrictHostKeyChecking=no -W %h:%p -q -p 22222 ghci@************"'

[sz_servers:vars]
lanproxy_ip=*************

[sz_servers]
SZ-TASK-001     ansible_host=************** lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos]
SZ-KAFKA-001    ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos]
SZ-KAFKA-002    ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos]
SZ-KAFKA-003    ansible_host=************** lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos]
SZ-STORE-001    ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos] ansible_python_interpreter=/usr/bin/python
SZ-STORE-002    ansible_host=************** lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos] ansible_python_interpreter=/usr/bin/python
SZ-STORE-003    ansible_host=************   lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos] ansible_python_interpreter=/usr/bin/python
SZ-REDIS-001    ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos]
SZ-REDIS-002    ansible_host=************** lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos]
SZ-REDIS-003    ansible_host=120.79.136.244 lan_ip=172.29.80.112 nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos]
SZ-PUSH-001     ansible_host=47.107.69.161  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos,rabbitmq,minio]
SZ-ES-001       ansible_host=************   lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no alert_ip=************* tags=[centos,prometheus,loki,grafana,alertmanager,frp_server,jumpproxy]
SZ-CVT-001      ansible_host=120.78.193.219 lan_ip=172.29.80.109 nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos]
SZ-REC-001      ansible_host=47.106.114.156 lan_ip=172.29.80.107 nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos]
SZ-CAL-001      ansible_host=47.106.91.56   lan_ip=172.29.80.108 nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[centos]
SZ-PX-001       ansible_host=47.106.95.178  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[ubuntu,proxy]
SZ-CSC-001      ansible_host=172.29.80.132  lan_ip=172.29.80.132 nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[ubuntu]
SZ-CSC-002      ansible_host=172.29.80.134  lan_ip=172.29.80.134 nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[ubuntu]
SZ-CSC-003      ansible_host=172.29.80.131  lan_ip=172.29.80.131 nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[ubuntu]
SZ-OPACC-001    ansible_host=120.79.82.48   lan_ip=172.29.80.133 nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[ubuntu]
SZ-NEWS-001     ansible_host=172.29.80.130  lan_ip=172.29.80.130 nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[ubuntu]
SZ-CLHOUSE-001  ansible_host=172.20.0.227   lan_ip=172.20.0.227  nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[ubuntu]
SZ-CLHOUSE-002  ansible_host=172.20.0.226   lan_ip=172.20.0.226  nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[ubuntu]
SZ-CLHOUSE-003  ansible_host=172.20.0.228   lan_ip=172.20.0.228  nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[ubuntu]
SZ-MKTAPI-001   ansible_host=172.20.0.229   lan_ip=172.20.0.229  nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[ubuntu]
SZ-MONT-002     ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* alert_ip=************* suplog=no tags=[ubuntu,loki]
SZ-DERIV-001    ansible_host=172.29.80.136  lan_ip=172.29.80.136 nvpn_ip=************* minio_ip=************* loki_ip=************* suplog=no tags=[ubuntu,java]

[hk_corp]
GH81 ansible_host=172.16.80.81 lan_ip=172.16.80.81 tags=[ubuntu]
GH82 ansible_host=172.16.80.82 lan_ip=172.16.80.82 tags=[ubuntu]
GH84 ansible_host=172.16.80.84 lan_ip=172.16.80.84 tags=[ubuntu]

[hk_qcloud_lighthouse]
LH-FG1 ansible_host=43.132.180.193 USE_PROXY=false
LH-FG2 ansible_host=43.129.191.143 USE_PROXY=false

[local_servers]
p902 ansible_host=192.168.1.92 lan_ip=192.168.1.92 tags=[ubuntu]

[all_servers:children]
hk_servers
sz_servers
ayers_servers

[kafka]
SZ-KAFKA-001    ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos]
SZ-KAFKA-002    ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos]
SZ-KAFKA-003    ansible_host=************** lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos]

[alertsz]
SZ-MONT-002     ansible_host=*************  lan_ip=************* 
SZ-ES-001       ansible_host=************   lan_ip=************* 

[alerthk]
HK-MG-002    ansible_host=************   lan_ip=************
HK-TD-003    ansible_host=************** lan_ip=*************

[landomains]
ayersgts.lan.ghhk lan_ip=*************
ayersvrs.lan.ghhk lan_ip=*************
ayerstrd.lan.ghhk lan_ip=*************
userstd.lan.ghhk lan_ip=************* 
mktquot-stock.lan.ghsz lan_ip=************
mktquot-service-open.lan.ghsz lan_ip=************
f-open-api.lan.ghsz lan_ip=************
open-api-gateway.lan.ghhk lan_ip=************
open-api-auth.lan.ghhk lan_ip=************
