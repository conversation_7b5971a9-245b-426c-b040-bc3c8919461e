{{ ansible_managed | comment }}

bind 0.0.0.0

daemonize no

pidfile /var/run/redis/redis_{{ item.port }}.pid
port {{ item.port }}

tcp-backlog {{ item.tcp_backlog | default('511') }}

tcp-keepalive {{ item.tcp_keepalive | default('300') }}

protected-mode {{ item.protected_mode | default('yes') }}

timeout {{ item.timeout | default('86400') }}

loglevel {{ item.loglevel | default('notice') }}

logfile /var/log/redis/{{ item.port }}.log

save {{ item.persistence_save | default('""') }}

dbfilename dump_{{ item.port }}.rdb

repl-diskless-sync {{ item.repl_diskless_sync | default('no') }}

repl-diskless-sync-delay {{ item.repl_diskless_delay | default('5') }}

repl-timeout {{ item.repl_timeout | default('60') }}

repl-disable-tcp-nodelay {{ item.repl_disable_tcp_nodelay | default('no') }}

repl-backlog-size {{ item.repl_backlog_size | default('1mb') }}

repl-backlog-ttl {{ item.repl_backlog_ttl | default('3600') }}

slave-priority {{ item.slave_priority | default('100') }}

maxmemory {{ item.maxmemory }}

maxmemory-policy {{ item.maxmemory_policy | default('volatile-lru') }}

maxmemory-samples {{ item.maxmemory_samples | default('27') }}

appendonly no

appendfilename "appendonly.aof"

appendfsync everysec

no-appendfsync-on-rewrite no

auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

aof-load-truncated yes

cluster-enabled {{ item.cluster_enabled | default('no') }}

{% if item.cluster_enabled is defined and item.cluster_enabled == 'yes' %}

cluster-config-file /etc/redis/nodes-{{ item.port | default('6379') }}.conf

cluster-node-timeout {{ item.cluster_node_timeout | default('15000') }}

cluster-slave-validity-factor {{ item.cluster_slave_validity_factor | default('10') }}

cluster-migration-barrier {{ item.cluster_migration_barrier | default('1') }}

cluster-require-full-coverage {{ item.cluster_require_full_coverage | default('no') }}

{% endif %}
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit slave {{ item.client_output_buffer_limit_slave_hard | default('256mb') }} {{ item.client_output_buffer_limit_slave_soft | default('64mb') }} {{ item.client_output_buffer_limit_slave_time | default('60') }}
client-output-buffer-limit pubsub 32mb 8mb 60

hz 10

aof-rewrite-incremental-fsync yes

dir /var/lib/redis

########################### ACTIVE DEFRAGMENTATION #######################

# Enabled active defragmentation
activedefrag {{ item.activedefrag | default('no') }}

# Minimum amount of fragmentation waste to start active defrag
active-defrag-ignore-bytes {{ item.active_defrag_ignore_bytes | default('100mb') }}

# Minimum percentage of fragmentation to start active defrag
active-defrag-threshold-lower {{ item.active_defrag_threshold_lower | default('10') }}

# Maximum percentage of fragmentation at which we use maximum effort
active-defrag-threshold-upper {{ item.active_defrag_threshold_upper | default('100') }}

# Minimal effort for defrag in CPU percentage
active-defrag-cycle-min {{ item.active_defrag_cycle_min | default('5') }}

# Maximal effort for defrag in CPU percentage
active-defrag-cycle-max {{ item.active_defrag_cycle_min | default('75') }}
