{{ ansible_managed|comment }}

[mysqld]
# Connection and Thread variables
port                            = 3306
bind_address                    = 0.0.0.0

pid_file                        = {{ mysql_pidfile }}
socket                          = {{ mysql_socket }}
basedir                         = /usr
datadir                         = /var/lib/mysql
tmpdir                          = /tmp/
authentication_policy           = mysql_native_password
default_storage_engine          = INNODB
character_set_server            = utf8mb4
max_connections                 = 505
max_user_connections            = 500
max_connect_errors              = 200000
max_allowed_packet              = 16M
tmp_table_size                  = 256M
max_heap_table_size             = 96M
interactive_timeout             = 600
wait_timeout                    = 600
thread_cache_size               = 505
transaction_isolation           = READ-COMMITTED
lower_case_table_names          = 1
group_concat_max_len            = 102400

# Session variables
sort_buffer_size                             = 2M
tmp_table_size                               = 32M

# Other buffers and caches
table_definition_cache                       = 1400
table_open_cache                             = 2000
table_open_cache_instances                   = 16

skip_name_resolve               = 0

# 时区
default_time_zone               = "+8:00"

# INNODB 优化
innodb_file_per_table           = 1
innodb_log_file_size            = 256M
innodb_buffer_pool_size         = 500M
innodb_flush_method             = O_DIRECT

# prevent use of non-transactional storage engines
disabled_storage_engines        = "MyISAM,BLACKHOLE,FEDERATED,ARCHIVE"

# Binary logging and Replication
server_id                           = {{ ansible_play_hosts.index(inventory_hostname) + 1 }}
binlog-row-image                    = MINIMAL
binlog-rows-query-log-events        = ON
log-bin-trust-function-creators     = TRUE
log_bin                             = mysql-bin
binlog_format                       = ROW
binlog_checksum                     = NONE
binlog_expire_logs_seconds          = 604800 # 7 days
binlog_cache_size                   = 1M
binlog_stmt_cache_size              = 1M
max_binlog_size                     = 128M
sync_binlog                         = 0
log_timestamps                      = system

# Slow Query log
slow_query_log                  = 1
long_query_time                 = 10
log_queries_not_using_indexes   = 1
slow_query_log_file             = {{ mysql_logdir | regex_replace("/$") }}/mysqld.slow.log
min_examined_row_limit          = 100
general_log                     = 0
general_log_file                = {{ mysql_logdir | regex_replace("/$") }}/mysqld.log

# Error log
log_error                       = {{ mysql_logdir | regex_replace("/$") }}/mysqld.error.log
log_error_verbosity             = 2
relay_log                       = mysql-relay
relay_log_purge                 = 1
relay_log_recovery              = ON
log_replica_updates               = ON

# Security variables
local_infile                                 = 0
allow_suspicious_udfs                        = false

gtid_mode                                    = ON
enforce_gtid_consistency                     = ON

replica_parallel_type                          = LOGICAL_CLOCK
replica_preserve_commit_order                  = 1
replica_parallel_workers                       = 16


report_host                             = {{ ansible_default_ipv4.address }}
report_port                             = 3306

[client]
default_character_set           = utf8mb4
port                            = 3306
socket                          = {{ mysql_socket }}

[mysql]
default_character_set           = utf8mb4
port                            = 3306
socket                          = {{ mysql_socket }}

[mysqldump]
max_allowed_packet              = 16M
port                            = 3306
socket                          = {{ mysql_socket }}
