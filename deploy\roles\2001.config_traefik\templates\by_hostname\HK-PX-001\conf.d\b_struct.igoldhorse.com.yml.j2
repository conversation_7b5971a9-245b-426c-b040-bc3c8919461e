{{ansible_managed|comment}}
http:
  middlewares:
    hkPx001StructIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
    openapiAuth:
      forwardAuth:
        address: http://HK-PROXY-001:8210/open-api-auth/auth_api/traffic_authentication
        trustForwardHeader: true
  routers:
    structpush.igoldhorse.com:
      service: hkPx001Struct
      rule: Host(`struct-push.igoldhorse.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
  services:
    hkPx001Struct:
      loadBalancer:
        servers:
        - url: http://HK-OPENAPI-001:8059
