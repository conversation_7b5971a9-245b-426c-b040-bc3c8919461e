---
- name: Create elasticsearch_exporter systemd service file (simple version)
  template:
    src: elasticsearch_exporter_simple.service.j2
    dest: /etc/systemd/system/elasticsearch_exporter.service
    owner: root
    group: root
    mode: '0644'
  become: true
  notify:
    - reload systemd
    - restart elasticsearch_exporter

- name: Create logrotate configuration for elasticsearch_exporter
  template:
    src: logrotate.j2
    dest: /etc/logrotate.d/elasticsearch_exporter
    owner: root
    group: root
    mode: '0644'
  become: true
  when: elasticsearch_exporter_log_output != 'journal'
