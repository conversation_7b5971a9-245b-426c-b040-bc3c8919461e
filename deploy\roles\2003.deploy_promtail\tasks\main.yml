---
- name: create promtail system group
  group:
    name: promtail
    system: true
    state: present
    gid: 3234

- name: create data directory
  file:
    path: /data
    state: directory
    owner: root
    group: root
    mode: 0755

- name: create promtail system user
  user:
    name: promtail
    system: true
    shell: "/usr/sbin/nologin"
    group: promtail
    groups:
    - wukong
    createhome: false
    home: "{{ PROMTAIL_DB_DIR }}"
    uid: 3234

- name: create promtail data directory
  file:
    path: "{{ PROMTAIL_DB_DIR }}"
    state: directory
    recurse: true
    owner: promtail
    group: promtail
    mode: 0755

- name: create promtail configuration directories
  file:
    path: "{{ item }}"
    state: directory
    owner: root
    group: promtail
    mode: 0770
  with_items:
  - "{{ PROMTAIL_CONFIG_DIR }}"

- name: promtail url
  debug:
    msg: "{{PROMTAIL_DOWNLOAD_URL}}"

- name: download promtail binary to local folder
  become: true
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "{{PROMTAIL_DOWNLOAD_URL}}"
    dest: "/tmp/promtail-linux-amd64"
    #checksum: '{{PROMTAIL_CHECKSUM}}'
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2


#- name: install unzip
#  package:
#    name: unzip
#    state: present
#
#- name: unpack promtail binaries
#  unarchive:
#    src: "/tmp/promtail-{{PROMTAIL_VERSION}}.zip"
#    dest: "/tmp"
#    remote_src: true
#  check_mode: false

- name: copy official promtail binaries
  copy:
    src: "/tmp/promtail-linux-amd64"
    dest: "/usr/local/bin/promtail"
    mode: '755'
    owner: root
    group: root
    remote_src: true
  #with_items:
  #- promtail-linux-amd64
  notify:
  - restart promtail

- name: create systemd service unit
  template:
    src: promtail.service.j2
    dest: /etc/systemd/system/promtail.service
    owner: root
    group: root
    mode: 0644
  notify:
  - restart promtail

- name: enabled promtail service
  systemd:
    name: promtail
    daemon_reload: true
    enabled: true
