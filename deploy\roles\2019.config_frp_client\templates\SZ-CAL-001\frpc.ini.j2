# /etc/frp/fprc.ini

{{ansible_managed|comment}}

[common]
server_addr = {{hostvars['HK-MG-002']['ansible_host']}}
server_port = {{FRPS_PORT}}
token = {{FRP_AUTH}}
protocol = kcp
enable_prometheus = true

dashboard_addr = 0.0.0.0
dashboard_port = {{FRPS_PORT+1}}
dashboard_user = {{FRPS_DASHBOARD_USER}}
dashboard_pwd = {{FRPS_DASHBOARD_PASSWORD}}

admin_addr = 0.0.0.0
admin_port = {{FRPS_PORT+2}}
admin_user = {{FRPS_ADMIN_USER}}
admin_pwd = {{FRPS_ADMIN_PASSWORD}}

[SZ-CAL-001:quot_common:8100]
local_ip = *************
local_port = 8100
remote_port = 8100
type = tcp
use_encryption = true
use_compression = true

[SZ-CAL-001:quot_option:8101]
local_ip = *************
local_port = 8101
remote_port = 8101
type = tcp
use_encryption = true
use_compression = true

[SZ-CAL-001:stocklist:8102]
local_ip = *************
local_port = 8102
remote_port = 8102
type = tcp
use_encryption = true
use_compression = true

[SZ-PUSH-001:mqtt-websocket:15675]
local_ip = *************
local_port = 15675
remote_port = 15675
type = tcp
use_encryption = true
use_compression = true

[SZ-PUSH-001:mqtt:1883]
local_ip = *************
local_port = 1883
remote_port = 1883
type = tcp
use_encryption = true
use_compression = true

[SZ-OPACC-001:amqp:5672]
local_ip = *************
local_port = 5672
remote_port = 56720
type = tcp
use_encryption = true
use_compression = true

[SZ-CSC-001:quot_common_a:8020]
local_ip = *************
local_port = 8020
remote_port = 8020
type = tcp
use_encryption = true
use_compression = true

[SZ-CSC-003:mkt_delay:8043]
local_ip = *************
local_port = 8043
remote_port = 8043
type = tcp
use_encryption = true
use_compression = true

[SZ-CAL-001:quot_common_hk:8021]
local_ip = *************
local_port = 8021
remote_port = 8021
type = tcp
use_encryption = true
use_compression = true

[SZ-CAL-001:quot_common_us:8022]
local_ip = *************
local_port = 8022
remote_port = 8022
type = tcp
use_encryption = true
use_compression = true

[SZ-TASK-001:code:8028]
local_ip = *************
local_port = 8028
remote_port = 8028
type = tcp
use_encryption = true
use_compression = true

[SZ-CAL-001:quot_mgr:8031]
local_ip = *************
local_port = 8031
remote_port = 8031
type = tcp
use_encryption = true
use_compression = true

[SZ-PUSH-001:quot_mgr:8000]
local_ip = *************
local_port = 8000
remote_port = 8000
type = tcp
use_encryption = true
use_compression = true

[SZ-NEWS-001:user_center:8220]
local_ip = *************
local_port = 8220
remote_port = 8220
type = tcp
use_encryption = true
use_compression = true

[SZ-MKTAPI-001:struct_task:8059]
local_ip = ************
local_port = 8059
remote_port = 8059
type = tcp
use_encryption = true
use_compression = true

[SZ-NEWS-001:biz_struct:8108]
local_ip = *************
local_port = 8108
remote_port = 8108
type = tcp
use_encryption = true
use_compression = true

[SZ-CAL-001:platform_core:8029]
local_ip = *************
local_port = 8029
remote_port = 8029
type = tcp
use_encryption = true
use_compression = true

[SZ-TASK-001:option:8027]
local_ip = *************
local_port = 8027
remote_port = 8027
type = tcp
use_encryption = true
use_compression = true
