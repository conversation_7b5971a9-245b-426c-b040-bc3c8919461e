---
# defaults file for ansible-rabbitmq
rabbitmq_config: []
# - queue_name: logstash
#   durable: true
#   exchange_name: logstash
#   type: direct
#   routing_key: logstash
#   tags: "ha-mode=all,ha-sync-mode=automatic"
# - queue_name: logstash-quorum
#   durable: true
#   exchange_name: logstash-quorum
#   type: direct
#   routing_key: logstash
#   queue_type: quorum
#   tags: "ha-mode=all,ha-sync-mode=automatic"
# - policy_pattern: ".*"
#   vhost: apps
#   tags: "ha-mode=all,ha-sync-mode=automatic"

# Defines if rabbitmq ha should be configured
rabbitmq_config_ha: false

rabbitmq_config_service: false
rabbitmq_config_file: rabbitmq.config.j2
rabbitmq_config_env_file: rabbitmq-env.conf.j2
rabbitmq_env_config: {}

# Defines if setting up a rabbitmq cluster
rabbitmq_enable_clustering: false
# Defines the inventory host that should be considered master
rabbitmq_master: None

rabbitmq_erlang_cookie_file: /var/lib/rabbitmq/.erlang.cookie

rabbitmq_listen_port: 5672
rabbitmq_listeners: []
rabbitmq_vm_memory_high_watermark: 0.6
rabbitmq_vm_memory_calculation_strategy: rss
rabbitmq_dump_log_write_threshold: 100000

# - 127.0.0.1
# - '::1'

# Uncomment to set cluster partition handling strategy (https://www.rabbitmq.com/partitions.html)
#rabbitmq_cluster_partition_handling: ignore

rabbitmq_ssl_enable: false
rabbitmq_ssl_port: 5671
rabbitmq_ssl_listeners: []
# - 127.0.0.1
# - "::1"

rabitmq_ssl_options: {}
# cacertfile: '"/path/to/testca/cacert.pem"'
# certfile: '"/path/to/server/cert.pem"'
# keyfile: '"/path/to/server/key.pem"'
# verify: verify_peer
# fail_if_no_peer_cert: "false"

# Define extra vhosts to be created
rabbitmq_extra_vhosts:
  - name: /
    state: present
  - name: /bussiness
    state: present
  - name: /webmqtt
    state: present

rabbitmq_extra_vhosts_SZ_MKTAPI:
  - name: /
    state: present
  - name: /webmqtt
    state: present

rabbitmq_extra_vhosts_HK_MG:
  - name: /
    state: present

rabbitmq_plugins: "rabbitmq_event_exchange,rabbitmq_stream,rabbitmq_management,rabbitmq_prometheus,rabbitmq_recent_history_exchange,rabbitmq_shovel_management,rabbitmq_top,rabbitmq_web_mqtt_examples,rabbitmq_tracing,rabbitmq_top"

rabbitmq_plugins_HK_MG: "rabbitmq_event_exchange,rabbitmq_stream,rabbitmq_management,rabbitmq_prometheus,rabbitmq_recent_history_exchange,rabbitmq_shovel_management,rabbitmq_top,rabbitmq_tracing,rabbitmq_top"

# Define admin user to create in order to login to WebUI
#rabbitmq_users:
#config in vault.vars.yml

