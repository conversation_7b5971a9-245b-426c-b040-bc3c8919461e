{{ansible_managed|comment}}
http:
  middlewares:
    hkPx001OmsIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    oms.igoldhorse.com:
      service: hkPx001CaddyOms
      rule: Host(`oms.igoldhorse.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
  services:
    hkPx001CaddyOms:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
