{{ansible_managed|comment}}
http:
  routers:
    cdn.static.igoldhorse.cn:
      service: hkPx001CaddyCdnStatic
      rule: Host(`cdn.static.igoldhorse.cn`,`cdn.static.igoldhorse.com`,`download.igoldhorse.cn`,`download.igoldhorse.com`)
      middlewares:
      - gzip
      - aaoHeader
      tls:
        certResolver: httpResolver
        domains:
        - main: cdn.static.igoldhorse.cn
        - main: cdn.static.igoldhorse.com
        - main: download.igoldhorse.cn
        - main: download.igoldhorse.com
  services:
    hkPx001CaddyCdnStatic:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
