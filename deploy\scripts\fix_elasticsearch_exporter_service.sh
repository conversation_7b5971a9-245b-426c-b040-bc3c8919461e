#!/bin/bash

# Elasticsearch Exporter服务修复脚本
# 修复systemd服务文件中的转义序列问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 检查服务状态
check_service_status() {
    log_header "检查Elasticsearch Exporter服务状态"
    
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        log_info "检查 $host..."
        
        # 检查服务状态
        local status=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-active elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
        local enabled=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-enabled elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "disabled")
        
        echo "  状态: $status"
        echo "  开机启动: $enabled"
        
        # 检查服务文件错误
        local service_errors=$(ansible -i hosts.ini "$host" -m shell -a "journalctl -u elasticsearch_exporter --since '1 hour ago' | grep 'Ignoring unknown escape sequences'" --become 2>/dev/null | grep -v "SUCCESS" | wc -l || echo "0")
        
        if [ "$service_errors" -gt 0 ]; then
            log_warn "  发现 $service_errors 个转义序列错误"
        else
            log_info "  无转义序列错误"
        fi
        
        echo ""
    done
}

# 修复服务文件
fix_service_file() {
    log_header "修复Elasticsearch Exporter服务文件"
    
    # 重新部署修复后的配置
    log_info "重新部署Elasticsearch Exporter配置..."
    ansible-playbook -i hosts.ini 2029.elasticsearch_exporter.playbook.yml --tags config
    
    if [ $? -eq 0 ]; then
        log_info "配置部署成功"
    else
        log_error "配置部署失败"
        return 1
    fi
}

# 重启服务
restart_service() {
    log_header "重启Elasticsearch Exporter服务"
    
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        log_info "重启 $host 上的服务..."
        
        # 停止服务
        ansible -i hosts.ini "$host" -m systemd -a "name=elasticsearch_exporter state=stopped" --become
        
        # 重载systemd配置
        ansible -i hosts.ini "$host" -m systemd -a "daemon_reload=yes" --become
        
        # 启动服务
        ansible -i hosts.ini "$host" -m systemd -a "name=elasticsearch_exporter state=started enabled=yes" --become
        
        # 等待服务启动
        sleep 5
        
        # 检查服务状态
        local status=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-active elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
        
        if [ "$status" = "active" ]; then
            log_info "  ✓ $host 服务启动成功"
        else
            log_error "  ✗ $host 服务启动失败"
            
            # 显示错误日志
            log_info "  错误日志:"
            ansible -i hosts.ini "$host" -m shell -a "journalctl -u elasticsearch_exporter --no-pager -n 10" --become
        fi
        
        echo ""
    done
}

# 验证修复结果
verify_fix() {
    log_header "验证修复结果"
    
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        log_info "验证 $host..."
        
        # 检查服务状态
        local status=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-active elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
        
        if [ "$status" = "active" ]; then
            log_info "  ✓ 服务运行正常"
            
            # 检查指标端点
            local metrics_check=$(ansible -i hosts.ini "$host" -m uri -a "url=http://localhost:9114/metrics" 2>/dev/null | grep -c "status.*200" || echo "0")
            
            if [ "$metrics_check" -gt 0 ]; then
                log_info "  ✓ 指标端点正常"
            else
                log_warn "  ⚠ 指标端点异常"
            fi
            
            # 检查是否还有转义序列错误
            local recent_errors=$(ansible -i hosts.ini "$host" -m shell -a "journalctl -u elasticsearch_exporter --since '5 minutes ago' | grep 'Ignoring unknown escape sequences'" --become 2>/dev/null | grep -v "SUCCESS" | wc -l || echo "0")
            
            if [ "$recent_errors" -eq 0 ]; then
                log_info "  ✓ 无转义序列错误"
            else
                log_warn "  ⚠ 仍有转义序列错误"
            fi
        else
            log_error "  ✗ 服务未运行"
        fi
        
        echo ""
    done
}

# 显示服务配置
show_service_config() {
    log_header "当前服务配置"
    
    log_info "查看修复后的服务文件内容..."
    
    # 显示一个主机的服务文件内容
    ansible -i hosts.ini "SZ-ES-001" -m shell -a "cat /etc/systemd/system/elasticsearch_exporter.service" --become 2>/dev/null | grep -v "SUCCESS" || log_warn "无法读取服务文件"
}

# 生成诊断报告
generate_diagnostic_report() {
    log_header "生成诊断报告"
    
    local report_file="/tmp/elasticsearch_exporter_diagnostic.txt"
    
    cat > "$report_file" << EOF
Elasticsearch Exporter服务诊断报告
生成时间: $(date)

问题描述:
systemd服务文件中的反斜杠转义序列导致警告信息，影响服务启动。

错误信息:
/etc/systemd/system/elasticsearch_exporter.service:11: Ignoring unknown escape sequences: "\ "

修复方案:
1. 移除ExecStart中的多行反斜杠续行符
2. 将所有参数合并到一行
3. 重新部署服务配置
4. 重启服务

修复步骤:
1. 更新服务模板文件
2. 重新部署配置
3. 重载systemd配置
4. 重启服务
5. 验证修复结果

服务状态检查:
EOF

    # 添加服务状态信息
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        echo "" >> "$report_file"
        echo "$host:" >> "$report_file"
        
        local status=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-active elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
        local enabled=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-enabled elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "disabled")
        
        echo "  状态: $status" >> "$report_file"
        echo "  开机启动: $enabled" >> "$report_file"
        
        if [ "$status" = "active" ]; then
            local metrics_check=$(ansible -i hosts.ini "$host" -m uri -a "url=http://localhost:9114/metrics" 2>/dev/null | grep -c "status.*200" || echo "0")
            if [ "$metrics_check" -gt 0 ]; then
                echo "  指标端点: 正常" >> "$report_file"
            else
                echo "  指标端点: 异常" >> "$report_file"
            fi
        fi
    done
    
    echo "" >> "$report_file"
    echo "建议:" >> "$report_file"
    echo "1. 定期检查服务状态" >> "$report_file"
    echo "2. 监控指标采集是否正常" >> "$report_file"
    echo "3. 关注systemd日志中的错误信息" >> "$report_file"
    
    log_info "诊断报告已生成: $report_file"
    cat "$report_file"
}

# 主函数
main() {
    echo "========================================"
    echo "Elasticsearch Exporter服务修复"
    echo "修复时间: $(date)"
    echo "========================================"
    echo ""
    
    # 检查前置条件
    if [ ! -f "hosts.ini" ]; then
        log_error "hosts.ini 文件不存在"
        exit 1
    fi
    
    if ! command -v ansible-playbook &> /dev/null; then
        log_error "ansible-playbook 未安装"
        exit 1
    fi
    
    # 执行修复步骤
    check_service_status
    fix_service_file
    restart_service
    verify_fix
    show_service_config
    generate_diagnostic_report
    
    log_info "Elasticsearch Exporter服务修复完成！"
    echo ""
    echo "验证命令:"
    echo "  # 检查服务状态"
    echo "  ansible -i hosts.ini 'SZ-ES-001' -m shell -a 'systemctl status elasticsearch_exporter' --become"
    echo ""
    echo "  # 检查指标"
    echo "  curl http://SZ-ES-001:9114/metrics"
    echo ""
    echo "  # 检查日志"
    echo "  ansible -i hosts.ini 'SZ-ES-001' -m shell -a 'journalctl -u elasticsearch_exporter -f' --become"
}

# 执行主函数
main "$@"
