# GH 生产环境标准化

## 使用 *ansible playbook* 部署服务器

1. [可选] 设置目标机 *sudo* 密码到环境变量 *ANSIBLE_BECOME_PASS* 中，可以方便以后的操作

    ```shell
    > export ANSIBLE_BECOME_PASS=<SUDOPASSWORD>
    ...
    ```

2. 启动 *ssh-agent* 并添加 *ssh* 私钥

    ```shell
    > eval $(ssh_agent)
    Agent pid 28914
    > ssh-add -D
    All identities removed.
    > ssh-add <ID_ED25519_FILE>
    Identity added: .ssh/id_ed25519
    ...
    ```

3. 运行 `ansible-playbook`， 带上合适的参数和环境变量

    ```shell
    > ansible-playbook playbook.yml \
        -i hosts.ini \
        -e @../vault.vars.yml \
        -e DEPLOY_PROJECT_HOSTS=<DEPLOYHOST/GROUP> \
        -u <SERVER_USERNAME> \
        -b
    ```

    | 参数 | 备注                       |
    | ---- | -------------------------- |
    | `-i` | 指定 `inventory` 文件      |
    | `-e` | 给 *playbook* 中的变量赋值 |
    | `-u` | 远程用户名                 |
    | `-b` | 是否使用 *sudo*             |

4. 运行完成后可以清理环境

    ```shell
    > ssh-agent -k
    unset SSH_AUTH_SOCK;
    unset SSH_AGENT_PID;
    echo Agent pid 28914 killed;
    ...
    ```

## 开发和测试 *ansible playbook*

进入 `vagrant` 文件夹，启动虚拟机环境。

```shell
> cd vagrant
> vagrant up
Bringing machine 'centos' up with 'libvirt' provider...
Bringing machine 'ubuntu' up with 'libvirt' provider...
...
```

第一次启动虚拟机环境会运行设置好的 `ansible-playbook`, 运行的结果应该没有错误。

修改 `ansible-playbook` 后，可以单独运行 `provision`，节省初始化虚拟机的时间。

```shell
> vagrant provision
==> ubuntu: Running provisioner: ansible...
    ubuntu: Running ansible-playbook...
...
```

完成 `ansible-playbook` 开发后，应该销毁虚拟机。保存有一次干净的虚拟机环境测试无误。

```shell
> vagrant destroy -f
==> ubuntu: Removing domain...
==> ubuntu: Deleting the machine folder
...
```

## 用户

用户名|用途|sudo|备注|
-|-|-|-
`ghci` | 只在 CI/CD 上使用| Y/full |
`ghops` | 用于日常运维操作| Y/limit | 只能 sudo 使用一部份命令
`ghroot` | 结对运维时可以使用 | Y/full |

## 01.init_user

初始化用户 pipeline 的 job 需要几个环境变量

1. `SSHPASS`
1. `ANSIBLE_BECOME_PASS`
1. `ANSIBLE_USER`