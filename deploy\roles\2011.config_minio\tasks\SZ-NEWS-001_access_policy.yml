- name: add minio user
  shell:
    cmd: |
      mc mb -p server/{{item.bucket}}
      mc admin user add server/ "{{item.user}}" "{{item.pass}}"
      mc admin policy add server/ policy_{{item.policy}}_{{item.user}} /tmp/policy_{{item.policy}}_{{item.user}}.json
      mc admin policy set server/ policy_{{item.policy}}_{{item.user}} user={{item.user}}
  with_items:
  - "{{minio_users[ansible_hostname]}}"

- name: add access policy
  shell:
    cmd: |
      mc policy set {{item.access}} server/{{item.bucket}}
  with_items:
  - "{{minio_users[ansible_hostname]}}"