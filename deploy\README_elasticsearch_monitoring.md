# Elasticsearch 集群监控部署指南

## 概述

本文档描述如何为Elasticsearch自建集群设置完整的监控解决方案，包括：
- Elasticsearch Exporter 部署
- Prometheus 指标采集配置
- Grafana 仪表板
- 告警规则配置
- Blackbox 健康检查

## Elasticsearch 集群信息

- 节点1: http://*************:9200
- 节点2: http://*************:9200  
- 节点3: http://*************:9200

## 部署步骤

### 1. 部署 Elasticsearch Exporter

```bash
# 部署到所有监控服务器
ansible-playbook -i hosts.ini 2029.elasticsearch_exporter.playbook.yml

# 或者指定特定主机
ansible-playbook -i hosts.ini 2029.elasticsearch_exporter.playbook.yml -e "DEPLOY_ELASTICSEARCH_EXPORTER_HOSTS=SZ-ES-001"
```

### 2. 更新 Prometheus 配置

Prometheus 会自动通过 file_sd 发现 elasticsearch_exporter 目标：
- 端口: 9114
- 路径: /metrics

### 3. 验证部署

```bash
# 检查 elasticsearch_exporter 服务状态
systemctl status elasticsearch_exporter

# 检查指标端点
curl http://localhost:9114/metrics

# 验证 ES 集群连接
curl http://*************:9200/_cluster/health
```

## 监控指标

### 集群级别指标
- `elasticsearch_cluster_health_status` - 集群健康状态
- `elasticsearch_cluster_health_number_of_nodes` - 节点数量
- `elasticsearch_cluster_health_active_shards` - 活跃分片数
- `elasticsearch_cluster_health_unassigned_shards` - 未分配分片数

### 节点级别指标
- `elasticsearch_node_stats_jvm_mem_heap_used_percent` - JVM堆内存使用率
- `elasticsearch_filesystem_data_available_bytes` - 可用磁盘空间
- `elasticsearch_node_stats_indices_docs` - 文档数量

### 索引级别指标
- `elasticsearch_indices_docs` - 索引文档数
- `elasticsearch_indices_store_size_bytes` - 索引存储大小

## 告警规则

已配置的告警规则包括：

### 关键告警 (Critical)
- ElasticsearchClusterRed - 集群状态为红色
- ElasticsearchNodeDown - 节点下线
- ElasticsearchDiskOutOfSpace - 磁盘空间不足(<10%)
- ElasticsearchClusterUnassignedShards - 存在未分配分片
- ElasticsearchTooFewNodesRunning - 运行节点数少于3个
- ElasticsearchHeapUsageTooHigh - 堆内存使用率>90%

### 警告告警 (Warning)
- ElasticsearchClusterYellow - 集群状态为黄色
- ElasticsearchDiskSpaceLow - 磁盘空间低(<20%)
- ElasticsearchHeapUsageWarning - 堆内存使用率>80%
- ElasticsearchClusterPendingTasks - 存在待处理任务

## 配置文件位置

- Exporter 配置: `/etc/elasticsearch_exporter/`
- Systemd 服务: `/etc/systemd/system/elasticsearch_exporter.service`
- Prometheus 规则: `/etc/prometheus/rule.d/elasticsearch.rule.yml`
- File SD 配置: `/etc/prometheus/file_sd.d/elasticsearch.yml`

## 故障排除

### 常见问题

1. **Exporter 无法连接到 ES 集群**
   ```bash
   # 检查网络连通性
   telnet ************* 9200
   
   # 检查 ES 集群状态
   curl http://*************:9200/_cluster/health
   ```

2. **指标采集失败**
   ```bash
   # 查看 exporter 日志
   journalctl -u elasticsearch_exporter -f
   
   # 检查 Prometheus targets
   # 访问 http://prometheus:9090/targets
   ```

3. **告警不触发**
   ```bash
   # 检查 Prometheus 规则
   promtool check rules /etc/prometheus/rule.d/elasticsearch.rule.yml
   
   # 重载 Prometheus 配置
   curl -X POST http://localhost:9090/-/reload
   ```

## 性能调优

### Exporter 配置优化
- 调整采集间隔: `--es.timeout=10s`
- 禁用不需要的指标: `--es.snapshots=false`
- 限制索引监控: `--es.indices_settings=false`

### Prometheus 配置优化
- 调整 scrape_interval: `30s`
- 设置合适的 scrape_timeout: `10s`

## 扩展监控

### 添加自定义指标
可以通过修改 `elasticsearch.rule.yml` 添加业务相关的告警规则。

### 集成 Grafana 仪表板
推荐使用官方 Elasticsearch 仪表板模板：
- Dashboard ID: 266 (Elasticsearch Overview)
- Dashboard ID: 2322 (Elasticsearch Cluster)

## 维护

### 定期检查
- 每周检查告警规则有效性
- 每月检查磁盘空间使用趋势
- 每季度评估监控指标的完整性

### 备份
- 定期备份 Prometheus 数据
- 备份监控配置文件
