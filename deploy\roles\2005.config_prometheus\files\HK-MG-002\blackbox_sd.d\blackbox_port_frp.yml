- labels:
    instance: frps_serverport_szopacc
    job: port_status
    type: probe
  targets:
  - HK-MG-002:55555
#- labels:
#    instance: frps_quot_common_szcal
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:8100
#- labels:
#    instance: frps_quot_option_szcal
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:8101
#- labels:
#    instance: frps_stocklist_szcal
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:8102
#- labels:
#    instance: frps_mqtt-websocket_szpush
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:15675
#- labels:
#    instance: frps_mqtt_szpush
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:1883
#- labels:
#    instance: frps_amqp_szpush
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:56720
#- labels:
#    instance: frps_quot_common_a_szcsc001
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:8020
#- labels:
#    instance: frps_quot_common_hk_szcal
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:8021
#- labels:
#    instance: frps_quot_common_us_szcal
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:8022
#- labels:
#    instance: frps_quot_mgr_szcal
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:8031
#- labels:
#    instance: frps_gateway_szcal
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:8000
#- labels:
#    instance: frps_user_center_sznews
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:8220
#- labels:
#    instance: frps_rsync_gh81
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:11873
#- labels:
#    instance: frps_ldap
#    job: port_status
#    type: probe
#  targets:
#  - HK-MG-002:11389
