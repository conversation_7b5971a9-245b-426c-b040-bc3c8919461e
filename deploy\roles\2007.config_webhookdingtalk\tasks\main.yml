- name: configure webhookdingtalk.yml
  template:
    src: webhookdingtalk.yml.j2
    dest: "{{WEBHOOKDINGTALK_CONFIG_DIR}}/webhookdingtalk.yml"
    force: true
    owner: root
    group: webhookdingtalk
    mode: '640'
  notify:
  - reload webhookdingtalk
- name: configure templates/alertmanager-dingtalk.tmpl
  template:
    src: alertmanager-dingtalk.tmpl.j2
    dest: "{{WEBHOOKDINGTALK_TEMPL_DIR}}/alertmanager-dingtalk.tmpl"
    force: true
    owner: root
    group: webhookdingtalk
    mode: '640'
  notify:
  - reload webhookdingtalk
