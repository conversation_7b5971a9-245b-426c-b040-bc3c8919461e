- name: Ensure a redis job that runs 
  cron:
    name: "redis_cron"
    job: "{{redis_cron_inline}}"
    day: "*/2"
    hour: 18
    minute: 10
- name: install shc
  apt:
    name: shc
    state: present
- name: Ensure  directories
  file:
    path: "/data/mysqlbackup"
    state: directory
    mode: 0750
    recurse: yes
- name: Ensure  directories
  file:
    path: "/home/<USER>"
    state: directory
    mode: 0750
    recurse: yes
- name: create backup_mysql.sh
  template:
    src: backup_mysql.sh.j2
    dest: /home/<USER>/backup_mysql.sh
    mode: 0750
- name: shc files
  shell: "shc -r -f /home/<USER>/backup_mysql.sh" 
- name: "absent some sh file"
  file:
    dest: "/home/<USER>/{{item}}"
    state: absent
  with_items:
  - backup_mysql.sh
  - backup_mysql.sh.x.c
- name: Ensure a  mydumper backup job that runs 
  cron:
    name: "mydumper_cron"
    job: "{{mydumper_cron_inline}}"
    day: "*/1"
    hour: "8,12,16,20"
    minute: 30

- name: create sync_openacc_data.sh
  template:
    src: sync_openacc_data.sh.j2
    dest: /data/sync_openacc_data.sh
    mode: 755
    
- name: Ensure a rsync openacc job1 that runs 
  cron:
    name: "rsyn_openacc_job1"
    job: "{{openacc_cron_inline1}}"

- name: Ensure a rsync openacc job2 that runs 
  cron:
    name: "rsyn_openacc_job2"
    job: "{{openacc_cron_inline2}}"

- name: create sync_mysqlback_data.sh
  template:
    src: sync_mysqlback_data.sh.j2
    dest: /data/sync_mysqlback_data.sh
    mode: 755
    
- name: Ensure a rsync mysqlbackup job that runs 
  cron:
    name: "rsyn_mysqlbackup_job"
    job: "{{mysqlback_cron_inline}}"

- name: create delete_oldfiles.sh
  template:
    src: delete_oldfiles.sh.j2
    dest: /data/create delete_oldfiles.sh
    mode: 755
    
- name: Ensure a delete_oldfiles job that runs 
  cron:
    name: "delete_oldfiles_job"
    job: "{{delete_oldfiles_cron_inline}}"
    day: "*/1"
    hour: "10,23"
    minute: 1