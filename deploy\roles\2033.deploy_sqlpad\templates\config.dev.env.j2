# config for dev setup
SQLPAD_PORT = 8838
SQLPAD_BASE_URL = "/sqlpad"
SQLPAD_DB_PATH = /data/sqlpad/db
SQLPAD_APP_LOG_LEVEL = debug
SQLPAD_WEB_LOG_LEVEL = debug
SQLPAD_SERVICE_TOKEN_SECRET = secr3t
SQLPAD_DEFAULT_CONNECTION_ID = devdbdriverid123
SQLPAD_PASSPHRASE = "{{passphrase}}"
SQLPAD_ADMIN = "<EMAIL>"
SQLPAD_CONNECTIONS__devdbdriverid123__driver = sqlite
SQLPAD_CONNECTIONS__devdbdriverid123__name = dev connection from config
SQLPAD_CONNECTIONS__devdbdriverid123__filename = "./test/fixtures/sales.sqlite"
SQLPAD_ALLOW_CSV_DOWNLOAD = "true"
# OIDC connect config
# Implementation used okta tutorial for testing/dev
# https://developer.okta.com/blog/2018/05/18/node-authentication-with-passport-and-oidc
# allowed domains not necessary, but is honored for oidc
#
# SQLPAD_ALLOWED_DOMAINS = "gmail.com other.com"
# PUBLIC_URL = "http://localhost:3010"
# SQLPAD_OIDC_CLIENT_ID = CLIENT_ID
# SQLPAD_OIDC_CLIENT_SECRET = SECRET
# SQLPAD_OIDC_ISSUER = "https://dev-350224.okta.com/oauth2/default"
# SQLPAD_OIDC_SCOPE = "openid profile email roles"
# If below are provided, the old passport-openidconnect implementation is used
# If the below are NOT provided, the new openid-client implementation is used
# SQLPAD_OIDC_AUTHORIZATION_URL = "https://dev-350224.okta.com/oauth2/default/v1/authorize"
# SQLPAD_OIDC_TOKEN_URL = "https://dev-350224.okta.com/oauth2/default/v1/token"
# SQLPAD_OIDC_USER_INFO_URL = "https://dev-350224.okta.com/oauth2/default/v1/userinfo"

# LDAP testing config
# This docker image can be used to test LDAP flow
# https://github.com/rroemhild/docker-test-openldap

# Disable local userpass auth
# SQLPAD_USERPASS_AUTH_DISABLED=true
# SQLPAD_LDAP_AUTH_ENABLED=true
# SQLPAD_LDAP_AUTO_SIGN_UP=true
# # Do not allow any login that does not already exist or match RBAC filter
# SQLPAD_LDAP_DEFAULT_ROLE = ""
# # Below the details depend on setup
# SQLPAD_LDAP_URL="ldap://localhost:389"
# SQLPAD_LDAP_BIND_DN="cn=admin,dc=planetexpress,dc=com"
# SQLPAD_LDAP_PASSWORD="GoodNewsEveryone"
# SQLPAD_LDAP_SEARCH_BASE="dc=planetexpress,dc=com"
# # RBAC from LDAP
# # Assuming the docker-test-openldap setup, hermes/hermes becomes an admin, bender/bender becomes an editor
# SQLPAD_LDAP_ROLE_ADMIN_FILTER = "(memberOf=cn=admin_staff,ou=people,dc=planetexpress,dc=com)"
# SQLPAD_LDAP_ROLE_EDITOR_FILTER = "(memberOf=cn=ship_crew,ou=people,dc=planetexpress,dc=com)"
