- name: create nacos system group
  group:
    name: nacos
    system: true
    state: present
    gid: 3250

- name: create nacos system user
  user:
    name: nacos
    system: true
    shell: "/usr/sbin/nologin"
    group: nacos
    createhome: false
    uid: 3250

- name: create nacos  directory
  file:
    path: "/etc/nacos"
    state: directory
    recurse: true
    owner: nacos
    group: nacos
    mode: 0755

- name: downloading nacos
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "{{NACOS_DLOAD_URL}}"
    dest: /etc/nacos/nacos-{{NACOS_VERSION}}.tar.gz
    checksum: '{{NACOS_DLOAD_CHECKSUM}}'
  register: _download_nacos
  until: _download_nacos is succeeded
  retries: 5
  delay: 2

- name: extract nacos tar
  unarchive:
    src: /etc/nacos/nacos-{{NACOS_VERSION}}.tar.gz
    dest: /etc
    owner: nacos
    group: nacos
    mode: 0755
    copy: no

- name: create systemd service unit
  template:
    src: nacos.service.j2
    dest: /etc/systemd/system/nacos.service
    owner: root
    group: root
    mode: 0644
  notify:
  - restart nacos

- name: config startup.sh
  template:
    src: startup.sh.j2
    dest: /etc/nacos/bin/startup.sh
    mode: 0755
  notify:
  - restart nacos

- name: ensuring that the nacos service is running
  service:
    daemon_reload: yes
    enabled: yes
    name: nacos
    state: started
