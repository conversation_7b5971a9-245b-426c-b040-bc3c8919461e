- name: update rsync.secret
  lineinfile:
    path: /etc/rsyncd.secrets
#    regex: '^{{minio_data_user}}:.*'
    create: true
#    line: '{{minio_data_user}}:{{minio_data_pass}}'
    regex: '{{item.regex}}'
    line: '{{item.line}}'
    mode: '600'
  with_items:
  - line: '{{minio_data_user}}:{{minio_data_pass}}'
    regex: '^{{minio_data_user}}:.*'
  - line: '{{minio_fund_data_user}}:{{minio_data_pass}}'
    regex: '^{{minio_fund_data_user}}:.*'

- name: create rsync module
  template:
    src: "{{item}}"
    dest: "/etc/rsyncd.d/{{item|basename|splitext|first}}"
    mode: '640'
  with_fileglob:
  - 'templates/{{ansible_hostname}}/*.conf.j2'
