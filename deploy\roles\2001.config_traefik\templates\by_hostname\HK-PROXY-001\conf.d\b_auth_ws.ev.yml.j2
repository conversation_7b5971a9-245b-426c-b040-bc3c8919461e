{{ansible_managed|comment}}
http:
  middlewares:
    openapiAuth:
      forwardAuth:
        address: http://open-api-auth.lan.ghhk:65432/open-api-auth/auth_api/traffic_authentication
        trustForwardHeader: true
    hkProxy001IpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        #- "***************"
        - "**************"
        - "*************"
        - "*************"
        - "**************"
        # tongyu
        - "*************"
        - "**************"
        - "***************"
        - "*************"
        - "**************"
        - "*************"
        - "*************"
        - "*************"
        - "************"
        - "*************"
        - "**************"
        - "************"
        # tiger
        - "*************"
        - "***************"
        #Easytrade
        - "*************"
        #Lcm
        - "**************"
#    hkProxy001IpWhiteList:
#      ipWhiteList:
#        sourceRange:
#        # TODO: add office ip
#        - "***************"
#        - "*************"
#        - "**************"
#        - "***************"
#        - "*************"
#        - "**************"
  routers:
    mqtt-openapi-int-ws.easyview.com.hk:
      service: hkProxy001AuthWsInt
      rule: Host(`mqtt-openapi-int-ws.easyview.com.hk`) && Path(`/ws`)
      middlewares:
      - hkProxy001IpWhiteList
      - openapiAuth
      tls:
        certResolver: httpResolver
        domains:
        - main: mqtt-openapi-int-ws.easyview.com.hk
        options: modern
tls:
  certificates:
  - certFile: /etc/traefik/ssl/easyview.com.hk_bundle.crt
    keyFile: /etc/traefik/ssl/easyview.com.hk.key

