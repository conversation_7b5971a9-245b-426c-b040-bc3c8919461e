{{ansible_managed|comment}}
http:
  middlewares:
    hkPx001FundriskIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    fundrisk.igoldhorse.com:
      service: hkPx001CaddyFundrisk
      rule: Host(`fundrisk.igoldhorse.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
  services:
    hkPx001CaddyFundrisk:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
