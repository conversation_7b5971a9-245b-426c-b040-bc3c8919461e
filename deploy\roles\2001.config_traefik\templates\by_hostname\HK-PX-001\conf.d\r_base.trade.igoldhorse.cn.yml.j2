{{ansible_managed|comment}}
http:
  routers:
    # dmz
    gqlDmz_base.trade.igoldhorse.cn:
      rule: Host(`base.trade.igoldhorse.cn`, `hk.trade.igoldhorse.cn`,`hk.trade.igoldhorse.com`) && Method(`POST`) && Path(`/dmz/api/v1/gql/`)
      service: gqlDmz
      middlewares:
      - gzip
      - requestAuthDmz
      - bodyLimit
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    gqlDmz_base.trade.igoldhorse.cn_cors:
      rule: Host(`base.trade.igoldhorse.cn`, `hk.trade.igoldhorse.cn`,`hk.trade.igoldhorse.com`) && Method(`OPTIONS`) && Path(`/dmz/api/v1/gql/`)
      service: gqlDmz
      middlewares:
      - gzip
      - bodyLimit
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    # secret
    gqlSecret_base.trade.igoldhorse.cn:
      rule: Host(`base.trade.igoldhorse.cn`, `hk.trade.igoldhorse.cn`,`hk.trade.igoldhorse.com`) && Method(`POST`) && Path(`/secret/api/v1/gql/`)
      service: gqlSecret
      middlewares:
      - gzip
      - requestAuthSecret
      - bodyLimit
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    gqlSecret_base.trade.igoldhorse.cn_cors:
      rule: Host(`base.trade.igoldhorse.cn`, `hk.trade.igoldhorse.cn`,`hk.trade.igoldhorse.com`) && Method(`OPTIONS`) && Path(`/secret/api/v1/gql/`)
      service: gqlSecret
      middlewares:
      - gzip
      - bodyLimit
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    # mqtt
    mqttSecret_sz.quot.igoldhorse.cn:
      rule: Host(`sz.quot.igoldhorse.cn`) && Path(`/ws`)
      service: mqttSecret
      middlewares:
      - requestAuthSecret
      tls:
        certResolver: httpResolver
    mqttNoSecret_sz.quot.igoldhorse.cn:
      rule: Host(`sz.noquot.igoldhorse.cn`) && Path(`/ws`)
      service: mqttSecret
      tls:
        certResolver: httpResolver
