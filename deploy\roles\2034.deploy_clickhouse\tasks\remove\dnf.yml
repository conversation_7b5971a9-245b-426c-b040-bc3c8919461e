---
#Main installation actions
#https://clickhouse.yandex/docs/en/getting_started/index.html#installation

- name: Uninstall by YUM | Ensure clickhouse package uninstalled
  dnf:
    name: "{{ clickhouse_package }}"
    state: absent
    autoremove: yes
  become: true

- name: Uninstall by YUM | Ensure clickhouse repo uninstalled
  yum_repository:
    name: clickhouse
    file: clickhouse
    state: absent
  become: true

- name: Uninstall by YUM | Ensure clickhouse key uninstalled
  rpm_key:
    key: "{{ clickhouse_repo_key }}"
    state: absent
  become: true
