---
- name: create minio system group
  group:
    name: minio
    system: true
    state: present
    gid: '{{MINIO_GID}}'

- name: create data directory
  file:
    path: /data
    state: directory
    owner: root
    group: root
    mode: 0755

- name: create minio system user
  user:
    name: minio
    system: true
    shell: "/usr/sbin/nologin"
    group: minio
    groups:
    - wukong
    createhome: false
    home: "{{ MINIO_DATA_DIR }}"
    uid: '{{MINIO_UID}}'

- name: create minio data directory
  file:
    path: "{{ MINIO_DATA_DIR }}"
    state: directory
    recurse: true
    owner: minio
    group: minio
    mode: 0755

- name: minio url
  debug:
    msg: |
      minio: "{{MINIO_DOWNLOAD_URL}}"
      mc: "{{MC_DOWNLOAD_URL}}"

- name: download minio server binary to local folder
  become: false
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "{{MINIO_DOWNLOAD_URL}}"
    dest: "/tmp/{{MINIO_FILE_NAME}}"
    checksum: '{{MINIO_CHECKSUM}}'
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: download mc client binary to local folder
  become: false
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "{{MC_DOWNLOAD_URL}}"
    dest: "/tmp/{{MC_FILE_NAME}}"
    checksum: '{{MC_CHECKSUM}}'
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: install minio/mc binary
  copy:
    src: "/tmp/{{item.src}}"
    dest: "/usr/local/bin/{{item.dest}}"
    remote_src: true
    mode: '755'
  with_items:
  - src: "{{MINIO_FILE_NAME}}"
    dest: minio
  - src: "{{MC_FILE_NAME}}"
    dest: mc

- name: copy minio server systemd service
  template:
    src: minio.service.j2
    dest: /etc/systemd/system/minio.service
    owner: root
    group: root
    mode: '744'
  notify:
  - restart minio

- name: enabled minio service
  systemd:
    name: minio
    daemon_reload: true
    enabled: true
