#!/bin/bash

# Elasticsearch Exporter状态检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 检查单个主机
check_host() {
    local host="$1"
    
    log_info "检查 $host..."
    
    # 检查服务状态
    echo "  服务状态:"
    local status=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-active elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
    local enabled=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-enabled elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "disabled")
    
    echo "    Active: $status"
    echo "    Enabled: $enabled"
    
    if [ "$status" = "active" ]; then
        # 检查进程
        local pid=$(ansible -i hosts.ini "$host" -m shell -a "pgrep -f elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "")
        if [ -n "$pid" ]; then
            echo "    PID: $pid"
        else
            echo "    PID: 未找到进程"
        fi
        
        # 检查端口
        local port_status=$(ansible -i hosts.ini "$host" -m shell -a "netstat -tlnp | grep :9114" --become 2>/dev/null | grep -v "SUCCESS" || echo "")
        if [ -n "$port_status" ]; then
            echo "    端口9114: 监听中"
        else
            echo "    端口9114: 未监听"
        fi
        
        # 检查指标端点
        local metrics_status=$(ansible -i hosts.ini "$host" -m uri -a "url=http://localhost:9114/metrics timeout=5" 2>/dev/null | grep "status" | grep "200" || echo "")
        if [ -n "$metrics_status" ]; then
            echo "    指标端点: 正常"
            
            # 获取一些关键指标
            local up_metric=$(ansible -i hosts.ini "$host" -m shell -a "curl -s http://localhost:9114/metrics | grep 'elasticsearch_up ' | head -1" 2>/dev/null | grep -v "SUCCESS" || echo "")
            if [ -n "$up_metric" ]; then
                echo "    ES连接状态: $up_metric"
            fi
        else
            echo "    指标端点: 异常"
        fi
    else
        # 显示服务失败原因
        echo "  服务失败详情:"
        ansible -i hosts.ini "$host" -m shell -a "systemctl status elasticsearch_exporter --no-pager -l" --become 2>/dev/null | grep -v "SUCCESS" | head -10
        
        echo "  最近日志:"
        ansible -i hosts.ini "$host" -m shell -a "journalctl -u elasticsearch_exporter --no-pager -n 5" --become 2>/dev/null | grep -v "SUCCESS"
    fi
    
    echo ""
}

# 检查Elasticsearch集群
check_elasticsearch_cluster() {
    log_header "检查Elasticsearch集群状态"
    
    local es_nodes=("*************:9200" "*************:9200" "*************:9200")
    
    for node in "${es_nodes[@]}"; do
        echo "检查ES节点: $node"
        
        if curl -s --connect-timeout 5 "http://$node" > /dev/null; then
            echo "  ✓ 节点可达"
            
            # 获取节点信息
            local node_info=$(curl -s "http://$node" 2>/dev/null)
            local node_name=$(echo "$node_info" | jq -r '.name' 2>/dev/null || echo "unknown")
            local version=$(echo "$node_info" | jq -r '.version.number' 2>/dev/null || echo "unknown")
            
            echo "  节点名: $node_name"
            echo "  版本: $version"
            
            # 获取集群健康状态
            local health=$(curl -s "http://$node/_cluster/health" 2>/dev/null)
            local status=$(echo "$health" | jq -r '.status' 2>/dev/null || echo "unknown")
            local nodes=$(echo "$health" | jq -r '.number_of_nodes' 2>/dev/null || echo "unknown")
            
            echo "  集群状态: $status"
            echo "  节点数: $nodes"
        else
            echo "  ✗ 节点不可达"
        fi
        echo ""
    done
}

# 检查Prometheus配置
check_prometheus_config() {
    log_header "检查Prometheus配置"
    
    local prometheus_hosts=("SZ-ES-001:9090" "SZ-MONT-002:9090" "HK-MG-002:9090")
    
    for host in "${prometheus_hosts[@]}"; do
        echo "检查Prometheus: $host"
        
        if curl -s --connect-timeout 5 "http://$host/api/v1/targets" > /dev/null 2>&1; then
            echo "  ✓ Prometheus可达"
            
            # 检查elasticsearch targets
            local targets=$(curl -s "http://$host/api/v1/targets" 2>/dev/null)
            local es_targets=$(echo "$targets" | jq -r '.data.activeTargets[] | select(.labels.job=="elasticsearch") | .scrapeUrl' 2>/dev/null || echo "")
            
            if [ -n "$es_targets" ]; then
                echo "  ✓ 发现elasticsearch targets:"
                echo "$es_targets" | while read target; do
                    echo "    - $target"
                done
            else
                echo "  ⚠ 未发现elasticsearch targets"
            fi
        else
            echo "  ✗ Prometheus不可达"
        fi
        echo ""
    done
}

# 生成状态报告
generate_status_report() {
    log_header "生成状态报告"
    
    local report_file="/tmp/elasticsearch_exporter_status_report.txt"
    
    cat > "$report_file" << EOF
Elasticsearch Exporter状态报告
生成时间: $(date)

=== 服务状态 ===
EOF

    # 添加服务状态
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        echo "" >> "$report_file"
        echo "$host:" >> "$report_file"
        
        local status=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-active elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
        local enabled=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-enabled elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "disabled")
        
        echo "  状态: $status" >> "$report_file"
        echo "  开机启动: $enabled" >> "$report_file"
        
        if [ "$status" = "active" ]; then
            local metrics_check=$(ansible -i hosts.ini "$host" -m uri -a "url=http://localhost:9114/metrics timeout=5" 2>/dev/null | grep -c "status.*200" || echo "0")
            if [ "$metrics_check" -gt 0 ]; then
                echo "  指标端点: 正常" >> "$report_file"
            else
                echo "  指标端点: 异常" >> "$report_file"
            fi
        fi
    done
    
    echo "" >> "$report_file"
    echo "=== Elasticsearch集群状态 ===" >> "$report_file"
    
    local es_nodes=("*************:9200" "*************:9200" "*************:9200")
    local healthy_nodes=0
    
    for node in "${es_nodes[@]}"; do
        echo "" >> "$report_file"
        echo "$node:" >> "$report_file"
        
        if curl -s --connect-timeout 5 "http://$node" > /dev/null; then
            echo "  状态: 可达" >> "$report_file"
            ((healthy_nodes++))
            
            local health=$(curl -s "http://$node/_cluster/health" 2>/dev/null | jq -r '.status' 2>/dev/null || echo "unknown")
            echo "  集群健康: $health" >> "$report_file"
        else
            echo "  状态: 不可达" >> "$report_file"
        fi
    done
    
    echo "" >> "$report_file"
    echo "=== 总结 ===" >> "$report_file"
    echo "健康的ES节点: $healthy_nodes/3" >> "$report_file"
    
    # 检查是否所有服务都正常
    local all_services_ok=true
    for host in "${hosts[@]}"; do
        local status=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-active elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
        if [ "$status" != "active" ]; then
            all_services_ok=false
            break
        fi
    done
    
    if [ "$all_services_ok" = true ] && [ "$healthy_nodes" -eq 3 ]; then
        echo "整体状态: 正常" >> "$report_file"
    else
        echo "整体状态: 异常" >> "$report_file"
        echo "" >> "$report_file"
        echo "建议操作:" >> "$report_file"
        if [ "$all_services_ok" = false ]; then
            echo "1. 重新部署elasticsearch_exporter服务" >> "$report_file"
        fi
        if [ "$healthy_nodes" -lt 3 ]; then
            echo "2. 检查Elasticsearch集群状态" >> "$report_file"
        fi
    fi
    
    log_info "状态报告已生成: $report_file"
    cat "$report_file"
}

# 主函数
main() {
    echo "========================================"
    echo "Elasticsearch Exporter状态检查"
    echo "检查时间: $(date)"
    echo "========================================"
    echo ""
    
    # 检查前置条件
    if [ ! -f "hosts.ini" ]; then
        log_error "hosts.ini 文件不存在"
        exit 1
    fi
    
    if ! command -v ansible &> /dev/null; then
        log_error "ansible 未安装"
        exit 1
    fi
    
    # 执行检查
    log_header "检查Elasticsearch Exporter服务"
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        check_host "$host"
    done
    
    check_elasticsearch_cluster
    check_prometheus_config
    generate_status_report
    
    log_info "状态检查完成！"
}

# 执行主函数
main "$@"
