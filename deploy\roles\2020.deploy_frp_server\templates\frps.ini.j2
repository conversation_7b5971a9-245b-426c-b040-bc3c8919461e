# /etc/frp/fprs.ini

{{ansible_managed|comment}}

[common]
bind_port = {{FRPS_PORT}}
kcp_bind_port = {{FRPS_PORT}}

token = {{FRP_AUTH}}

dashboard_addr = 0.0.0.0
dashboard_port = {{FRPS_PORT+1}}
dashboard_user = {{FRPS_DASHBOARD_USER}}
dashboard_pwd = {{FRPS_DASHBOARD_PASSWORD}}

admin_addr = 0.0.0.0
admin_port = {{FRPS_PORT+2}}
admin_user = {{FRPS_ADMIN_USER}}
admin_pwd = {{FRPS_ADMIN_PASSWORD}}

enable_prometheus = true
