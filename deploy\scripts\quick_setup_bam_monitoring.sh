#!/bin/bash

# BAM API监控快速配置脚本
# 用法: ./quick_setup_bam_monitoring.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 快速配置函数
quick_setup() {
    log_header "BAM API监控快速配置"
    
    # 1. 更新Promtail配置
    log_info "步骤1: 更新Promtail配置..."
    ansible-playbook -i hosts.ini 2003.promtail.playbook.yml --tags config
    
    # 2. 更新Prometheus配置
    log_info "步骤2: 更新Prometheus配置..."
    ansible-playbook -i hosts.ini 2005.prometheus.playbook.yml --tags config
    
    # 3. 重启服务
    log_info "步骤3: 重启相关服务..."
    ansible -i hosts.ini "SZ-ES-001,SZ-MONT-002,HK-MG-002" -m systemd -a "name=promtail state=restarted" --become
    ansible -i hosts.ini "SZ-ES-001,SZ-MONT-002,HK-MG-002" -m systemd -a "name=prometheus state=reloaded" --become
    
    log_info "配置完成！"
}

# 验证配置
verify_setup() {
    log_header "验证配置"
    
    # 检查Promtail指标
    local hosts=("SZ-ES-001:19080" "SZ-MONT-002:19080" "HK-MG-002:19080")
    
    for host in "${hosts[@]}"; do
        log_info "检查 $host..."
        if curl -s --connect-timeout 5 "http://$host/metrics" | grep -q "bam_api"; then
            log_info "  ✓ BAM API指标正常"
        else
            log_warn "  ⚠ 未发现BAM API指标"
        fi
    done
}

# 显示使用说明
show_usage() {
    echo ""
    echo "BAM API监控已配置完成！"
    echo ""
    echo "监控的日志格式:"
    echo "  ResponseResultAdvice.java - request url:[</api/path>] ... use: [<808>]ms"
    echo ""
    echo "监控指标:"
    echo "  - bam_api_response_time_ms: 响应时间直方图"
    echo "  - bam_api_slow_requests_total: 慢请求计数器 (>1000ms)"
    echo ""
    echo "告警规则:"
    echo "  - BAMSlowAPIRequests: 检测到慢请求"
    echo "  - BAMHighSlowAPIRequestRate: 慢请求频率过高"
    echo "  - BAMHighAPIResponseTimeP95: P95响应时间过高"
    echo ""
    echo "Prometheus查询示例:"
    echo "  # 查看慢请求率"
    echo "  rate(promtail_custom_bam_api_slow_requests_total[5m])"
    echo ""
    echo "  # 查看P95响应时间"
    echo "  histogram_quantile(0.95, rate(promtail_custom_bam_api_response_time_ms_bucket[5m]))"
    echo ""
    echo "  # 查看最慢的接口"
    echo "  topk(10, histogram_quantile(0.95, rate(promtail_custom_bam_api_response_time_ms_bucket[5m])) by (api_path))"
    echo ""
    echo "验证命令:"
    echo "  # 检查Promtail指标"
    echo "  curl http://SZ-ES-001:19080/metrics | grep bam_api"
    echo ""
    echo "  # 检查Prometheus规则"
    echo "  curl http://SZ-ES-001:9090/api/v1/rules | grep BAM"
    echo ""
}

# 主函数
main() {
    echo "========================================"
    echo "BAM API监控快速配置"
    echo "配置时间: $(date)"
    echo "========================================"
    echo ""
    
    # 检查前置条件
    if [ ! -f "hosts.ini" ]; then
        log_error "hosts.ini 文件不存在"
        exit 1
    fi
    
    if ! command -v ansible-playbook &> /dev/null; then
        log_error "ansible-playbook 未安装"
        exit 1
    fi
    
    # 执行配置
    quick_setup
    echo ""
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 验证配置
    verify_setup
    echo ""
    
    # 显示使用说明
    show_usage
    
    log_info "BAM API监控配置完成！"
}

# 执行主函数
main "$@"
