[program:rocketmqexporter]
directory = /opt/rocketmq
command = java -jar rocketmq-exporter-0.0.2-SNAPSHOT.jar >> /data/logs/rocketmqexporter.out
autorestart = true
startretries = 100
user = rocketmq
killasgroup = true
stopasgroup = true
startsecs = 10
environment = LANG="en_US.UTF-8"
priority=999
stdout_logfile_maxbytes = 100MB
stdout_logfile = /var/log/supervisor/rocketmqexporter.log
stdout_logfile_backups = 1
stderr_logfile_maxbytes = 100MB
stderr_logfile = /var/log/supervisor/rocketmqexporter.error.log
stderr_logfile_backups = 1
redirect_stderr = true