image: register.vpn.fwdev.ltd/willhallonline/ansible:2.12-alpine-3.14

include:
- /normalize.gitlab-ci.yml
- /system_service.gitlab-ci.yml
- /service_restart.gitlab-ci.yml

stages:
- normalize
- system_service
- monitoring and logging
- service_restart

variables:
  GITLAB_PROJECT:
    description: gitlab 项目地址， https://gitserver/<GITLAB_PROJECT>.git
  DEPLOY_BOT_SSH_PRIVATE_KEY:
    description: 连接部署机器的私钥, base64 格式
  ANSIBLE_BECOME_PASS:
    description: 服务器上 sudo 的密码
  ANSIBLE_SSH_PASS:
    description: 服务器 SSH 连接密码
  ANSIBLE_USER:
    description: ansible 连接用户名
  ANSIBLE_PORT:
    description: ansible 连接端口
  GITLAB_SSH_IP:
    description: GITLAB 服务器 IP
