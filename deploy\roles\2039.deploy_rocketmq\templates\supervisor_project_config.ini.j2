[program:rocketmq]
directory = /opt/rocketmq/bin
command = sh mqnamesrv
autorestart = true
startretries = 100
user = rocketmq
killasgroup = true
stopasgroup = true
startsecs = 10
environment = LANG="en_US.UTF-8"
priority=777
stdout_logfile_maxbytes = 100MB
stdout_logfile = /var/log/supervisor/rocketmq.log
stdout_logfile_backups = 1
stderr_logfile_maxbytes = 100MB
stderr_logfile = /var/log/supervisor/rocketmq.error.log
stderr_logfile_backups = 1
redirect_stderr = true

[program:rocketmqbrk]
directory = /opt/rocketmq/bin
command = sh mqbroker -c ../conf/broker.conf
autorestart = true
startretries = 100
user = rocketmq
killasgroup = true
stopasgroup = true
startsecs = 10
environment = LANG="en_US.UTF-8"
priority=888
stdout_logfile_maxbytes = 100MB
stdout_logfile = /var/log/supervisor/rocketmqbrk.log
stdout_logfile_backups = 1
stderr_logfile_maxbytes = 100MB
stderr_logfile = /var/log/supervisor/rocketmqbrk.error.log
stderr_logfile_backups = 1
redirect_stderr = true

[program:rocketmqdashb]
directory = /opt/rocketmq
command = java -jar rocketmq-dashboard-1.0.1-SNAPSHOT.jar >> /data/logs/rocketmq-dashboard/rocketmq-dashboard.out
autorestart = true
startretries = 100
user = rocketmq
killasgroup = true
stopasgroup = true
startsecs = 10
environment = LANG="en_US.UTF-8"
priority=999
stdout_logfile_maxbytes = 100MB
stdout_logfile = /var/log/supervisor/rocketmqdashb.log
stdout_logfile_backups = 1
stderr_logfile_maxbytes = 100MB
stderr_logfile = /var/log/supervisor/rocketmqdashb.error.log
stderr_logfile_backups = 1
redirect_stderr = true