redis_cron_inline: redis-cli -c -p 63790 set qaq "`date`"
mydumper_cron_inline: flock -n /tmp/backup_mysql.lock /home/<USER>/backup_mysql.sh.x
openacc_cron_inline1: flock -n /tmp/sync_openacc_data.lock /data/sync_openacc_data.sh
openacc_cron_inline2: sleep 30;flock -n /tmp/sync_openacc_data.lock /data/sync_openacc_data.sh
mysqlback_cron_inline: flock -n /tmp/sync_mysqlback_data.lock /data/sync_mysqlback_data.sh
delete_oldfiles_cron_inline: flock -n /tmp/delete_oldlogs.lock /data/delete_oldfiles.sh

