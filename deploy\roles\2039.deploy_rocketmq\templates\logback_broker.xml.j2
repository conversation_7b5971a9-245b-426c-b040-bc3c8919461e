<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<configuration>
    <appender name="DefaultAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/rocketmq/${brokerLogDir}/broker_default.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/data/logs/rocketmq/otherdays/${brokerLogDir}/broker_default.%i.log.gz</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>100MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>

    <appender name="RocketmqBrokerAppender_inner"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/rocketmq/${brokerLogDir}/broker.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/data/logs/rocketmq/otherdays/${brokerLogDir}/broker.%i.log.gz</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>20</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>128MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <appender name="RocketmqBrokerAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqBrokerAppender_inner"/>
    </appender>

    <appender name="RocketmqProtectionAppender_inner"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/rocketmq/${brokerLogDir}/protection.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/data/logs/rocketmq/otherdays/${brokerLogDir}/protection.%i.log.gz</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>100MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <appender name="RocketmqProtectionAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqProtectionAppender_inner"/>
    </appender>

    <appender name="RocketmqWaterMarkAppender_inner"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/rocketmq/${brokerLogDir}/watermark.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/data/logs/rocketmq/otherdays/${brokerLogDir}/watermark.%i.log.gz</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>100MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <appender name="RocketmqWaterMarkAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqWaterMarkAppender_inner"/>
    </appender>

    <appender name="RocketmqStoreAppender_inner"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/rocketmq/${brokerLogDir}/store.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/data/logs/rocketmq/otherdays/${brokerLogDir}/store.%i.log.gz</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>128MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <appender name="RocketmqStoreAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqStoreAppender_inner"/>
    </appender>

    <appender name="RocketmqRemotingAppender_inner"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/rocketmq/${brokerLogDir}/remoting.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/data/logs/rocketmq/otherdays/${brokerLogDir}/remoting.%i.log.gz</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>100MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <appender name="RocketmqRemotingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqRemotingAppender_inner"/>
    </appender>

    <appender name="RocketmqStoreErrorAppender_inner"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/rocketmq/${brokerLogDir}/storeerror.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/data/logs/rocketmq/otherdays/${brokerLogDir}/storeerror.%i.log.gz</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>100MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <appender name="RocketmqStoreErrorAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqStoreErrorAppender_inner"/>
    </appender>


    <appender name="RocketmqTransactionAppender_inner"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/rocketmq/${brokerLogDir}/transaction.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/data/logs/rocketmq/otherdays/${brokerLogDir}/transaction.%i.log.gz</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>100MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <appender name="RocketmqTransactionAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqTransactionAppender_inner"/>
    </appender>

    <appender name="RocketmqRebalanceLockAppender_inner"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/rocketmq/${brokerLogDir}/lock.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/data/logs/rocketmq/otherdays/${brokerLogDir}/lock.%i.log.gz</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>5</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>100MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <appender name="RocketmqRebalanceLockAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqRebalanceLockAppender_inner"/>
    </appender>

    <appender name="RocketmqFilterAppender_inner"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/rocketmq/${brokerLogDir}/filter.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/data/logs/rocketmq/otherdays/${brokerLogDir}/filter.%i.log.gz</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>100MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <appender name="RocketmqFilterAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqFilterAppender_inner"/>
    </appender>

    <appender name="RocketmqStatsAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/rocketmq/${brokerLogDir}/stats.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/data/logs/rocketmq/otherdays/${brokerLogDir}/stats.%i.log.gz</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>5</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>100MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>

    <appender name="RocketmqCommercialAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/rocketmq/${brokerLogDir}/commercial.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/data/logs/rocketmq/otherdays/${brokerLogDir}/commercial.%i.log.gz</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>500MB</maxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="RocketmqPopAppender_inner"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/rocketmq/pop.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>/data/logs/rocketmq/otherdays/pop.%i.log
            </fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>20</maxIndex>
        </rollingPolicy>
        <triggeringPolicy
            class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>128MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>

    <appender name="RocketmqPopAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqPopAppender_inner"/>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <append>true</append>
        <encoder>
            <pattern>%d{yyy-MM-dd HH\:mm\:ss,GMT+8} %p %t - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>

    <logger name="RocketmqBroker" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="RocketmqBrokerAppender"/>
    </logger>

    <logger name="RocketmqProtection" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="RocketmqProtectionAppender"/>
    </logger>

    <logger name="RocketmqWaterMark" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="RocketmqWaterMarkAppender"/>
    </logger>

    <logger name="RocketmqCommon" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="RocketmqBrokerAppender"/>
    </logger>

    <logger name="RocketmqStore" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="RocketmqStoreAppender"/>
    </logger>

    <logger name="RocketmqStoreError" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="RocketmqStoreErrorAppender"/>
    </logger>

    <logger name="RocketmqTransaction" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="RocketmqTransactionAppender"/>
    </logger>

    <logger name="RocketmqRebalanceLock" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="RocketmqRebalanceLockAppender"/>
    </logger>

    <logger name="RocketmqRemoting" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="RocketmqRemotingAppender"/>
    </logger>

    <logger name="RocketmqStats" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="RocketmqStatsAppender"/>
    </logger>

    <logger name="RocketmqCommercial" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="RocketmqCommercialAppender"/>
    </logger>

    <logger name="RocketmqFilter" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="RocketmqFilterAppender"/>
    </logger>

    <logger name="RocketmqConsole" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="RocketmqPop" additivity="false">
        <level value="INFO" />
        <appender-ref ref="RocketmqPopAppender" />
    </logger>

    <root>
        <level value="INFO"/>
        <appender-ref ref="DefaultAppender"/>
    </root>
</configuration>
