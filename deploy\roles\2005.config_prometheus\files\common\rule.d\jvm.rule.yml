# severity按严重程度由高到低：critical、warning
groups:
  - name: jvm-alerting
    rules:
  #  # down了超过1分钟
  #  - alert: instance-down
  #    expr: up == 0
  #    for: 1m
  #    labels:
  #      severity: warning
  #    annotations:
  #      summary: "Instance {{ $labels.instance }} down"
  #      description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minutes."
#
    # down了超过1分钟
    - alert: instance-down
      expr: up == 0
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "Instance {{ $labels.instance }} down"
        description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 5 minutes."

    # 堆空间使用超过85%
    - alert: heap-usage-too-much
      expr: jvm_memory_used_bytes{job="jvm", area="heap"} / jvm_memory_max_bytes * 100 > 85
      for: 1m
      labels:
        severity: warning
      annotations:
        summary: "JVM Instance {{ $labels.instance }} memory usage > 85%"
        description: "{{ $labels.instance }} of job {{ $labels.job }} has been in status [heap usage > 85%] for more than 1 minutes. current usage ({{ $value }}%)"
    
    # 堆空间使用超过90%
    - alert: heap-usage-too-much
      expr: jvm_memory_used_bytes{job="jvm", area="heap"} / jvm_memory_max_bytes * 100 > 90
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "JVM Instance {{ $labels.instance }} memory usage > 90%"
        description: "{{ $labels.instance }} of job {{ $labels.job }} has been in status [heap usage > 90%] for more than 1 minutes. current usage ({{ $value }}%)"

  # # 在5分钟里，Old GC花费时间超过50%        
  # - alert: old-gc-time-too-much
  #   expr: increase(jvm_gc_pause_seconds_sum{action="end of major GC"}[5m]) > 5 * 60 * 0.5
  #   for: 5m
  #   labels:
  #     severity: warning
  #   annotations:
  #     summary: "JVM Instance {{ $labels.instance }} Old GC time > 50% running time"
  #     description: "{{ $labels.instance }} of job {{ $labels.job }} has been in status [Old GC time > 50% running time] for more than 5 minutes. current seconds ({{ $value }}%)"

  # # 在5分钟里，Old GC花费时间超过80%
  # - alert: old-gc-time-too-much
  #   expr: increase(jvm_gc_pause_seconds_sum{action="end of major GC"}[5m]) > 5 * 60 * 0.8
  #   for: 5m
  #   labels:
  #     severity: critical
  #   annotations:
  #     summary: "JVM Instance {{ $labels.instance }} Old GC time > 80% running time"
  #     description: "{{ $labels.instance }} of job {{ $labels.job }} has been in status [Old GC time > 80% running time] for more than 5 minutes. current seconds ({{ $value }}%)"