---
- name: Set redis_cluster_masters_ip
  set_fact:
    redis_cluster_masters_ip: "{{ redis_cluster_masters_ip | default([]) + [item.name] }}"
  with_items: "{{ redis_cluster_masters }}"

- name: Set redis_cluster_masters_instances
  set_fact:
    redis_cluster_masters_instances: ""

- name: Mount redis_cluster_masters_instances
  set_fact:
    redis_cluster_masters_instances: "{{ redis_cluster_masters_instances + item.name + ':' + item.port|string + ' ' }}"
  with_items: "{{ redis_cluster_masters }}"

- name: Print redis cluster node list
  debug:
    msg: "{{ redis_cluster_masters_instances }}"

- name: Create redis masters
  shell: "printf 'yes' | redis-trib create {{ redis_cluster_masters_instances }}"
  args:
    creates: /etc/redis/redis-cluster-masters.created
  run_once: True
  register: redis_trib

- name: Make created file if redis masters are created
  file:
    path: /etc/redis/redis-cluster-masters.created
    state: touch
    owner: redis
    group: redis
    mode: 0750
  when: redis_trib is defined and "All 16384 slots covered." in redis_trib.stdout 
