---
- name: Assert usage of systemd as an init system
  assert:
    that: ansible_service_mgr == 'systemd'
    msg: "This role only works with systemd"

- name: Get systemd version
  command: systemctl --version
  changed_when: false
  check_mode: false
  register: __systemd_version
  tags:
    - skip_ansible_lint

- name: Set systemd version fact
  set_fact:
    mysqld_exporter_systemd_version: "{{ __systemd_version.stdout_lines[0] | regex_replace('^systemd\\s(\\d+).*$', '\\1') }}"

- name: Naive assertion of proper listen address
  assert:
    that:
      - "':' in mysqld_exporter_web_listen_address"

- name: check collector flags
  fail:
    msg: "Collector flags cannot be both disabled and enabled"
  when: item in mysqld_exporter_collect
  with_items: "{{ mysqld_exporter_no_collect }}"

#- name: "Get checksum for {{ go_arch_map[ansible_architecture] | default(ansible_architecture) }} architecture"
#  set_fact:
#    __mysqld_exporter_checksum: "{{ item.split(' ')[0] }}"
#  with_items:
#    - "{{ lookup('url', 'https://github.com/prometheus/mysqld_exporter/releases/download/v' + mysqld_exporter_version + '/sha256sums.txt', wantlist=True) | list }}"
#  when: "('linux-' + (go_arch_map[ansible_architecture] | default(ansible_architecture)) + '.tar.gz') in item"
