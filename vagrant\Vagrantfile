# -*- mode: ruby -*-
# vi: set ft=ruby :

$script = <<-SCRIPT
echo disable default repo
echo > /etc/apt/sources.list
echo add opentuna repo
cat > /etc/apt/sources.list.d/opentuna.list << EOF
deb https://opentuna.cn/ubuntu/ focal main restricted universe multiverse
deb https://opentuna.cn/ubuntu/ focal-updates main restricted universe multiverse
deb https://opentuna.cn/ubuntu/ focal-backports main restricted universe multiverse
deb https://opentuna.cn/ubuntu/ focal-security main restricted universe multiverse
EOF
apt-get update
# apt-get install default-jdk -y
useradd -m wukong
SCRIPT

Vagrant.configure("2") do |config|
  if Vagrant.has_plugin?("vagrant-cachier")
    config.cache.scope = :box
    config.cache.enable :apt
    config.cache.synced_folder_opts = {
      type: :rsync
    }
  end
  config.vm.box_check_update = false
  config.vm.box = "generic/ubuntu2004"
  config.vm.network "forwarded_port", guest: 22, host: 22222
  config.vm.synced_folder ".", "/vagrant", type: 'rsync'
  config.vm.provider :libvirt do |libvirt|
    libvirt.memory = 512
    libvirt.cpus = 4
    libvirt.video_vram = 32
  end

  config.vm.define "ubuntu0" do |m|
    m.vm.hostname = "HK-PX-001"
  end

#   config.vm.define "ubuntu1" do |m|
#     m.vm.hostname = "SZ-PUSH-001"
#   end

  config.vm.provision "shell", inline: $script

  config.vm.provision "ansible" do |ansible|
    # ansible.playbook = "../deploy/2014.redis_sentinel.playbook.yml"
    # ansible.playbook = "../deploy/2015.rabbitmq.playbook.yml"
    # ansible.playbook = "../deploy/2012.mysql.playbook.yml"
    ansible.playbook = "../deploy/2001.traefik.playbook.yml"
    ansible.vault_password_file = "./ansible_vault_password"
    ansible.become = true
    # ansible.start_at_task = "2001.config_traefik : copy common traefik config templates"
    # ansible.tags = ["traefik", "common_config"]
    ansible.verbose = 'vvvvv'
    ansible.host_vars = {
        "ubuntu0": {
            "ansible_python_interpreter": "/usr/bin/python3"
        }
    }
    ansible.extra_vars = {
        "USE_PROXY": false,
        "DEPLOY_TRAEFIK_HOSTS": "all",
    }
    ansible.raw_arguments = [
        "-e",
        "@../vault.vars.yml"
    ]
  end

end
