{{ansible_managed|comment}}
http:
  routers:
    src.static.igoldhorse.cn:
      service: hkPx001CaddySrcStatic
      rule: Host(`src.static.igoldhorse.cn`,`src.static.igoldhorse.com`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
        domains:
        - main: src.static.igoldhorse.cn
        - main: src.static.igoldhorse.com     
  services:
    hkPx001CaddySrcStatic:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
