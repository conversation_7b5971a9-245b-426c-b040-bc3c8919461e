{{ansible_managed|comment}}
tcp:
  routers:
    devservices:
      service: test_fwdev
      rule: |
        HostSNI(
          `fwdev.top`,
          `jira.fwdev.top`,
          `zen.fwdev.top`,
          `drone.fwdev.top`,
          `puml.fwdev.top`,
          `kroki.fwdev.top`,
          `shields.fwdev.top`,
          `sentry.fwdev.top`,
          `gitlab.fwdev.top`,
          `risk.demo.fwdev.top`,
          `risk-marks.fwdev.top`,
          )
      tls:
        passthrough: true

http:
  routers:
    cert:
      service: cert
      rule: |
        Host(
          `fwdev.top`,
          `jira.fwdev.top`,
          `zen.fwdev.top`,
          `drone.fwdev.top`,
          `puml.fwdev.top`,
          `kroki.fwdev.top`,
          `shields.fwdev.top`,
          `sentry.fwdev.top`,
          `gitlab.fwdev.top`,
          `risk.demo.fwdev.top`,
          `risk-marks.fwdev.top`,
          )
