{{ansible_managed|comment}}

targets:
  webhook1:
    url: https://oapi.dingtalk.com/robot/send?access_token=4d2fa9d32f6b6e761b76b532967c7eef6c88ba2b67927d8720209cc4bf7532fa
    secret: SEC67e97ebf5879f8d69b1beef404a7a97ccb758e6461cc1f9d2a3fbe4c5c5cd31a
  webhook1_mention_all:
    url: https://oapi.dingtalk.com/robot/send?access_token=4d2fa9d32f6b6e761b76b532967c7eef6c88ba2b67927d8720209cc4bf7532fa
    secret: SEC67e97ebf5879f8d69b1beef404a7a97ccb758e6461cc1f9d2a3fbe4c5c5cd31a
    mention:
      all: true
#  webhook_hq:
#    url: https://oapi.dingtalk.com/robot/send?access_token=ba601e095ebfd71c57b73ebbfaa3b6ee30da94a8597cc302da33273437542aca
#    secret: SEC162d394972d0d4105ad1a2dd68664b4a202277a919e9e3bc5947fe41d80530a4
  webhook_py:
#    url: https://oapi.dingtalk.com/robot/send?access_token=4b6d88b957168588ef9a8a5c545df4cdf83d22900138141c40802b02e71564fd
#    secret: SECb29652dc58a6779d725455ef1f8fa38c8400337df820d580373c8c09f1778157
    url: https://oapi.dingtalk.com/robot/send?access_token=00d7d11690a354d481d6f5f887a5fc649ab150221957f3d58b318679a95708c5
    secret: SECa7cea4a14b84d75fbd8e36f660d9dc398c97cf3828b04fcf7f640af31a95f28b
    message:
{% raw %}
      text: '{{ template "email.to.message" . }}'
{% endraw %}
