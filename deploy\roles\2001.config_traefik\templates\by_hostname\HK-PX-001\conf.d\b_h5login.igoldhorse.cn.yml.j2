{{ansible_managed|comment}}
http:
  middlewares:
    redirectH5loginIgoldhorseCnToCom:
      redirectRegex:
        permanent: true
        regex: "^https://h5login.igoldhorse.cn/(.*)"
        replacement: "https://h5login.igoldhorse.com/${1}"
  routers:
    h5login.igoldhorse.cn:
      service: hkPx001CaddyH5Login
      rule: Host(`h5login.igoldhorse.cn`,`h5login.igoldhorse.com`)
      middlewares:
      - gzip
      - redirectH5loginIgoldhorseCnToCom
      tls:
        certResolver: httpResolver
        domains:
        - main: h5login.igoldhorse.cn
        - main: h5login.igoldhorse.com
  services:
    hkPx001CaddyH5Login:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
