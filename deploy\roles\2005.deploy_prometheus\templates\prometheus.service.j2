{{ ansible_managed | comment }}

[Unit]
Description=Prometheus
After=network-online.target

[Service]
Type=simple
User=prometheus
Group=prometheus
ExecReload=/bin/kill -HUP $MAINPID
ExecStart=/usr/local/bin/prometheus \
  --config.file={{ prometheus_config_dir }}/prometheus.yml \
  --storage.tsdb.path={{ prometheus_db_dir }} \
  --web.enable-lifecycle
LimitNOFILE=65000
Restart=always

[Install]
WantedBy=multi-user.target
