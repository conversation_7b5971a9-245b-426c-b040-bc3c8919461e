#!/bin/bash
export RSYNC_PASSWORD={{RSYNC_PASSWORD}};

#PRE_DATE=`date --date='1 days ago' -I | tr -d '-'`
#DATE=`date -I | tr -d '-'`
#basedir=/data/minio
#dirt[1]=biz-platform/data/bpm/report/online
#dirt[2]=biz-platform/data/bpm/report/offline
#dirt[3]=biz-platform/data/bpm/openaccount/online
#dirt[4]=biz-platform/data/bpm/openaccount/offline
#dirt[5]=biz-platform/data/bpm/pi/online
#dirt[6]=biz-platform/data/bpm/pi/offline
#for d in "${dirt[@]}"
#do
#rsync -azvhP \
#    --port=52093 \
#    --log-file=/tmp/sync_minio_data.log \
#    --bwlimit=50m \
#    --exclude=.minio.sys \
#    $basedir/$d/$PRE_DATE \
#    upload_minio_data@127.0.0.1::minio_data/$d/ \
#    ;
#done
#
#for d in "${dirt[@]}"
#do
#rsync -azvhP \
#    --port=52093 \
#    --log-file=/tmp/sync_minio_data.log \
#    --bwlimit=50m \
#    --exclude=.minio.sys \
#    $basedir/$d/$DATE \
#    upload_minio_data@127.0.0.1::minio_data/$d/ \
#    ;
#done

PRE_DATE=`date --date='1 days ago' -I | tr -d '-'`
DATE=`date -I | tr -d '-'`
basedir=/data/minio/biz-platform/data
rsync -azvhP \
    --port=52093 \
    --log-file=/tmp/sync_minio_data.log \
    --bwlimit=50m \
    --exclude=.minio.sys \
    $basedir/bpm/ \
    upload_minio_data@127.0.0.1::minio_data/biz-platform/data/bpm/ \
    ;
rsync -azvhP \
    --port=52093 \
    --log-file=/tmp/sync_fund_minio_data.log \
    --bwlimit=50m \
    --exclude=.minio.sys \
    dl_minio_fund_data@127.0.0.1::minio_fund_data/ \
    /data/minio/biz-platform/data/fund/ \
    ;


