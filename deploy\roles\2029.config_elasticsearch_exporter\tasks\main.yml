---
- name: Update elasticsearch_exporter configuration for multiple ES nodes
  template:
    src: elasticsearch_exporter_multi.service.j2
    dest: /etc/systemd/system/elasticsearch_exporter.service
    owner: root
    group: root
    mode: '0644'
  become: true
  notify:
    - reload systemd
    - restart elasticsearch_exporter
  when: elasticsearch_exporter_multi_node | default(false)

- name: Create elasticsearch_exporter configuration directory
  file:
    path: /etc/elasticsearch_exporter
    state: directory
    owner: prometheus
    group: prometheus
    mode: '0755'
  become: true

- name: Configure elasticsearch_exporter for cluster monitoring
  template:
    src: elasticsearch_cluster_config.yml.j2
    dest: /etc/elasticsearch_exporter/cluster_config.yml
    owner: prometheus
    group: prometheus
    mode: '0644'
  become: true
  notify: restart elasticsearch_exporter
  when: elasticsearch_exporter_cluster_config | default(false)
