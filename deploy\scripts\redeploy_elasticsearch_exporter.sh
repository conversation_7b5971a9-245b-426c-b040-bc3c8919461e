#!/bin/bash

# Elasticsearch Exporter重新部署脚本
# 修复服务配置问题并重新部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 停止现有服务
stop_existing_service() {
    log_header "停止现有Elasticsearch Exporter服务"
    
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        log_info "停止 $host 上的服务..."
        
        # 停止服务（忽略错误，因为服务可能已经停止）
        ansible -i hosts.ini "$host" -m systemd -a "name=elasticsearch_exporter state=stopped" --become || true
        
        # 禁用服务
        ansible -i hosts.ini "$host" -m systemd -a "name=elasticsearch_exporter enabled=no" --become || true
        
        log_info "  ✓ $host 服务已停止"
    done
}

# 清理旧文件
cleanup_old_files() {
    log_header "清理旧文件"
    
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        log_info "清理 $host 上的旧文件..."
        
        # 删除旧的服务文件
        ansible -i hosts.ini "$host" -m file -a "path=/etc/systemd/system/elasticsearch_exporter.service state=absent" --become || true
        
        # 重载systemd
        ansible -i hosts.ini "$host" -m systemd -a "daemon_reload=yes" --become
        
        log_info "  ✓ $host 清理完成"
    done
}

# 重新部署
redeploy_exporter() {
    log_header "重新部署Elasticsearch Exporter"
    
    # 完整重新部署
    log_info "执行完整部署..."
    ansible-playbook -i hosts.ini 2029.elasticsearch_exporter.playbook.yml -v
    
    if [ $? -eq 0 ]; then
        log_info "✓ 部署成功"
    else
        log_error "✗ 部署失败"
        return 1
    fi
}

# 验证部署
verify_deployment() {
    log_header "验证部署结果"
    
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        log_info "验证 $host..."
        
        # 等待服务启动
        sleep 5
        
        # 检查服务状态
        local status=$(ansible -i hosts.ini "$host" -m shell -a "systemctl is-active elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
        
        if [ "$status" = "active" ]; then
            log_info "  ✓ 服务运行正常"
            
            # 检查进程
            local process_check=$(ansible -i hosts.ini "$host" -m shell -a "pgrep -f elasticsearch_exporter" --become 2>/dev/null | grep -v "SUCCESS" | wc -l || echo "0")
            
            if [ "$process_check" -gt 0 ]; then
                log_info "  ✓ 进程运行正常"
            else
                log_warn "  ⚠ 进程未找到"
            fi
            
            # 检查端口监听
            local port_check=$(ansible -i hosts.ini "$host" -m shell -a "netstat -tlnp | grep :9114" --become 2>/dev/null | grep -v "SUCCESS" | wc -l || echo "0")
            
            if [ "$port_check" -gt 0 ]; then
                log_info "  ✓ 端口9114监听正常"
            else
                log_warn "  ⚠ 端口9114未监听"
            fi
            
            # 检查指标端点（等待更长时间）
            sleep 10
            local metrics_check=$(ansible -i hosts.ini "$host" -m uri -a "url=http://localhost:9114/metrics timeout=10" 2>/dev/null | grep -c "status.*200" || echo "0")
            
            if [ "$metrics_check" -gt 0 ]; then
                log_info "  ✓ 指标端点正常"
            else
                log_warn "  ⚠ 指标端点异常，检查详细信息..."
                
                # 显示服务日志
                ansible -i hosts.ini "$host" -m shell -a "journalctl -u elasticsearch_exporter --no-pager -n 10" --become
            fi
        else
            log_error "  ✗ 服务未运行: $status"
            
            # 显示服务状态详情
            ansible -i hosts.ini "$host" -m shell -a "systemctl status elasticsearch_exporter --no-pager" --become
            
            # 显示最近的日志
            ansible -i hosts.ini "$host" -m shell -a "journalctl -u elasticsearch_exporter --no-pager -n 20" --become
        fi
        
        echo ""
    done
}

# 测试Elasticsearch连接
test_elasticsearch_connection() {
    log_header "测试Elasticsearch连接"
    
    local es_nodes=("*************:9200" "*************:9200" "*************:9200")
    
    for node in "${es_nodes[@]}"; do
        log_info "测试连接到 $node..."
        
        if curl -s --connect-timeout 5 "http://$node" > /dev/null; then
            log_info "  ✓ $node 连接正常"
            
            # 获取集群健康状态
            local health=$(curl -s "http://$node/_cluster/health" | jq -r '.status' 2>/dev/null || echo "unknown")
            log_info "  集群状态: $health"
        else
            log_warn "  ⚠ $node 连接失败"
        fi
    done
}

# 显示使用说明
show_usage_info() {
    log_header "部署完成信息"
    
    echo ""
    echo "Elasticsearch Exporter已重新部署完成！"
    echo ""
    echo "服务信息:"
    echo "  - 端口: 9114"
    echo "  - 指标路径: /metrics"
    echo "  - 监控的ES集群: *************:9200, *************:9200, *************:9200"
    echo ""
    echo "验证命令:"
    echo "  # 检查服务状态"
    echo "  ansible -i hosts.ini 'SZ-ES-001' -m shell -a 'systemctl status elasticsearch_exporter' --become"
    echo ""
    echo "  # 检查指标"
    echo "  curl http://SZ-ES-001:9114/metrics"
    echo ""
    echo "  # 检查ES连接"
    echo "  curl http://SZ-ES-001:9114/metrics | grep elasticsearch_up"
    echo ""
    echo "  # 查看日志"
    echo "  ansible -i hosts.ini 'SZ-ES-001' -m shell -a 'journalctl -u elasticsearch_exporter -f' --become"
    echo ""
    echo "Prometheus配置:"
    echo "  - 目标: SZ-ES-001:9114, SZ-MONT-002:9114, HK-MG-002:9114"
    echo "  - Job名称: elasticsearch"
    echo ""
}

# 主函数
main() {
    echo "========================================"
    echo "Elasticsearch Exporter重新部署"
    echo "部署时间: $(date)"
    echo "========================================"
    echo ""
    
    # 检查前置条件
    if [ ! -f "hosts.ini" ]; then
        log_error "hosts.ini 文件不存在"
        exit 1
    fi
    
    if ! command -v ansible-playbook &> /dev/null; then
        log_error "ansible-playbook 未安装"
        exit 1
    fi
    
    # 测试ES连接
    test_elasticsearch_connection
    echo ""
    
    # 执行重新部署步骤
    stop_existing_service
    cleanup_old_files
    redeploy_exporter
    verify_deployment
    show_usage_info
    
    log_info "Elasticsearch Exporter重新部署完成！"
}

# 执行主函数
main "$@"
