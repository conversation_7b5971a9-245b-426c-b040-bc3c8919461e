{{ansible_managed|comment}}

bind 0.0.0.0
port 26379
daemonize yes
pidfile "/var/run/sentinel/redis-sentinel.pid"
logfile "/var/log/redis/redis-sentinel.log"
dir "/var/lib/redis"
sentinel myid 95785cae80a2fa241d8e06346968cc7a6f3dc9c8
sentinel deny-scripts-reconfig yes
sentinel monitor mymaster 127.0.0.1 6379 1
sentinel config-epoch mymaster 0
sentinel leader-epoch mymaster 0
protected-mode no
sentinel current-epoch 0
requirepass "Redis@Bussiness"