---
MINIO_CONFIG_DIR: /etc/minio
MINIO_DATA_DIR: /data/minio
MINIO_CONSOLE_HTTP_PORT: 49462
MINIO_PORT: 42924
MINIO_ROOT_USER: EeR7Ko3a
MINIO_ROOT_PASSWORD: thaiThahb0woh


# `minio_users` DEFINE
# minio_users:
#  {{ansible_hostname}}:
#  - bucket: bucket name
#    user: username
#    pass: password
#    policy: `read` or `write`
minio_users:
  SZ-OPACC-001:
  # biz-platform
  - bucket: biz-platform
    user: sz-bp-read
    pass: XCqz7wqpOGlQ1M6K1q44
    policy: read
  - bucket: biz-platform
    user: sz-bp-5Eaet
    pass: DcPmPrrFJXW3kTE5h
    policy: write
  HK-TD-002:
  # biz-platform
  - bucket: biz-platform
    user: biz-platform_readonly
    pass: aem6kit_ae7faeTh
    policy: read
  - bucket: biz-platform
    user: biz-platform_ookaiX5E
    pass: ieshu4naewohX,ie
    policy: write
  HK-MG-002:
  # ayers-reports
  #- bucket: ayers-reports
  #  user: ayers-reports_Oox3shai
  #  pass: vLs3692qGiJoo6enczTwzyvJq
  #  policy: write
  #- bucket: ayers-reports
  #  user: ayers-reports_readonly
  #  pass: Shael0voob
  #  policy: read
  - bucket: lpoa
    user: lpoa_wr
    pass: vLs3692qGiJoreenczTwzyvJq
    policy: write
  - bucket: lpoa
    user: lpoa_readonly
    pass: Shael0vderoob
    policy: read
  - bucket: ayers
    user: ayers-write
    pass: MWyoob8EkSSYy0L9
    policy: write
  - bucket: ayers
    user: ayers-read
    pass: zmK1PFJPtm
    policy: read
  SZ-PUSH-001:
  - bucket: ayers-reports
    user: ayers-reports_Oox3shai
    pass: vLs3692qGiJoo6enczTwzyvJq
    policy: write
  SZ-NEWS-001:
  - bucket: oms
    user: sz-user-wr
    access: public
    pass: DcPmPrrFJXW3kTE5hw
    policy: write
