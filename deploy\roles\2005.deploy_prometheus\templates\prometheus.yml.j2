# {{ ansible_managed | comment }}

global:
  scrape_interval: 20s
  evaluation_interval: 20s
alerting:
  alertmanagers:
  - file_sd_configs:
    - files:
      - alertmanager.d/*.yml
rule_files:
- rule.d/*.yml
scrape_configs:
- job_name: prometheus
  static_configs:
  - targets:
    - 127.0.0.1:9090
- job_name: file_sd
  file_sd_configs:
  - files:
    - file_sd.d/*.yml
  relabel_configs:
  - source_labels: [__address__]
    target_label: __param_target
  - source_labels: [__param_target]
    target_label: instance
- job_name: jvm
  metrics_path: /actuator/prometheus
  file_sd_configs:
  - files:
    - jvm_file_sd.d/jvm.yml
  relabel_configs:
  - source_labels:
    - __address__
    - instance
    separator: "-"
    target_label: instance
- job_name: jvm_s
  file_sd_configs:
  - files:
    - jvm_file_sd.d/jvm_s.yml
  relabel_configs:
  - source_labels:
    - instance
    separator: "/"
    target_label: __metrics_path__
    regex: (.+)
    replacement: $1/actuator/prometheus
  - source_labels:
    - __address__
    - instance
    separator: "-"
    target_label: instance

#- job_name: 'ping_status'
#  metrics_path: /probe
#  params:
#    module: [icmp]
#  file_sd_configs:
#  - files:
#    - blackbox_sd.d/blackbox_icmp.yml
#  relabel_configs:
#  - source_labels: [__address__]
#    target_label: __param_target
#  - source_labels: [__param_target]
#    target_label: instance
#  - target_label: __address__
#    replacement: 127.0.0.1:9115
- job_name: 'port_status'
  metrics_path: /probe
  params:
    module: [tcp_connect]
  file_sd_configs:
  - files:
    - blackbox_sd.d/blackbox_port.yml
    - blackbox_sd.d/blackbox_port_frp.yml
  relabel_configs:
  - source_labels:
    - __address__
    - instance
    separator: "-"
    target_label: instance
  - source_labels: [__address__]
    target_label: __param_target
  #- source_labels: [__param_target]
  #  target_label: instance
  - target_label: __address__
    replacement: 127.0.0.1:9115
#- job_name: 'blackbox-http'
#  metrics_path: /probe
#  params:
#    modelue: [http_2xx]
#  file_sd_configs:
#  - files:
#    - blackbox_sd.d/blackbox_http.yml
#  relabel_configs:
#  - source_labels: [__address__]
#    target_label: __param_target
#  - source_labels: [__param_target]
#    target_label: instance
#  - target_label: __address__
#    replacement: 127.0.0.1:9115
- job_name: 'redis_exporter_targets'
  static_configs:
    - targets:
      - SZ-PX-001:9100
  metrics_path: /metrics
  relabel_configs:
    - source_labels: [__address__]
      target_label: __param_target
    - source_labels: [__param_target]
      target_label: instance
  #  - target_label: __address__
  #    replacement: SZ-PX-001:9121
#- job_name: 'redis_exporter'
#  static_configs:
#  - targets:
#    - SZ-PX-001:9121
#- job_name: 'kafkajmx'
#  static_configs:
#    - targets: ['*************:9988']
- job_name: 'rktmqexporter'
  static_configs:
    - targets: ['SZ-DERIV-001:5557']
- job_name: 'kafka'
  static_configs:
  - targets: ['SZ-KAFKA-001:9308','SZ-KAFKA-002:9308','SZ-KAFKA-003:9308']
- job_name: 'kafkajmx'
  static_configs:
    - targets: ['SZ-KAFKA-001:7071','SZ-KAFKA-002:7071','SZ-KAFKA-003:7071']
#- job_name: 'sz_redis_exporter_targets'
#  static_configs:
#  - targets:
#    - SZ-REDIS-001:6379
#    - SZ-REDIS-002:6379
#    - SZ-REDIS-003:6379
#  params:
#    check-keys: ["metrics:*"]
#  metrics_path: /scrape
#  relabel_configs:
#    - source_labels: [__address__]
#      regex: "(.*):(.*)"
#      target_label: __param_target
#      replacement: redis://$1:6379
#    - source_labels: [__param_target]
#      target_label: instance
#    - target_label: __address__
#      replacement: SZ-REDIS-001:9121
#- job_name: 'sz_redis_exporter'
#  static_configs:
#  - targets:
#    - SZ-REDIS-001:9121
#- job_name: 'dns-uptime'
#  metrics_path: /probe
#  params:
#    module: [dns_tcp]
#  static_configs:
#    - labels:
#        server: 'dnspod_free'
#      targets:
#      - cat.dnspod.net
#      - canoeing.dnspod.net
#    - labels:
#        server: 'dnspod_ent'
#      targets:
#      - ns3.dnsv4.com
#      - ns4.dnsv4.com
#  relabel_configs:
#    - source_labels: [__address__]
#      target_label: __param_target
#    - source_labels: [__param_target]
#      target_label: instance
#    - target_label: __address__
#      replacement: 127.0.0.1:9115