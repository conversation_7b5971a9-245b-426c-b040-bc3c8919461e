# {{ansible_managed|comment}}

route:
  group_wait: 10s
  group_interval: 30s
  repeat_interval: 30m
  group_by:
  - alertname
  receiver: slack
  routes:
  - receiver: email
    continue: true
    matchers:
    - severity!="debug"
    - gh_severity!="debug"
  - receiver: slack
    continue: true
    matchers:
    - severity!="debug"
    - gh_severity!="debug"
  - receiver: pushover
    continue: true
    matchers:
    - severity="debug"
    - gh_severity="debug"
  - receiver: dingtalk_sz
    continue: true
    matchers:
    - severity=~"(warning|critical)"
  - receiver: dingtalk_hk
    continue: true
    matchers:
    - severity=~"(warning|critical)"
  - receiver: dingtalk_sz_mention_all
    continue: true
    matchers:
    - gh_severity="critical"
  - receiver: dingtalk_hk_mention_all
    continue: true
    matchers:
    - gh_severity="critical"
#  - receiver: dingtalk_hq
#    continue: true
#    matchers:
#    - hq_severity="critical"
  - receiver: dingtalk_py
    continue: true
    matchers:
    - py_severity="critical"
    - gh_severity="critical"
receivers:
# slack receiver
- name: slack
  slack_configs:
  - api_url: *********************************************************************************
    send_resolved: true
    channel: alerts
# {% raw %}
#     text: "{{ range .Alerts }}<!channel> {{ .Annotations.summary }}\n{{ .Annotations.description }}\n{{ end }}"
# {% endraw %}
# dingtalk receiver
- name: dingtalk_sz
  webhook_configs:
  - url: http://*************:8060/dingtalk/webhook1/send
- name: dingtalk_sz_mention_all
  webhook_configs:
  - url: http://*************:8060/dingtalk/webhook1_mention_all/send
- name: dingtalk_hk
  webhook_configs:
  - url: http://************:8060/dingtalk/webhook1/send
- name: dingtalk_hk_mention_all
  webhook_configs:
  - url: http://************:8060/dingtalk/webhook1_mention_all/send
#- name: dingtalk_hq
#  webhook_configs:
#  - url: http://************:8060/dingtalk/webhook_hq/send
- name: dingtalk_py
  webhook_configs:
  - url: http://************:8060/dingtalk/webhook_py/send
- name: pushover
  pushover_configs:
  - user_key: uydfem29ffvnt4cs8nucgcky9ohbnu
    token: auf6iiuu5bvs54s29mgknrcywbn8b6
- name: email
  email_configs:
  - to: <EMAIL>
    smarthost: smtp.mailgun.org:587
    require_tls: true
    from: <EMAIL>
    auth_username: <EMAIL>
    auth_password: **************************************************
inhibit_rules:
- source_match:
    alertname: BlackboxProbeFailed
    severity: critical
  target_match_re:
    alertname: instance-down|PrometheusTargetMissing
    severity: critical
  equal:
    - instance
- source_match:
    severity: critical
  target_match:
    severity: warning
  equal:
    - ['alertname', 'instance']