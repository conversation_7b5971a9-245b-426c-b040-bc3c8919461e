---
- name: copy common config.d
  copy:
    src: 'common/{{item.name}}'
    dest: "{{prometheus_config_dir}}"
    owner: prometheus
    group: prometheus
    mode: '644'
  ignore_errors: true
  with_items:
  - name: rule.d
  - name: file_sd.d
  - name: alertmanager.d
  notify:
  - reload prometheus

- name: copy node rule.d
  copy:
    src: "{{ansible_hostname}}/{{item.name}}"
    dest: "{{prometheus_config_dir}}"
    owner: prometheus
    group: prometheus
    mode: '644'
  ignore_errors: true
  with_items:
  - name: rule.d
  - name: file_sd.d
  - name: alertmanager.d
  - name: blackbox_sd.d
  - name: jvm_file_sd.d
  #- name: redis_sd.d
  #- name: mysql_sd.d
  notify:
  - reload prometheus

- name: "absent some config file {{item.rel_path}}, {{item.note}}"
  file:
    dest: "{{prometheus_config_dir}}/{{item.rel_path}}"
    state: absent
  with_items:
  - rel_path: rule.d/promtail.yml
    note: rule file name must end with *.rule.yml
  - rel_path: file_sd.d/blackbox_http.yml
    note: mv to blackbox_sd.d
  - rel_path: file_sd.d/blackbox_icmp.yml
    note: mv to blackbox_sd.d
  - rel_path: file_sd.d/blackbox_port.yml
    note: mv to blackbox_sd.d
  ignore_errors: true
  notify:
  - reload prometheus

- name: Remove redis_sd directory if it exist
  file:
    path: "{{prometheus_config_dir}}/redis_sd.d"
    state: absent
  ignore_errors: true
  notify:
  - reload prometheus

- name: Remove mysql_sd directory if it exist
  file:
    path: "{{prometheus_config_dir}}/mysql_sd.d"
    state: absent
  ignore_errors: true
  notify:
  - reload prometheus

