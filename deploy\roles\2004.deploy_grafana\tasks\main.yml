- name: download grafana package
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: '{{GRAFANA_DOWNLOAD_URL}}'
    dest: "/tmp/{{GRAFANA_PKG_FILE}}"
    mode: '0644'
    checksum: "{{GRAFANA_CHECKSUM}}"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: install grafana on ubuntu
  apt:
    deb: "/tmp/{{GRAFANA_PKG_FILE}}"
  when: ansible_facts["distribution"].upper() == "UBUNTU"
  notify:
  - restart grafana-server

- name: install grafana on centos
  shell: "yum install /tmp/{{GRAFANA_PKG_FILE}} -y"
  when: ansible_facts["distribution"].upper() == "CENTOS"
  notify:
  - restart grafana-server

- name: install grafana plugins
  shell: "grafana-cli plugins install {{item}}"
  with_items:
  - redis-datasource
  - grafana-clock-panel
  - grafana-worldmap-panel
  - grafana-simple-json-datasource
  - grafana-piechart-panel
  notify:
  - restart grafana-server
