---
- name: create sqlpad system group
  group:
    name: sqlpad
    system: true
    state: present
    gid: 3660

- name: create sqlpad system user
  user:
    name: sqlpad
    system: true
    shell: "/usr/sbin/nologin"
    group: sqlpad
    createhome: false
    uid: 3660

- name: create sqlpad db directory
  file:
    path: "/data/sqlpad"
    state: directory
    recurse: true
    owner: sqlpad
    group: sqlpad
    mode: 0755

- name: create apps directories
  file:
    path: "/opt/sqlpad"
    state: directory
    owner: sqlpad
    group: sqlpad
    mode: 0755

#- name: apt install nodejs
#  package:
#    name: nodejs
#    state: present
#    update_cache: true
- block:
    - name: apt install npm
      apt:
        name: npm
        state: present
        update_cache: true
    - name: Install uuid  
      npm:
        name: uuid
        global: yes
        state: latest
    - name: Install n
      npm:
        name: n
        global: yes
        state: latest
    - name: install node
      command: n stable
  when: ansible_facts["distribution"].upper() == "UBUNTU"

- name: download sqlpad binary
  become: true
  get_url:
    url: "https://github.com/sqlpad/sqlpad/archive/refs/tags/v{{sqld_version}}.tar.gz"
    dest: "/opt/sqlpad-{{sqld_version}}.tar.gz"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: Unpack sqlpad binary
  become: true
  unarchive:
    src: "/opt/sqlpad-{{sqld_version}}.tar.gz"
    dest: "/opt"
    remote_src: True
  check_mode: false

- name: run scripts/build.sh
  shell: 
    cmd: scripts/build.sh
    chdir: /opt/sqlpad-{{sqld_version}}

- name: create config.dev.env
  template:
    src: config.dev.env.j2
    dest: /opt/sqlpad-{{sqld_version}}/server/config.dev.env
    owner: sqlpad
    group: sqlpad
    mode: 0664
  notify:
  - restart sqlpad

- name: mv default diretory
  shell:
    cmd: cp -r /opt/sqlpad-{{sqld_version}}/* /opt/sqlpad

- name: Ensure apps directories
  file:
    path: "/opt/sqlpad"
    state: directory
    owner: sqlpad
    group: sqlpad
    recurse: yes

- name: create systemd service unit
  template:
    src: sqlpad.service.j2
    dest: /etc/systemd/system/sqlpad.service
    owner: root
    group: root
    mode: 0644
  notify:
  - restart sqlpad

- name: enabled sqlpad systemd
  systemd:
    name: sqlpad
    daemon-reload: true
    enabled: true
