---
- name: toggler cloudera user sudo password less
  lineinfile:
    path: /etc/sudoers
    line: 'chao.peng ALL=(root) NOPASSWD:ALL'
    regexp: '^chao.peng'
    create: true
    state: present

- name: toggler cloudera user sudo password less
  lineinfile:
    path: /etc/sudoers
    line: 'chao.peng ALL=(root) NOPASSWD:ALL'
    regexp: '^chao.peng'
    create: true
    state: present

- name: enable sshd password login
  lineinfile:
    path: /etc/ssh/sshd_config
    regex: '{{item.regex}}'
    line: '{{item.line}}'
    validate: sshd -t -f %s
  notify:
  - reload ssh
  with_items:
  - regex: ^PasswordAuthentication.*
    line: PasswordAuthentication yes
