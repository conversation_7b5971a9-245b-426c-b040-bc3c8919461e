---
# tasks file for enable_prometheus
- name: create prometheus system group
  group:
    name: prometheus
    system: true
    state: present
    gid: 3231

- name: create prometheus system user
  user:
    name: prometheus
    system: true
    shell: "/usr/sbin/nologin"
    group: prometheus
    createhome: false
    home: "{{ prometheus_db_dir }}"
    uid: 3231

- name: create prometheus data directory
  file:
    path: "{{ prometheus_db_dir }}"
    state: directory
    owner: prometheus
    group: prometheus
    mode: 0755

- name: create prometheus configuration directories
  file:
    path: "{{ item }}"
    state: directory
    owner: root
    group: prometheus
    mode: 0770
  with_items:
    - "{{ prometheus_config_dir }}"
    - "{{ prometheus_config_dir }}/rule.d"
    - "{{ prometheus_config_dir }}/file_sd.d"
    - "{{ prometheus_config_dir }}/alertmanager.d"

- name: prometheus url
  debug:
    msg: "https://filescfcdn.fwdev.top/common/prometheus-{{ prometheus_version }}.linux-{{ go_arch }}.tar.gz"

- name: download prometheus binary to local folder
  become: false
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "https://filescfcdn.fwdev.top/common/prometheus-{{ prometheus_version }}.linux-{{ go_arch }}.tar.gz"
    dest: "/tmp/prometheus-{{ prometheus_version }}.linux-{{ go_arch }}.tar.gz"
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: unpack prometheus binaries
  unarchive:
    src: "/tmp/prometheus-{{ prometheus_version }}.linux-{{ go_arch }}.tar.gz"
    dest: "/tmp"
    creates: "/tmp/prometheus-{{ prometheus_version }}.linux-{{ go_arch }}/prometheus"
    remote_src: true
  check_mode: false

- name: copy official prometheus and promtool binaries
  copy:
    src: "/tmp/prometheus-{{ prometheus_version }}.linux-{{ go_arch }}/{{ item }}"
    dest: "/usr/local/bin/{{ item }}"
    mode: '755'
    owner: root
    group: root
    remote_src: true
  with_items:
    - prometheus
    - promtool
  notify:
  - restart prometheus

- name: create systemd service unit
  template:
    src: prometheus.service.j2
    dest: /etc/systemd/system/prometheus.service
    owner: root
    group: root
    mode: 0644
  notify:
  - restart prometheus

- name: enabled prometheus systemd
  systemd:
    name: prometheus
    daemon-reload: true
    enabled: true

- name: configure prometheus
  template:
    src: prometheus.yml.j2
    dest: "{{ prometheus_config_dir }}/prometheus.yml"
    force: true
    owner: root
    group: prometheus
    mode: 0640
    validate: "/usr/local/bin/promtool check config %s"
  when: (ansible_hostname)=='SZ-ES-001' or (ansible_hostname)=='SZ-MONT-002'
  notify:
  - reload prometheus

- name: configure hk prometheus
  template:
    src: prometheus.yml.hk.j2
    dest: "{{ prometheus_config_dir }}/prometheus.yml"
    force: true
    owner: root
    group: prometheus
    mode: 0640
    validate: "/usr/local/bin/promtool check config %s"
  when: (ansible_hostname)=='HK-MG-002'
  notify:
  - reload prometheus
