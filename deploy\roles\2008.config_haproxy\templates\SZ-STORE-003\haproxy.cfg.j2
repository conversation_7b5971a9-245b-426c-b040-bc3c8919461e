# file: /etc/haproxy/haproxy.cf
{{ ansible_managed|comment }}

global
    log /dev/log   local0
    log /dev/log    local1 notice
    chroot /var/lib/haproxy
    stats socket /var/lib/haproxy/stats
    stats timeout 30s
    user haproxy
    group haproxy
    daemon
frontend stats
    bind *:8404
   # http-request use-service prometheus-exporter if { path /metrics }
    stats enable
    stats uri /stats
    stats refresh 10s
listen xxl-job-admin
    bind 0.0.0.0:8828
    mode http
    timeout connect 10s
    timeout client 120s
    timeout server 120s
    server xxl-job 127.0.0.1:8080

