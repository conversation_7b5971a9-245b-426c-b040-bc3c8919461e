- name: create rsyncd.d
  file:
    path: /etc/rsyncd.d
    state: directory
    owner: root
    group: root
    mode: "700"

- name: create rsyncd.conf
  template:
    src: rsyncd.conf.j2
    dest: /etc/rsyncd.conf
    owner: root
    group: root
    mode: 600

- name: ensure rsync start at boot
  systemd:
    name: rsync
    daemon_reload: true
    enabled: true
    state: started

- name: performance per host task
  include_tasks: '{{ansible_hostname}}.yml'
