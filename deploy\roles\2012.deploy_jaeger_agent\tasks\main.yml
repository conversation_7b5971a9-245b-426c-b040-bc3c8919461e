---
- name: create jaeger_agent system group
  group:
    name: jaeger_agent
    system: true
    state: present
    gid: 4627

- name: create data directory
  file:
    path: /data
    state: directory
    owner: root
    group: root
    mode: 0755

- name: create jaeger_agent system user
  user:
    name: jaeger_agent
    system: true
    shell: "/usr/sbin/nologin"
    group: jaeger_agent
    groups:
    - wukong
    createhome: false
    home: "{{ JAEGER_AGENT_DB_DIR }}"
    uid: 4627

- name: create jaeger_agent data directory
  file:
    path: "{{ JAEGER_AGENT_DB_DIR }}"
    state: directory
    recurse: true
    owner: jaeger_agent
    group: jaeger_agent
    mode: 0755

- name: create jaeger_agent configuration directories
  file:
    path: "{{ item }}"
    state: directory
    owner: root
    group: jaeger_agent
    mode: 0770
  with_items:
  - "{{ JAEGER_AGENT_CONFIG_DIR }}"

- name: jaeger_agent url
  debug:
    msg: "{{JAEGER_AGENT_DOWNLOAD_URL}}"

- name: download jaeger_agent binary to local folder
  become: false
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "{{JAEGER_AGENT_DOWNLOAD_URL}}"
    dest: "/tmp/{{JAEGER_AGENT_FILE_NAME}}"
    checksum: '{{JAEGER_AGENT_CHECKSUM}}'
  register: _download_binary
  until: _download_binary is succeeded
  retries: 5
  delay: 2

- name: unpack jaeger_agent binaries
  unarchive:
    src: "/tmp/{{JAEGER_AGENT_FILE_NAME}}"
    dest: "/tmp"
    remote_src: true
  check_mode: false

- name: copy official jaeger_agent binaries
  copy:
    src: "/tmp/{{item}}"
    dest: "/usr/local/bin/"
    mode: '755'
    owner: root
    group: root
    remote_src: true
  with_items:
  - "{{JAEGER_AGENT_FILE_NAME|splitext|first|splitext|first}}/jaeger-agent"
  notify:
  - restart jaeger_agent

- name: create systemd service unit
  template:
    src: jaeger_agent.service.j2
    dest: /etc/systemd/system/jaeger_agent.service
    owner: root
    group: root
    mode: 0644
  notify:
  - restart jaeger_agent

- name: enabled jaeger_agent service
  systemd:
    name: jaeger_agent
    daemon_reload: true
    enabled: true
