{{ansible_managed|comment}}
http:
  middlewares:
    hkPx001SqaIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
        - "*************"
        - "*************"
  routers:
    struct-quotation-api.igoldhorse.com:
      service: hkPx001SqaBackend
      rule: Host(`struct-quotation-api.igoldhorse.com`)
      middlewares:
      - hkPx001SqaIpWhiteList
      tls:
        certResolver: httpResolver
        domains:
        - main: struct-quotation-api.igoldhorse.com
  services:
    hkPx001SqaBackend:
      loadBalancer:
        servers:
        - url: http://HK-OPENAPI-001:8061
