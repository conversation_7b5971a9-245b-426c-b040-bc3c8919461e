- name: create seata system group
  group:
    name: "{{ start_user }}"
    system: true
    state: present

- name: create seata system user
  user:
    name: "{{ start_user }}"
    system: true
    shell: "/usr/sbin/nologin"
    createhome: true

- name: Download seata
  get_url:
    use_proxy: true
    url: "{{seata_url}}"
    dest: /tmp/{{seata_file}}

- name: decompression seata
  unarchive: src=/tmp/{{seata_file}} dest=/etc remote_src=true owner="{{ start_user }}" group="{{ start_user }}"

- name: Ensure  directories
  file:
    path: "{{install_dir}}"
    state: directory
    mode: 0755
    recurse: yes

- name: config txt
  template:
    src: "{{ item }}.j2"
    dest: "{{ install_dir }}/{{item}}"
    owner: "{{ start_user }}"
    group: "{{ start_user }}"
    mode: 0600
  with_items:
    - config.txt

- name: config file
  template:
    src: "{{item}}.j2"
    dest: "{{ install_dir }}/conf/{{item}}"
    owner: "{{ start_user }}"
    group: "{{ start_user }}"
    mode: 0600
  with_items:
    - registry.conf
    - file.conf

- name: nacos-config.sh
  template:
    src: "{{item}}.j2"
    dest: "{{ install_dir }}/conf/{{item}}"
    owner: "{{ start_user }}"
    group: "{{ start_user }}"
    mode: 0755
  with_items:
    - nacos-config.sh

- name: makdir logs
  file:
    path: /data/logs/seata
    state: directory
    owner: "{{ start_user }}"
    group: "{{ start_user }}"

- name: makdir logs
  file:
    path: "/home/<USER>/logs/seata"
    state: directory
    owner: "{{ start_user }}"
    group: "{{ start_user }}"

- name: touch logs file
  file:
    path: "/data/logs/seata/seata-out.log"
    state: touch
    owner: "{{ start_user }}"
    group: "{{ start_user }}"
    mode: 0644

- name: run conf/nacos-config.sh
  shell: 
    cmd: bash nacos-config.sh -h {{seata_nacos_host}} -p 8848 -g SEATA_GROUP -t 397c6a46-383a-4e9e-b9a9-3b5b203235cb -u nacos -w nacos
    chdir: "{{install_dir}}/conf"
  ignore_errors: true

- name: copy  supervisor config file
  template:
    src: supervisor_project_config.ini.j2
    dest: "/etc/supervisor/conf.d/seata.conf"
  notify:
  - "restart seata"