- name: create sync_minio_data.sh
  template:
    src: sync_minio_data.sh.j2
    dest: /home/<USER>
    mode: 755
    
- name: Ensure a rsync minio job1 that runs 
  cron:
    name: "rsyn_minio_job1"
    job: "{{minio_cron_inline1}}"

- name: Ensure a rsync minio job2 that runs 
  cron:
    name: "rsyn_minio_job2"
    job: "{{minio_cron_inline2}}"

- name: create delete_oldfiles.sh
  template:
    src: delete_oldfiles.sh.j2
    dest: /data/create delete_oldfiles.sh
    mode: 755
    
- name: Ensure a delete_oldfiles job that runs 
  cron:
    name: "delete_oldfiles_job"
    job: "{{delete_oldfiles_cron_inline}}"
    day: "*/1"
    hour: "10,23"
    minute: 1