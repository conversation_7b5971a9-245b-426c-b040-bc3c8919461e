{{ansible_managed|comment}}
http:
  routers:
    # Dmz
    gqlDmz_base.trade.goldhorsecn.com:
      rule: Host(`base.trade.goldhorsecn.com`, `cn.trade.goldhorsecn.com`) && Method(`POST`) && Path(`/dmz/api/v1/gql/`)
      service: gqlDmz
      middlewares:
      - gzip
      - requestAuthError
      - requestAuthDmz
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.goldhorsecn.com
        - main: cn.trade.goldhorsecn.com
    gqlDmz_base.trade.goldhorsecn.com_cors:
      rule: Host(`base.trade.goldhorsecn.com`, `cn.trade.goldhorsecn.com`) && Method(`OPTIONS`) && Path(`/dmz/api/v1/gql/`)
      service: gqlDmz
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.goldhorsecn.com
        - main: cn.trade.goldhorsecn.com
    gqlDmz_base.trade.goldhorsecn.com_ping:
      rule: Host(`base.trade.goldhorsecn.com`, `cn.trade.goldhorsecn.com`) && Method(`GET`) && Path(`/dmz/-/ping/`)
      service: gqlDmz
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.goldhorsecn.com
        - main: cn.trade.goldhorsecn.com
    # Secret
    gqlSecret_base.trade.goldhorsecn.com:
      rule: Host(`base.trade.goldhorsecn.com`, `cn.trade.goldhorsecn.com`) && Method(`POST`) && Path(`/secret/api/v1/gql/`)
      service: gqlSecret
      middlewares:
      - gzip
      - requestAuthError
      - requestAuthSecret
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.goldhorsecn.com
        - main: cn.trade.goldhorsecn.com
    gqlSecret_base.trade.goldhorsecn.com_cors:
      rule: Host(`base.trade.goldhorsecn.com`, `cn.trade.goldhorsecn.com`) && Method(`OPTIONS`) && Path(`/secret/api/v1/gql/`)
      service: gqlSecret
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.goldhorsecn.com
        - main: cn.trade.goldhorsecn.com
    gqlSecret_base.trade.goldhorsecn.com_ping:
      rule: Host(`base.trade.goldhorsecn.com`, `cn.trade.goldhorsecn.com`) && Method(`GET`) && Path(`/secret/-/ping/`)
      service: gqlSecret
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.goldhorsecn.com
        - main: cn.trade.goldhorsecn.com
    mqttSecret_sz.quot.goldhorsecn.com:
      rule: Host(`sz.quot.goldhorsecn.com`) && Path(`/ws`)
      service: mqttSecret
      middlewares:
      - requestAuthError
      - requestAuthSecret
      tls:
        certResolver: httpResolver
        domains:
        - main: base.trade.goldhorsecn.com
        - main: cn.trade.goldhorsecn.com
    mqttNoSecret_sz.quot.goldhorsecn.com:
      rule: Host(`sz.noquot.goldhorsecn.com`) && Path(`/ws`)
      service: mqttSecret
      tls:
        certResolver: httpResolver
