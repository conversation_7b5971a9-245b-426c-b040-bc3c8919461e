{{ansible_managed|comment}}
http:
  middlewares:
    szBackendOmsMediaIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
  routers:
    oms-media.igoldhorse.cn:
      service: szBackendOmsMedia
      rule: Host(`oms-media.igoldhorse.cn`)
      middlewares:
      - gzip
      tls:
        certResolver: httpResolver
        domains:
        - main: oms-media.igoldhorse.cn
  services:
    szBackendOmsMedia:
      loadBalancer:
        servers:
        - url: http://SZ-NEWS-001:32709
