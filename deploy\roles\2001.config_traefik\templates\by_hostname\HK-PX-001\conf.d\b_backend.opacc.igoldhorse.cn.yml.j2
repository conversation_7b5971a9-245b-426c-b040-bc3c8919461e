{{ansible_managed|comment}}
http:
  middlewares:
    stripBackendOpAccApi:
      stripPrefix:
        prefixes:
        - "/api"
    redirectIgoldhorseCnToCom:
      redirectRegex:
        permanent: true
        regex: "^https://backend.opacc.igoldhorse.cn/(.*)"
        replacement: "https://backend.opacc.igoldhorse.com/${1}"
  routers:
    mqtt.backend.opacc.igoldhorse.com:
      service: hkPx001OpAccBackendMqttWs
      rule: Host(`mqtt.backend.opacc.igoldhorse.cn`) && Path(`/ws`)
      tls:
        certResolver: httpResolver
        domains:
        - main: mqtt.backend.opacc.igoldhorse.cn
    backend.opacc.igoldhorse.com_mqttws:
      service: hkPx001OpAccBackendMqttWs
      rule: Host(`backend.opacc.igoldhorse.cn`,`backend.opacc.igoldhorse.com`) && Path(`/ws`)
      tls:
        certResolver: httpResolver
        domains:
        - main: backend.opacc.igoldhorse.cn
        - main: backend.opacc.igoldhorse.com
    backend.opacc.igoldhorse.cn_api:
      service: hkPx001OpAccBackendApi
      rule: Host(`backend.opacc.igoldhorse.cn`,`backend.opacc.igoldhorse.com`) && PathPrefix(`/api`)
      middlewares:
      - gzip
      - stripBackendOpAccApi
      tls:
        certResolver: httpResolver
        domains:
        - main: backend.opacc.igoldhorse.cn
        - main: backend.opacc.igoldhorse.com
    backend.opacc.igoldhorse.cn_h5:
      service: hkPx001CaddyOpAccBackendH5
      rule: Host(`backend.opacc.igoldhorse.cn`,`backend.opacc.igoldhorse.com`)
      middlewares:
      - gzip
      - redirectIgoldhorseCnToCom
      - aaoHeader
      tls:
        certResolver: httpResolver
        domains:
        - main: backend.opacc.igoldhorse.cn
        - main: backend.opacc.igoldhorse.com
  services:
    hkPx001OpAccBackendApi:
      loadBalancer:
        servers:
        - url: http://HK-TD-002:8101
    hkPx001OpAccBackendMqttWs:
      loadBalancer:
        servers:
        - url: http://HK-TD-002:15675
    hkPx001CaddyOpAccBackendH5:
      loadBalancer:
        servers:
        - url: http://HK-PX-001:32709
