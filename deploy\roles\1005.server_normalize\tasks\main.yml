- name: os tasks
  include_tasks: "{{ansible_distribution|lower}}.yml"

- name: config sshd by os
  include_tasks: "{{ansible_distribution|lower}}/sshd.yml"

- name: harden sshd
  lineinfile:
    path: /etc/ssh/sshd_config
    regex: '{{item.regex}}'
    line: '{{item.line}}'
    validate: sshd -t -f %s
  notify:
  - reload ssh
  with_items:
  - regex: ^PermitRootLogin.*
    line: PermitRootLogin no
  - regex: ^PasswordAuthentication.*
    line: PasswordAuthentication no
  #ubuntu22.04 may have to add "PubkeyAcceptedKeyTypes +ssh-rsa"

- name: static hosts with lan ip
  lineinfile:
    path: /etc/hosts
    regex: '.*{{item}}$'
    line: '{{hostvars[item].lan_ip}} {{item}}'
  when: hostvars[item].ansible_host is defined and hostvars[item].lan_ip is defined
  with_items: "{{groups.all_servers}}"

- name: 'static hosts {{item.hostname}}'
  lineinfile:
    path: /etc/hosts
    regex: '.*{{item.hostname}}$'
    line: '{{vars[item.ip_field]}} {{item.hostname}}'
  when: vars[item.ip_field] is defined
  with_items:
  - hostname: n.vpn.fwdev.top
    ip_field: nvpn_ip
  - hostname: n.vpn.fwdev.ltd
    ip_field: nvpn_ip
  - hostname: GH-MINIO
    ip_field: minio_ip
  - hostname: LOKI
    ip_field: loki_ip
  - hostname: ALERT
    ip_field: alert_ip
  - hostname: AYERS_GTS_API
    ip_field: ayers_gts_api_ip
  - hostname: LANPROXY
    ip_field: lanproxy_ip

- name: set timezone to Asia/Shanghai
  timezone:
    name: Asia/Shanghai
    hwclock: local

- name: set hostname
  hostname:
    name: '{{inventory_hostname}}'

- name: move exists supervisor log directory
  command:
    creates: /var/log/supervisor.bak
    cmd: mv /var/log/supervisor /var/log/supervisor.bak
  ignore_errors: true


- name: create application logs directory
  file:
    path: /data/logs
    state: directory
    mode: '755'
    owner: wukong
    group: wukong

- name: create data log directory
  file:
    path: /data/log
    state: directory
    mode: '755'
    owner: root
    group: root

- name: create supervisor data log directory
  file:
    path: /data/log/supervisor
    state: directory
    mode: '755'
    owner: root
    group: root

- name: create supervisor log symlink
  file:
    path: /var/log/supervisor
    src: /data/log/supervisor
    force: true
    state: link

- name: enable daily supervisor logrotate
  template:
    src: logrotate.d_supervisor.j2
    dest: /etc/logrotate.d/supervisor

- name: mkdir /data
  file:
    path: /data
    state: directory
    owner: wukong
    group: wukong
  ignore_errors: true
