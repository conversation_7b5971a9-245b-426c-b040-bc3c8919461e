{{ansible_managed|comment}}
http:
  middlewares:
    hkTd003LifecycleIpWhiteList:
      ipWhiteList:
        sourceRange:
        # TODO: add office ip
        - "127.0.0.1/32"
        - "*************"
        - "**************"
        - "**************"
        - "*************"
        - "***************"
  routers:
    lifecycle.igoldhorse.com:
      service: hkTd003LifecycleBackend
      rule: Host(`lifecycle.igoldhorse.com`)
      middlewares:
      - gzip
      - hkTd003LifecycleIpWhiteList
      tls:
        certResolver: httpResolver
        domains:
        - main: lifecycle.igoldhorse.com
  services:
    hkTd003LifecycleBackend:
      loadBalancer:
        servers:
        - url: http://HK-TD-003:35560
