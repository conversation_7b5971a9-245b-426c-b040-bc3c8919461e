# API性能监控配置 - 可以添加到主promtail配置中
# 支持多种日志格式的接口耗时监控

# 配置示例1: ResponseResultAdvice日志格式 - 提取request url和use时间
- job_name: response_result_advice_logs
  static_configs:
  - targets:
    - localhost
    labels:
      job: response-result-advice
      __path__: /var/log/application/*.log
  pipeline_stages:
  # 解析ResponseResultAdvice格式的API日志
  - regex:
      source: filename
      expression: /var/log/application/(?P<service>.*?)\.log
  - labels:
      service: service
  # 提取API路径和响应时间 - ResponseResultAdvice格式
  - match:
      selector: '{job="response-result-advice"} |~ "ResponseResultAdvice.*request url.*use:"'
      stages:
      # 提取request url中的API路径
      - regex:
          expression: '.*request url:\[<(?P<api_path>[^>]+)>\].*use: \[<(?P<response_time>\d+)>\]ms.*'
      - labels:
          api_path: api_path
      # 记录所有API响应时间
      - metrics:
          api_response_time_ms:
            type: Histogram
            description: API response time in milliseconds from ResponseResultAdvice
            source: response_time
            config:
              buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000, 30000]
      # 记录慢请求计数器 (>1000ms)
      - template:
          source: is_slow
          template: |
            {{- if gt (float64 .response_time) 1000.0 -}}
              1
            {{- else -}}
              0
            {{- end -}}
      - metrics:
          api_slow_requests_total:
            type: Counter
            description: Total number of slow API requests (>1s) from ResponseResultAdvice
            source: is_slow
            config:
              value: "1"
              action: inc

# 配置示例2: 标准格式 - "接口: /api/user/login 耗时: 1200ms"
- job_name: api_logs_standard
  static_configs:
  - targets:
    - localhost
    labels:
      job: api-logs
      __path__: /var/log/application/*api*.log
  pipeline_stages:
  # 解析标准格式的API日志
  - regex:
      source: filename
      expression: /var/log/application/(?P<service>.*?)\.log
  - labels:
      service: service
  # 提取接口路径和耗时 - 标准格式
  - match:
      selector: '{job="api-logs"}'
      stages:
      - regex:
          expression: '.*接口[:：]\s*(?P<api_path>[^\s]+).*耗时[:：]\s*(?P<response_time>\d+(?:\.\d+)?)\s*(?P<time_unit>ms|毫秒|s|秒).*'
      - template:
          source: response_time_ms
          template: |
            {{- if eq .time_unit "s" -}}
              {{ mul .response_time 1000 }}
            {{- else if eq .time_unit "秒" -}}
              {{ mul .response_time 1000 }}
            {{- else -}}
              {{ .response_time }}
            {{- end -}}
      - labels:
          api_path: api_path
          method: ""
      # 记录所有API响应时间
      - metrics:
          api_response_time_ms:
            type: Histogram
            description: API response time in milliseconds
            source: response_time_ms
            config:
              buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000, 30000]
      # 记录慢请求计数器 (>1000ms)
      - match:
          selector: '{job="api-logs"}'
          stages:
          - regex:
              expression: '.*'
          - template:
              source: is_slow
              template: |
                {{- if gt (float64 .response_time_ms) 1000.0 -}}
                  1
                {{- else -}}
                  0
                {{- end -}}
          - metrics:
              api_slow_requests_total:
                type: Counter
                description: Total number of slow API requests (>1s)
                source: is_slow
                config:
                  value: "1"
                  action: inc

# 配置示例2: JSON格式日志
- job_name: api_logs_json
  static_configs:
  - targets:
    - localhost
    labels:
      job: api-logs-json
      __path__: /var/log/application/*api*.json
  pipeline_stages:
  - json:
      expressions:
        api_path: api_path
        response_time: response_time
        method: method
        status_code: status_code
        timestamp: timestamp
  - labels:
      api_path: api_path
      method: method
      status_code: status_code
  # JSON格式的响应时间监控
  - metrics:
      api_response_time_json_ms:
        type: Histogram
        description: API response time from JSON logs
        source: response_time
        config:
          buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000, 30000]
  # JSON格式的慢请求监控
  - match:
      selector: '{job="api-logs-json"}'
      stages:
      - template:
          source: is_slow_json
          template: |
            {{- if gt (float64 .response_time) 1000.0 -}}
              1
            {{- else -}}
              0
            {{- end -}}
      - metrics:
          api_slow_requests_json_total:
            type: Counter
            description: Slow API requests from JSON logs
            source: is_slow_json
            config:
              value: "1"
              action: inc

# 配置示例3: Nginx访问日志格式
- job_name: nginx_api_logs
  static_configs:
  - targets:
    - localhost
    labels:
      job: nginx-api
      __path__: /var/log/nginx/access.log
  pipeline_stages:
  # 解析Nginx日志格式: IP - - [timestamp] "METHOD /path HTTP/1.1" status size "referer" "user-agent" response_time
  - regex:
      expression: '^(?P<remote_addr>[^\s]+) - - \[(?P<timestamp>[^\]]+)\] "(?P<method>\w+) (?P<api_path>[^\s]+) HTTP/[^"]*" (?P<status_code>\d+) (?P<body_bytes_sent>\d+) "[^"]*" "[^"]*" (?P<response_time>\d+(?:\.\d+)?)'
  - labels:
      api_path: api_path
      method: method
      status_code: status_code
  # 只监控API路径 (以/api开头)
  - match:
      selector: '{job="nginx-api"} |~ "\"\\w+ /api/"'
      stages:
      - template:
          source: response_time_ms
          template: "{{ mul .response_time 1000 }}"
      - metrics:
          nginx_api_response_time_ms:
            type: Histogram
            description: Nginx API response time
            source: response_time_ms
            config:
              buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000]
      # Nginx慢请求监控
      - template:
          source: is_nginx_slow
          template: |
            {{- if gt (float64 .response_time_ms) 1000.0 -}}
              1
            {{- else -}}
              0
            {{- end -}}
      - metrics:
          nginx_api_slow_requests_total:
            type: Counter
            description: Nginx slow API requests
            source: is_nginx_slow
            config:
              value: "1"
              action: inc

# 配置示例4: 自定义格式 - 最后一列是耗时
- job_name: custom_format_logs
  static_configs:
  - targets:
    - localhost
    labels:
      job: custom-api
      __path__: /var/log/custom/*.log
  pipeline_stages:
  # 假设日志格式: timestamp level service api_path other_info response_time_ms
  - regex:
      expression: '^(?P<timestamp>[^\s]+)\s+(?P<level>[^\s]+)\s+(?P<service>[^\s]+)\s+(?P<api_path>[^\s]+).*\s+(?P<response_time>\d+(?:\.\d+)?)(?:ms)?$'
  - labels:
      api_path: api_path
      service: service
      level: level
  # 自定义格式的监控
  - metrics:
      custom_api_response_time_ms:
        type: Histogram
        description: Custom format API response time
        source: response_time
        config:
          buckets: [10, 50, 100, 200, 500, 1000, 2000, 5000, 10000]
  # 自定义格式的慢请求
  - match:
      selector: '{job="custom-api"}'
      stages:
      - template:
          source: is_custom_slow
          template: |
            {{- if gt (float64 .response_time) 1000.0 -}}
              1
            {{- else -}}
              0
            {{- end -}}
      - metrics:
          custom_api_slow_requests_total:
            type: Counter
            description: Custom format slow requests
            source: is_custom_slow
            config:
              value: "1"
              action: inc
