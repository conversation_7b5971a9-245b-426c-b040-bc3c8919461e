{{ansible_managed|comment}}
http:
  routers:
    gqldmz-lpoa-root:
      rule: Host(`gqldmz.lpoa.igoldhorse.cn`) && Method(`GET`, `POST`) && Path(`/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      - requestAuthError
      - requestAuthDmz
      tls:
        certResolver: httpResolver
    gqldmz-lpoa-root-cors:
      rule: Host(`gqldmz.lpoa.igoldhorse.cn`) && Method(`OPTIONS`) && Path(`/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      tls:
        certResolver: httpResolver
    gqldmz-lpoa-auth:
      rule: Host(`gqldmz.lpoa.igoldhorse.cn`) && Method(`POST`) && Path(`/api/v1/gql/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      - requestAuthError
      - requestAuthDmz
      tls:
        certResolver: httpResolver
    gqldmz-lpoa-cors:
      rule: Host(`gqldmz.lpoa.igoldhorse.cn`) && Method(`OPTIONS`) && Path(`/api/v1/gql/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      - requestAuthError
      tls:
        certResolver: httpResolver
    gqldmz-lpoa-intranet:
      rule: Host(`gqldmz.lpoa.igoldhorse.cn`) && PathPrefix(`/-/ping/`)
      service: gqllpoa
      middlewares:
      - gzip
      - bodyLimit
      tls:
        certResolver: httpResolver