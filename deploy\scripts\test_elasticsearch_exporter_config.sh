#!/bin/bash

# 测试Elasticsearch Exporter配置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 测试elasticsearch_exporter命令行参数
test_exporter_args() {
    log_header "测试Elasticsearch Exporter参数"
    
    # 检查是否有elasticsearch_exporter二进制文件
    local hosts=("SZ-ES-001" "SZ-MONT-002" "HK-MG-002")
    
    for host in "${hosts[@]}"; do
        log_info "检查 $host 上的elasticsearch_exporter..."
        
        # 检查二进制文件是否存在
        local binary_exists=$(ansible -i hosts.ini "$host" -m shell -a "test -f /etc/elasticsearch_exporter/bin/elasticsearch_exporter && echo 'exists' || echo 'not found'" --become 2>/dev/null | grep -v "SUCCESS" || echo "not found")
        
        if [ "$binary_exists" = "exists" ]; then
            log_info "  ✓ 二进制文件存在"
            
            # 测试帮助信息
            log_info "  获取帮助信息..."
            ansible -i hosts.ini "$host" -m shell -a "/etc/elasticsearch_exporter/bin/elasticsearch_exporter --help" --become 2>/dev/null | grep -v "SUCCESS" | grep -A 5 -B 5 "es.uri" || echo "    无法获取帮助信息"
        else
            log_warn "  ⚠ 二进制文件不存在"
        fi
        
        echo ""
    done
}

# 测试单个ES节点连接
test_single_node_config() {
    log_header "测试单节点配置"
    
    local test_host="SZ-ES-001"
    local es_node="http://*************:9200"
    
    log_info "在 $test_host 上测试单节点配置..."
    
    # 创建临时配置文件
    local temp_service="/tmp/elasticsearch_exporter_test.service"
    
    cat > "$temp_service" << EOF
[Unit]
Description=Elasticsearch Exporter Test
After=network.target

[Service]
Type=simple
User=prometheus
Group=prometheus
ExecStart=/etc/elasticsearch_exporter/bin/elasticsearch_exporter --web.listen-address=0.0.0.0:9115 --es.uri=$es_node --es.timeout=5s --log.level=debug
Restart=no

[Install]
WantedBy=multi-user.target
EOF

    # 复制到目标主机
    ansible -i hosts.ini "$test_host" -m copy -a "src=$temp_service dest=/tmp/elasticsearch_exporter_test.service" --become
    
    # 安装并启动测试服务
    ansible -i hosts.ini "$test_host" -m shell -a "systemctl daemon-reload" --become
    ansible -i hosts.ini "$test_host" -m shell -a "systemctl start elasticsearch_exporter_test" --become
    
    # 等待启动
    sleep 5
    
    # 检查状态
    local test_status=$(ansible -i hosts.ini "$test_host" -m shell -a "systemctl is-active elasticsearch_exporter_test" --become 2>/dev/null | grep -v "SUCCESS" || echo "inactive")
    
    if [ "$test_status" = "active" ]; then
        log_info "  ✓ 单节点测试服务启动成功"
        
        # 测试指标端点
        local metrics_test=$(ansible -i hosts.ini "$test_host" -m uri -a "url=http://localhost:9115/metrics timeout=5" 2>/dev/null | grep -c "status.*200" || echo "0")
        
        if [ "$metrics_test" -gt 0 ]; then
            log_info "  ✓ 指标端点正常"
        else
            log_warn "  ⚠ 指标端点异常"
        fi
    else
        log_error "  ✗ 单节点测试服务启动失败"
        
        # 显示错误日志
        ansible -i hosts.ini "$test_host" -m shell -a "journalctl -u elasticsearch_exporter_test --no-pager -n 10" --become 2>/dev/null | grep -v "SUCCESS"
    fi
    
    # 清理测试服务
    ansible -i hosts.ini "$test_host" -m shell -a "systemctl stop elasticsearch_exporter_test" --become || true
    ansible -i hosts.ini "$test_host" -m shell -a "systemctl disable elasticsearch_exporter_test" --become || true
    ansible -i hosts.ini "$test_host" -m file -a "path=/tmp/elasticsearch_exporter_test.service state=absent" --become
    
    # 清理本地临时文件
    rm -f "$temp_service"
}

# 测试多节点配置语法
test_multi_node_syntax() {
    log_header "测试多节点配置语法"
    
    log_info "测试不同的多节点URI格式..."
    
    # 方法1: 逗号分隔的URI
    echo "方法1: 逗号分隔"
    echo "  --es.uri=http://*************:9200,http://*************:9200,http://*************:9200"
    
    # 方法2: 多个--es.uri参数（可能不支持）
    echo "方法2: 多个参数"
    echo "  --es.uri=http://*************:9200 --es.uri=http://*************:9200 --es.uri=http://*************:9200"
    
    # 方法3: 单个节点（推荐用于生产）
    echo "方法3: 单个节点（推荐）"
    echo "  --es.uri=http://*************:9200"
    
    echo ""
    log_info "根据elasticsearch_exporter文档，推荐使用单个节点URI"
    log_info "exporter会自动发现集群中的其他节点"
}

# 生成推荐配置
generate_recommended_config() {
    log_header "生成推荐配置"
    
    local config_file="/tmp/elasticsearch_exporter_recommended.service"
    
    cat > "$config_file" << 'EOF'
[Unit]
Description=Elasticsearch Exporter
Documentation=https://github.com/prometheus-community/elasticsearch_exporter
Wants=network-online.target
After=network-online.target

[Service]
Type=simple
User=prometheus
Group=prometheus
ExecStart=/etc/elasticsearch_exporter/bin/elasticsearch_exporter --web.listen-address=0.0.0.0:9114 --web.telemetry-path=/metrics --es.uri=http://*************:9200 --es.all=true --es.indices=true --es.indices_settings=true --es.shards=true --es.snapshots=true --es.timeout=5s --log.level=info --log.format=logfmt
SyslogIdentifier=elasticsearch_exporter
Restart=always
RestartSec=1
StartLimitInterval=0

PrivateTmp=yes
ProtectHome=yes
NoNewPrivileges=yes
ProtectSystem=strict
ProtectControlGroups=true
ProtectKernelModules=true
ProtectKernelTunables=yes

[Install]
WantedBy=multi-user.target
EOF

    log_info "推荐的服务配置已生成: $config_file"
    echo ""
    echo "推荐配置特点:"
    echo "1. 使用单个ES节点URI (exporter会自动发现其他节点)"
    echo "2. 启用所有监控选项"
    echo "3. 设置合理的超时时间"
    echo "4. 使用systemd安全特性"
    echo ""
    
    cat "$config_file"
    
    echo ""
    echo "要应用此配置，请运行:"
    echo "  ansible -i hosts.ini 'SZ-ES-001,SZ-MONT-002,HK-MG-002' -m copy -a 'src=$config_file dest=/etc/systemd/system/elasticsearch_exporter.service' --become"
    echo "  ansible -i hosts.ini 'SZ-ES-001,SZ-MONT-002,HK-MG-002' -m systemd -a 'daemon_reload=yes' --become"
    echo "  ansible -i hosts.ini 'SZ-ES-001,SZ-MONT-002,HK-MG-002' -m systemd -a 'name=elasticsearch_exporter state=restarted enabled=yes' --become"
}

# 检查ES集群状态
check_es_cluster() {
    log_header "检查Elasticsearch集群状态"
    
    local es_nodes=("*************:9200" "*************:9200" "*************:9200")
    local healthy_nodes=0
    
    for node in "${es_nodes[@]}"; do
        log_info "检查 $node..."
        
        if curl -s --connect-timeout 5 "http://$node" > /dev/null; then
            log_info "  ✓ 节点可达"
            ((healthy_nodes++))
            
            # 获取节点信息
            local node_info=$(curl -s "http://$node" 2>/dev/null)
            local node_name=$(echo "$node_info" | jq -r '.name' 2>/dev/null || echo "unknown")
            echo "    节点名: $node_name"
            
            # 获取集群健康状态
            local health=$(curl -s "http://$node/_cluster/health" 2>/dev/null)
            local status=$(echo "$health" | jq -r '.status' 2>/dev/null || echo "unknown")
            local nodes_count=$(echo "$health" | jq -r '.number_of_nodes' 2>/dev/null || echo "unknown")
            
            echo "    集群状态: $status"
            echo "    集群节点数: $nodes_count"
        else
            log_warn "  ⚠ 节点不可达"
        fi
        echo ""
    done
    
    log_info "健康节点数: $healthy_nodes/3"
    
    if [ "$healthy_nodes" -eq 3 ]; then
        log_info "✓ 所有ES节点都正常"
    elif [ "$healthy_nodes" -gt 0 ]; then
        log_warn "⚠ 部分ES节点异常"
    else
        log_error "✗ 所有ES节点都不可达"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "Elasticsearch Exporter配置测试"
    echo "测试时间: $(date)"
    echo "========================================"
    echo ""
    
    # 检查前置条件
    if [ ! -f "hosts.ini" ]; then
        log_error "hosts.ini 文件不存在"
        exit 1
    fi
    
    if ! command -v ansible &> /dev/null; then
        log_error "ansible 未安装"
        exit 1
    fi
    
    # 执行测试
    check_es_cluster
    test_exporter_args
    test_multi_node_syntax
    test_single_node_config
    generate_recommended_config
    
    log_info "配置测试完成！"
    echo ""
    echo "建议:"
    echo "1. 使用单个ES节点URI，让exporter自动发现其他节点"
    echo "2. 确保所有ES节点都可达"
    echo "3. 使用推荐的服务配置"
    echo "4. 监控exporter日志以确保正常运行"
}

# 执行主函数
main "$@"
