- name: configure minio
  template:
    src: minio.j2
    dest: /etc/default/minio
    force: true
    owner: root
    group: minio
    mode: '640'
  register: config_minio
  notify:
  - restart minio

- name: start minio
  systemd:
    name: minio
    state: started
  when: config_minio.changed

- name: wait for minio started
  wait_for:
    port: '{{MINIO_PORT}}'

- name: set mc admin info
  shell:
    cmd: |
      mc alias set server http://127.0.0.1:{{MINIO_PORT}} {{MINIO_ROOT_USER}} {{MINIO_ROOT_PASSWORD}}
      mc admin info server

- name: create minio userpolicy
  template:
    src: "policy_{{item.policy}}.json.j2"
    dest: /tmp/policy_{{item.policy}}_{{item.user}}.json
  with_items:
  - "{{minio_users[ansible_hostname]}}"

- name: performance per host task
  include_tasks: '{{ansible_hostname}}_access_policy.yml'
