{{ansible_managed|comment}}
http:
  routers:
    # Dmz
    gqlDmz_core.trade.igoldhorse.cn:
      rule: Host(`core.trade.igoldhorse.cn`) && Method(`POST`) && Path(`/dmz/api/v1/gql/`)
      service: gqlDmzTh
      middlewares:
      - gzip
      - requestAuthError
      - requestAuthDmz
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    gqlDmz_core.trade.igoldhorse.cn_cors:
      rule: Host(`core.trade.igoldhorse.cn`) && Method(`OPTIONS`) && Path(`/dmz/api/v1/gql/`)
      service: gqlDmzTh
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    gqlDmz_core.trade.igoldhorse.cn_ping:
      rule: Host(`core.trade.igoldhorse.cn`) && Method(`GET`) && Path(`/dmz/-/ping/`)
      service: gqlDmzTh
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    # Secret
    gqlSecret_core.trade.igoldhorse.cn:
      rule: Host(`core.trade.igoldhorse.cn`) && Method(`POST`) && Path(`/secret/api/v1/gql/`)
      service: gqlSecretTh
      middlewares:
      - gzip
      - requestAuthError
      - requestAuthSecret
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    gqlSecret_core.trade.igoldhorse.cn_cors:
      rule: Host(`core.trade.igoldhorse.cn`) && Method(`OPTIONS`) && Path(`/secret/api/v1/gql/`)
      service: gqlSecretTh
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
    gqlSecret_core.trade.igoldhorse.cn_ping:
      rule: Host(`core.trade.igoldhorse.cn`) && Method(`GET`) && Path(`/secret/-/ping/`)
      service: gqlSecretTh
      middlewares:
      - gzip
      - bodyLimit
      - stripGqlPathPrefix
      - gqlRateLimit
      tls:
        certResolver: httpResolver
