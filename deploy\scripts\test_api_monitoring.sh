#!/bin/bash

# API监控测试脚本
# 用法: ./test_api_monitoring.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[====]${NC} $1"
}

# 生成测试日志
generate_test_logs() {
    local log_file="/tmp/api_test.log"
    local json_log_file="/tmp/api_test.json"
    
    log_info "生成测试日志..."
    
    # 生成标准格式测试日志
    cat > "$log_file" << EOF
2024-01-15 10:00:01 INFO 接口: /api/user/login 耗时: 150ms 状态: 200
2024-01-15 10:00:02 INFO 接口: /api/user/profile 耗时: 80ms 状态: 200
2024-01-15 10:00:03 WARN 接口: /api/order/create 耗时: 1200ms 状态: 200
2024-01-15 10:00:04 INFO 接口: /api/product/list 耗时: 300ms 状态: 200
2024-01-15 10:00:05 ERROR 接口: /api/payment/process 耗时: 2500ms 状态: 500
2024-01-15 10:00:06 INFO 接口: /api/user/logout 耗时: 50ms 状态: 200
2024-01-15 10:00:07 WARN 接口: /api/search/query 耗时: 1800ms 状态: 200
2024-01-15 10:00:08 INFO 接口: /api/user/settings 耗时: 120ms 状态: 200
2024-01-15 10:00:09 WARN 接口: /api/report/generate 耗时: 3000ms 状态: 200
2024-01-15 10:00:10 INFO 接口: /api/health/check 耗时: 10ms 状态: 200
EOF

    # 生成JSON格式测试日志
    cat > "$json_log_file" << EOF
{"timestamp":"2024-01-15T10:00:01Z","level":"INFO","api_path":"/api/user/login","method":"POST","response_time":150,"status_code":200}
{"timestamp":"2024-01-15T10:00:02Z","level":"INFO","api_path":"/api/user/profile","method":"GET","response_time":80,"status_code":200}
{"timestamp":"2024-01-15T10:00:03Z","level":"WARN","api_path":"/api/order/create","method":"POST","response_time":1200,"status_code":200}
{"timestamp":"2024-01-15T10:00:04Z","level":"INFO","api_path":"/api/product/list","method":"GET","response_time":300,"status_code":200}
{"timestamp":"2024-01-15T10:00:05Z","level":"ERROR","api_path":"/api/payment/process","method":"POST","response_time":2500,"status_code":500}
{"timestamp":"2024-01-15T10:00:06Z","level":"INFO","api_path":"/api/user/logout","method":"POST","response_time":50,"status_code":200}
{"timestamp":"2024-01-15T10:00:07Z","level":"WARN","api_path":"/api/search/query","method":"GET","response_time":1800,"status_code":200}
{"timestamp":"2024-01-15T10:00:08Z","level":"INFO","api_path":"/api/user/settings","method":"GET","response_time":120,"status_code":200}
{"timestamp":"2024-01-15T10:00:09Z","level":"WARN","api_path":"/api/report/generate","method":"POST","response_time":3000,"status_code":200}
{"timestamp":"2024-01-15T10:00:10Z","level":"INFO","api_path":"/api/health/check","method":"GET","response_time":10,"status_code":200}
EOF

    echo "$log_file"
    echo "$json_log_file"
}

# 测试正则表达式
test_regex_patterns() {
    local log_file="$1"
    
    log_header "测试正则表达式匹配"
    
    # 测试标准格式正则
    local standard_regex='.*接口[:：]\s*(?P<api_path>[^\s]+).*耗时[:：]\s*(?P<response_time>\d+(?:\.\d+)?)\s*(?P<time_unit>ms|毫秒|s|秒).*'
    
    log_info "测试标准格式正则表达式..."
    
    while IFS= read -r line; do
        if echo "$line" | grep -qE '接口[:：].*耗时[:：]'; then
            local api_path=$(echo "$line" | sed -n 's/.*接口[:：]\s*\([^\s]*\).*/\1/p')
            local response_time=$(echo "$line" | sed -n 's/.*耗时[:：]\s*\([0-9.]*\).*/\1/p')
            local time_unit=$(echo "$line" | sed -n 's/.*耗时[:：]\s*[0-9.]*\s*\([a-zA-Z毫秒]*\).*/\1/p')
            
            echo "  API: $api_path, 耗时: $response_time$time_unit"
            
            # 检查是否为慢请求
            if [ "$time_unit" = "ms" ] || [ "$time_unit" = "毫秒" ]; then
                if (( $(echo "$response_time > 1000" | bc -l) )); then
                    echo "    ⚠ 慢请求检测: 是"
                else
                    echo "    ✓ 慢请求检测: 否"
                fi
            elif [ "$time_unit" = "s" ] || [ "$time_unit" = "秒" ]; then
                if (( $(echo "$response_time > 1" | bc -l) )); then
                    echo "    ⚠ 慢请求检测: 是"
                else
                    echo "    ✓ 慢请求检测: 否"
                fi
            fi
        fi
    done < "$log_file"
}

# 测试JSON解析
test_json_parsing() {
    local json_file="$1"
    
    log_header "测试JSON日志解析"
    
    while IFS= read -r line; do
        if command -v jq &> /dev/null; then
            local api_path=$(echo "$line" | jq -r '.api_path')
            local response_time=$(echo "$line" | jq -r '.response_time')
            local method=$(echo "$line" | jq -r '.method')
            local status_code=$(echo "$line" | jq -r '.status_code')
            
            echo "  API: $method $api_path, 耗时: ${response_time}ms, 状态: $status_code"
            
            # 检查是否为慢请求
            if (( $(echo "$response_time > 1000" | bc -l) )); then
                echo "    ⚠ 慢请求检测: 是"
            else
                echo "    ✓ 慢请求检测: 否"
            fi
        else
            log_warn "jq 未安装，跳过JSON解析测试"
            break
        fi
    done < "$json_file"
}

# 检查Promtail指标
check_promtail_metrics() {
    log_header "检查Promtail指标"
    
    local promtail_hosts=("localhost:19080" "SZ-ES-001:19080" "SZ-MONT-002:19080")
    
    for host in "${promtail_hosts[@]}"; do
        log_info "检查 $host..."
        
        if curl -s --connect-timeout 5 "http://$host/metrics" > /dev/null 2>&1; then
            local metrics=$(curl -s "http://$host/metrics")
            
            # 检查API相关指标
            if echo "$metrics" | grep -q "api_response_time_ms"; then
                log_info "  ✓ 发现 api_response_time_ms 指标"
                local count=$(echo "$metrics" | grep "api_response_time_ms_count" | wc -l)
                echo "    指标数量: $count"
            else
                log_warn "  ⚠ 未发现 api_response_time_ms 指标"
            fi
            
            if echo "$metrics" | grep -q "api_slow_requests_total"; then
                log_info "  ✓ 发现 api_slow_requests_total 指标"
                local slow_count=$(echo "$metrics" | grep "api_slow_requests_total" | tail -1 | awk '{print $2}')
                echo "    慢请求计数: $slow_count"
            else
                log_warn "  ⚠ 未发现 api_slow_requests_total 指标"
            fi
        else
            log_warn "  ✗ 无法连接到 $host"
        fi
        echo ""
    done
}

# 检查Prometheus告警规则
check_prometheus_rules() {
    log_header "检查Prometheus告警规则"
    
    local prometheus_hosts=("localhost:9090" "SZ-ES-001:9090" "SZ-MONT-002:9090")
    
    for host in "${prometheus_hosts[@]}"; do
        log_info "检查 $host..."
        
        if curl -s --connect-timeout 5 "http://$host/api/v1/rules" > /dev/null 2>&1; then
            local rules=$(curl -s "http://$host/api/v1/rules")
            
            # 检查API性能相关规则
            if echo "$rules" | grep -q "SlowAPIRequests"; then
                log_info "  ✓ 发现 SlowAPIRequests 告警规则"
            else
                log_warn "  ⚠ 未发现 SlowAPIRequests 告警规则"
            fi
            
            if echo "$rules" | grep -q "HighAPIResponseTimeP95"; then
                log_info "  ✓ 发现 HighAPIResponseTimeP95 告警规则"
            else
                log_warn "  ⚠ 未发现 HighAPIResponseTimeP95 告警规则"
            fi
            
            # 检查活跃告警
            local alerts=$(curl -s "http://$host/api/v1/alerts")
            if echo "$alerts" | grep -q "SlowAPIRequests"; then
                log_warn "  ⚠ 检测到活跃的慢API告警"
            else
                log_info "  ✓ 无活跃的慢API告警"
            fi
        else
            log_warn "  ✗ 无法连接到 $host"
        fi
        echo ""
    done
}

# 生成监控报告
generate_monitoring_report() {
    log_header "生成监控报告"
    
    local report_file="/tmp/api_monitoring_report.txt"
    
    cat > "$report_file" << EOF
API性能监控测试报告
生成时间: $(date)

测试项目:
1. 正则表达式匹配测试 - 验证日志解析是否正确
2. JSON解析测试 - 验证JSON格式日志处理
3. Promtail指标检查 - 确认指标正常采集
4. Prometheus规则检查 - 验证告警规则配置

监控指标:
- api_response_time_ms: API响应时间直方图
- api_slow_requests_total: 慢请求计数器 (>1000ms)

告警规则:
- SlowAPIRequests: 检测到慢请求时触发
- HighSlowAPIRequestRate: 慢请求频率过高时触发
- HighAPIResponseTimeP95: P95响应时间过高时触发
- HighAPIResponseTimeP99: P99响应时间过高时触发

配置文件位置:
- Promtail配置: /etc/promtail/promtail.yml
- Prometheus规则: /etc/prometheus/rule.d/api_performance.rule.yml

使用说明:
1. 确保日志格式正确匹配配置的正则表达式
2. 监控慢请求阈值默认为1000ms，可根据需要调整
3. 告警规则可以根据业务需求进行自定义
4. 建议定期检查监控指标和告警状态

EOF

    log_info "监控报告已生成: $report_file"
    cat "$report_file"
}

# 主函数
main() {
    echo "========================================"
    echo "API性能监控测试"
    echo "测试时间: $(date)"
    echo "========================================"
    echo ""
    
    # 检查依赖工具
    local missing_tools=()
    for tool in curl bc; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_info "请安装缺少的工具后重试"
        exit 1
    fi
    
    # 生成测试日志
    local test_logs=($(generate_test_logs))
    local standard_log="${test_logs[0]}"
    local json_log="${test_logs[1]}"
    
    # 执行测试
    test_regex_patterns "$standard_log"
    echo ""
    
    test_json_parsing "$json_log"
    echo ""
    
    check_promtail_metrics
    check_prometheus_rules
    generate_monitoring_report
    
    # 清理测试文件
    rm -f "$standard_log" "$json_log"
    
    log_info "API监控测试完成！"
}

# 执行主函数
main "$@"
