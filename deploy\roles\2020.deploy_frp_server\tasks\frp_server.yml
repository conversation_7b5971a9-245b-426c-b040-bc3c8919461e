- name: create frp group
  group:
    name: frp
    gid: 4347

- name: create frp user
  user:
    name: frp
    uid: 4347

- name: create frp config dir
  file:
    path: "{{FRP_CONFIG_DIR}}"
    state: directory
    owner: frp
    group: frp

- name: frp download url
  debug:
    msg: "{{FRP_DOWNLOAD_URL}}"

- name: download frp package
  get_url:
    use_proxy: '{{USE_PROXY|default(true)}}'
    url: "{{FRP_DOWNLOAD_URL}}"
    dest: "/tmp/{{FRP_FILENAME}}"
  register: _download_result
  until: _download_result is succeeded
  retries: 5
  delay: 5

- name: unarchive
  unarchive:
    src: "/tmp/{{FRP_FILENAME}}"
    dest: "/tmp"
    remote_src: true

- name: copy binaries
  copy:
    src: "/tmp/{{FRP_FILENAME|splitext|first|splitext|first}}/{{item}}"
    dest: /usr/local/bin
    remote_src: true
    mode: '755'
    force: true
  with_items:
  - frpc
  - frps
  notify:
  - restart frps

- name: copy config and service
  template:
    src: "{{item.src}}"
    dest: "{{item.dest}}"
    owner: frp
    group: frp
  with_items:
  - src: frps.ini.j2
    dest: /etc/frp/frps.ini
  - src: frps.service.j2
    dest: /etc/systemd/system/frps.service
  notify:
  - restart frps

- name: ensure frps on boot
  systemd:
    name: frps
    daemon_reload: true
    enabled: true
