---
- name: deploy elasticsearch exporter
  hosts: "{{DEPLOY_ELASTICSEARCH_EXPORTER_HOSTS|default('SZ-ES-001')}}"
  environment:
    HTTP_PROXY: http://LANPROXY:8818
    HTTPS_PROXY: http://LANPROXY:8818
  vars:
    # Override default ES URI for each monitoring server
    elasticsearch_exporter_es_uri: "http://*************:9200,http://*************:9200,http://*************:9200"
    elasticsearch_exporter_multi_node: true
    elasticsearch_exporter_cluster_config: true
  roles:
  - 2029.deploy_elasticsearch_exporter
  - 2029.config_elasticsearch_exporter
