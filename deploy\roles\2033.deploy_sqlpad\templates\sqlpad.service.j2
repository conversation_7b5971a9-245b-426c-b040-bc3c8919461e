{{ ansible_managed | comment }}

[Unit]
Description=SQLPad Web based SQL Editor
Documentation=https://github.com/rickbergfalk/sqlpad
Wants=network-online.target
After=network-online.target
[Service]
WorkingDirectory=/opt/sqlpad/server
Type=simple
User=sqlpad
Group=sqlpad
ExecReload=/bin/kill -HUP $MAINPID
#ExecStart=/usr/local/bin/sqlpad --dbPath /data/sqlpad/db --ip 0.0.0.0 --port 8838 --debug --baseUrl /sqlpad --admin <EMAIL> --passphrase {{passphrase}} --allowCsvDownload false
ExecStart=node server.js --config ./config.dev.env
SyslogIdentifier=sqlpad
Restart=always
[Install]
WantedBy=multi-user.target
