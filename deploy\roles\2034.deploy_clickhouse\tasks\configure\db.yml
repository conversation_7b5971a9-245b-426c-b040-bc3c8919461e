---
- name: Gather list of existing databases
  command: "clickhouse-client -h 127.0.0.1 --port {{ clickhouse_tcp_port }} -u default --password 123 -q 'show databases'"
  changed_when: False
  register: existing_databases
  tags: [config_db]

- name: Config | Delete database config
  command: |
    clickhouse-client -h 127.0.0.1 --port {{ clickhouse_tcp_port }} -u default --password 123
    -q 'DROP DATABASE IF EXISTS `{{ item.name }}`
    {% if item.cluster is defined %}ON CLUSTER `{{ item.cluster }}`{% endif %}'
  with_items: "{{ clickhouse_dbs }}"
  when: item.state is defined and item.state == 'absent' and item.name in existing_databases.stdout_lines
  tags: [config_db]

- name: Config | Create database config
  command: |
    clickhouse-client -h 127.0.0.1 --port {{ clickhouse_tcp_port }} -u default --password 123
    -q 'CREATE DATABASE IF NOT EXISTS `{{ item.name }}`
    {% if item.cluster is defined %}ON CLUSTER `{{ item.cluster }}`{% endif %}
    {% if item.engine is defined %}ENGINE = {{ item.engine }}{% endif %}'
  with_items: "{{ clickhouse_dbs }}"
  when: (item.state is undefined or item.state == 'present') and item.name not in existing_databases.stdout_lines
  tags: [config_db]
