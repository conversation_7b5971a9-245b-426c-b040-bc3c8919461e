{{ ansible_managed | comment }}

[Unit]
Description=Alertmanager
After=network-online.target

[Service]
Type=simple
User=alertmanager
Group=alertmanager
ExecReload=/bin/kill -HUP $MAINPID
ExecStart=/usr/local/bin/alertmanager \
  --storage.path={{ALERTMANAGER_DB_DIR}} \
  --config.file={{ALERTMANAGER_CONFIG_DIR}}/alertmanager.yml \
    --web.listen-address=:{{ alertmanager_port }} \
  --cluster.listen-address="{{hostvars[inventory_hostname]['lan_ip']}}:{{alertmanager_mesh_port}}" \
  --cluster.peer="{{groups.alertmanager[0]}}:{{alertmanager_mesh_port}}"
LimitNOFILE=65000
Restart=always

[Install]
WantedBy=multi-user.target
